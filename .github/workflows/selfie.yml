name: Make Everything Selfie

on:
  push:
    paths-ignore:
    - '**.md'
    - '**.txt'
    - 'docs'
    - 'AUTHORS'
    - 'LICENSE'
  pull_request:
    paths-ignore:
    - '**.md'
    - '**.txt'
    - 'docs'
    - 'AUTHORS'
    - 'LICENSE'
  schedule:
    # trigger fridays at 12am
    - cron: '0 0 * * 5'
  workflow_dispatch:
    inputs:
      oslinux:
        description: 'Make all on Linux'
        type: boolean
        required: true
        default: true
      osmacos:
        description: 'Make all on macOS'
        type: boolean
        required: true
        default: true
      oswindows:
        description: 'Make all on Windows'
        type: boolean
        required: true
        default: true
      rundocker:
        description: 'Make everything on docker'
        type: boolean
        required: true
        default: true

jobs:
  make-all-on-linux:
    name: Make all of selfie on Linux
    runs-on: ubuntu-latest
    if: ${{ ((github.event_name != 'workflow_dispatch') || (github.event.inputs.oslinux == 'true')) && (((!github.event.repository.private) || ((github.ref == 'refs/heads/main') && (github.event_name != 'schedule'))) || (github.event_name == 'workflow_dispatch')) }}

    steps:
    - name: Checkout selfie
      uses: actions/checkout@v4
    - name: Make all
      run: make -j -O all
    - name: Use Python 3.12.x
      uses: actions/setup-python@v5
      with:
        python-version: "3.12.x"
    - name: Run autograder baseline
      run: make grade

  make-all-on-macos:
    name: Make all of selfie on macOS
    runs-on: macos-latest
    if: ${{ ((github.event_name != 'workflow_dispatch') || (github.event.inputs.osmacos == 'true')) && ((!github.event.repository.private) || (github.event_name == 'workflow_dispatch')) }}

    steps:
    - name: Checkout selfie
      uses: actions/checkout@v4
    - name: Make all
      run: make -j all
    - name: Use Python 3.12.x
      uses: actions/setup-python@v5
      with:
        python-version: "3.12.x"
    - name: Run autograder baseline
      run: make grade

  make-all-on-windows:
    name: Make all of selfie on Windows
    runs-on: windows-latest
    if: ${{ ((github.event_name != 'workflow_dispatch') || (github.event.inputs.oswindows == 'true')) && ((!github.event.repository.private) || (github.event_name == 'workflow_dispatch')) }}

    steps:
    - name: Checkout selfie
      uses: actions/checkout@v4
    - name: Use gcc from cygwin for LP64 data model (not LLP64 with mingw)
      run: |
        choco upgrade cygwin -y --no-progress
        C:\tools\cygwin\cygwinsetup.exe -q -d -N -R C:\tools\cygwin -l C:\tools\cygwin\packages -P gcc-core,make | Out-Default
        echo 'C:\tools\cygwin\bin' >> $env:GITHUB_PATH
    - name: Make all
      run: make -j -O all
    # TODO: python3 does not work here
