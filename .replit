# The command that is executed when the run button is clicked.
run = "./selfie"

compile = "make"

# The default file opened in the editor.
entrypoint = "selfie.c"

# Setting environment variables
# [env]
# FOO="foo"

# Packager configuration for the Universal Package Manager
# See https://github.com/replit/upm for supported languages.
[packager]
language = "c"

  [packager.features]
  # Enables the package search sidebar
  packageSearch = true
  # Enabled package guessing
  guessImports = false

# Per language configuration: language.<lang name>
[languages.c]
# The glob pattern to match files for this programming language
pattern = "**/*.{c,h}"

    # LSP configuration for code intelligence
    [languages.c.languageServer]
    start = ["ccls"]

[nix]
channel = "stable-21_11"