# ASCII table

[ASCII](https://en.wikipedia.org/wiki/ASCII) abbreviated from *American Standard Code for Information Interchange*, is a character encoding standard for electronic communication. ASCII codes represent text in computers, telecommunications equipment, and other devices. Most modern character-encoding schemes are based on ASCII, although they support many additional characters. ASCII is used to map characters to 7 bit wide integers and vice versa.

## Control Characters

The first 32 characters cannot be printed and are used purely for control.

| Decimal | Octal | Hexadecimal |  Binary  | Symbol |       Description        |
| :-----: | :---: | :---------: | :------: | :----: | :----------------------: |
|    0    |  000  |     00      | 00000000 |  NUL   |        Null Char         |
|    1    |  001  |     01      | 00000001 |  SOH   |     Start of Heading     |
|    2    |  002  |     02      | 00000010 |  STX   |      Start of Text       |
|    3    |  003  |     03      | 00000011 |  ETX   |       End of Text        |
|    4    |  004  |     04      | 00000100 |  EOT   |   End of Transmission    |
|    5    |  005  |     05      | 00000101 |  ENQ   |         Enquiry          |
|    6    |  006  |     06      | 00000110 |  ACK   |     Acknowledgement      |
|    7    |  007  |     07      | 00000111 |  BEL   |           Bell           |
|    8    |  010  |     08      | 00001000 |   BS   |        Backspace         |
|    9    |  011  |     09      | 00001001 |   HT   |      Horizontal Tab      |
|   10    |  012  |     0A      | 00001010 |   LF   |   New Line (Line Feed)   |
|   11    |  013  |     0B      | 00001011 |   VT   |       Vertical Tab       |
|   12    |  014  |     0C      | 00001100 |   FF   |        Form Feed         |
|   13    |  015  |     0D      | 00001101 |   CR   |     Carriage Return      |
|   14    |  016  |     0E      | 00001110 |   SO   |        Shift Out         |
|   15    |  017  |     0F      | 00001111 |   SI   |         Shift In         |
|   16    |  020  |     10      | 00010000 |  DLE   |     Data Line Escape     |
|   17    |  021  |     11      | 00010001 |  DC1   |  Device Control 1 (XON)  |
|   18    |  022  |     12      | 00010010 |  DC2   |     Device Control 2     |
|   19    |  023  |     13      | 00010011 |  DC3   | Device Control 3 (XOFF)  |
|   20    |  024  |     14      | 00010100 |  DC4   |     Device Control 4     |
|   21    |  025  |     15      | 00010101 |  NAK   | Negative Acknowledgement |
|   22    |  026  |     16      | 00010110 |  SYN   |     Synchronous Idle     |
|   23    |  027  |     17      | 00010111 |  ETB   |  End of Transmint Block  |
|   24    |  030  |     18      | 00011000 |  CAN   |          Cancel          |
|   25    |  031  |     19      | 00011001 |   EM   |      End Of Medium       |
|   26    |  032  |     1A      | 00011010 |  SUB   |        Substitute        |
|   27    |  033  |     1B      | 00011011 |  ESC   |          Escape          |
|   28    |  034  |     1C      | 00011100 |   FS   |      File Seperator      |
|   29    |  035  |     1D      | 00011101 |   GS   |     Group Seperator      |
|   30    |  036  |     1E      | 00011110 |   RS   |     Record Seperator     |
|   31    |  037  |     1F      | 00011111 |   US   |      Unit Seperator      |

## Printable Characters

The codes 32 - 127 are all the printable characters. You will find almost every one of them on your keyboard.

| Decimal | Octal | Hexadecimal |  Binary  | Symbol |             Description              |
| :-----: | :---: | :---------: | :------: | :----: | :----------------------------------: |
|   32    |  040  |     20      | 00100000 | &nbsp; |                Space                 |
|   33    |  041  |     21      | 00100001 |   !    |           Exclamation Mark           |
|   34    |  042  |     22      | 00100010 |   "    |     Double Quote or Spheech Mark     |
|   35    |  043  |     23      | 00100011 |   #    |        Hash, Number or Sharp         |
|   36    |  044  |     24      | 00100100 |   $    |                Dollar                |
|   37    |  045  |     25      | 00100101 |   %    |          Percent or Modulus          |
|   38    |  046  |     26      | 00100110 |   &    |              Ampersand               |
|   39    |  047  |     27      | 00100111 |   '    |             Single Quote             |
|   40    |  050  |     28      | 00101000 |   (    |           Open Parenthesis           |
|   41    |  051  |     29      | 00101001 |   )    |          Close Parenthesis           |
|   42    |  052  |     2A      | 00101010 |   *    |         Asterisk or Multiply         |
|   43    |  053  |     2B      | 00101011 |   +    |                 Plus                 |
|   44    |  054  |     2C      | 00101100 |   ,    |                Comma                 |
|   45    |  055  |     2D      | 00101101 |   -    |          Hyphen or Subtract          |
|   46    |  056  |     2E      | 00101110 |   .    |       Period, Full Stop or Dot       |
|   47    |  057  |     2F      | 00101111 |   /    |       Forward Slash or Divide        |
|   48    |  060  |     30      | 00110000 |   0    |                 Zero                 |
|   49    |  061  |     31      | 00110001 |   1    |                 One                  |
|   50    |  062  |     32      | 00110010 |   2    |                 Two                  |
|   51    |  063  |     33      | 00110011 |   3    |                Three                 |
|   52    |  064  |     34      | 00110100 |   4    |                 Four                 |
|   53    |  065  |     35      | 00110101 |   5    |                 Five                 |
|   54    |  066  |     36      | 00110110 |   6    |                 Six                  |
|   55    |  067  |     37      | 00110111 |   7    |                Seven                 |
|   56    |  070  |     38      | 00111000 |   8    |                Eight                 |
|   57    |  071  |     39      | 00111001 |   9    |                 Nine                 |
|   58    |  072  |     3A      | 00111010 |   :    |                Colon                 |
|   59    |  073  |     3B      | 00111011 |   ;    |              Semicolon               |
|   60    |  074  |     3C      | 00111100 |   <    |   Less Than or Open Angled Bracket   |
|   61    |  075  |     3D      | 00111101 |   =    |                Equals                |
|   62    |  076  |     3E      | 00111110 |   >    | Greater Than or Close Angled Bracket |
|   63    |  077  |     3F      | 00111111 |   ?    |            Question Mark             |
|   64    |  100  |     40      | 01000000 |   @    |              At Symbol               |
|   65    |  101  |     41      | 01000001 |   A    |              Capital A               |
|   66    |  102  |     42      | 01000010 |   B    |              Capital B               |
|   67    |  103  |     43      | 01000011 |   C    |              Capital C               |
|   68    |  104  |     44      | 01000100 |   D    |              Capital D               |
|   69    |  105  |     45      | 01000101 |   E    |              Capital E               |
|   70    |  106  |     46      | 01000110 |   F    |              Capital F               |
|   71    |  107  |     47      | 01000111 |   G    |              Capital G               |
|   72    |  110  |     48      | 01001000 |   H    |              Capital H               |
|   73    |  111  |     49      | 01001001 |   I    |              Capital I               |
|   74    |  112  |     4A      | 01001010 |   J    |              Capital J               |
|   75    |  113  |     4B      | 01001011 |   K    |              Capital K               |
|   76    |  114  |     4C      | 01001100 |   L    |              Capital L               |
|   77    |  115  |     4D      | 01001101 |   M    |              Capital M               |
|   78    |  116  |     4E      | 01001110 |   N    |              Capital N               |
|   79    |  117  |     4F      | 01001111 |   O    |              Capital O               |
|   80    |  120  |     50      | 01010000 |   P    |              Capital P               |
|   81    |  121  |     51      | 01010001 |   Q    |              Capital Q               |
|   82    |  122  |     52      | 01010010 |   R    |              Capital R               |
|   83    |  123  |     53      | 01010011 |   S    |              Capital S               |
|   84    |  124  |     54      | 01010100 |   T    |              Capital T               |
|   85    |  125  |     55      | 01010101 |   U    |              Capital U               |
|   86    |  126  |     56      | 01010110 |   V    |              Capital V               |
|   87    |  127  |     57      | 01010111 |   W    |              Capital W               |
|   88    |  130  |     58      | 01011000 |   X    |              Capital X               |
|   89    |  131  |     59      | 01011001 |   Y    |              Capital Y               |
|   90    |  132  |     5A      | 01011010 |   Z    |              Capital Z               |
|   91    |  133  |     5B      | 01011011 | &#91;  |         Open Square Bracket          |
|   92    |  134  |     5C      | 01011100 | &#92;  |              Backslash               |
|   93    |  135  |     5D      | 01011101 |   ]    |         Close Square Bracket         |
|   94    |  136  |     5E      | 01011110 |   ^    |          Circumflex Accent           |
|   95    |  137  |     5F      | 01011111 |   _    |              Underscore              |
|   96    |  140  |     60      | 01100000 | &#96;  |             Grave Accent             |
|   97    |  141  |     61      | 01100001 |   a    |             Lowercase a              |
|   98    |  142  |     62      | 01100010 |   b    |             Lowercase b              |
|   99    |  143  |     63      | 01100011 |   c    |             Lowercase c              |
|   100   |  144  |     64      | 01100100 |   d    |             Lowercase d              |
|   101   |  145  |     65      | 01100101 |   e    |             Lowercase e              |
|   102   |  146  |     66      | 01100110 |   f    |             Lowercase f              |
|   103   |  147  |     67      | 01100111 |   g    |             Lowercase g              |
|   104   |  150  |     68      | 01101000 |   h    |             Lowercase h              |
|   105   |  151  |     69      | 01101001 |   i    |             Lowercase i              |
|   106   |  152  |     6A      | 01101010 |   j    |             Lowercase j              |
|   107   |  153  |     6B      | 01101011 |   k    |             Lowercase k              |
|   108   |  154  |     6C      | 01101100 |   l    |             Lowercase l              |
|   109   |  155  |     6D      | 01101101 |   m    |             Lowercase m              |
|   110   |  156  |     6E      | 01101110 |   n    |             Lowercase n              |
|   111   |  157  |     6F      | 01101111 |   o    |             Lowercase o              |
|   112   |  160  |     70      | 01110000 |   p    |             Lowercase p              |
|   113   |  161  |     71      | 01110001 |   q    |             Lowercase q              |
|   114   |  162  |     72      | 01110010 |   r    |             Lowercase r              |
|   115   |  163  |     73      | 01110011 |   s    |             Lowercase s              |
|   116   |  164  |     74      | 01110100 |   t    |             Lowercase t              |
|   117   |  165  |     75      | 01110101 |   u    |             Lowercase u              |
|   118   |  166  |     76      | 01110110 |   v    |             Lowercase v              |
|   119   |  167  |     77      | 01110111 |   w    |             Lowercase w              |
|   120   |  170  |     78      | 01111000 |   x    |             Lowercase x              |
|   121   |  171  |     79      | 01111001 |   y    |             Lowercase y              |
|   122   |  172  |     7A      | 01111010 |   z    |             Lowercase z              |
|   123   |  173  |     7B      | 01111011 |   {    |          Open Curly Bracket          |
|   124   |  174  |     7C      | 01111100 | &#124; |             Vertical Bar             |
|   125   |  175  |     7D      | 01111101 |   }    |         Close Curly Bracket          |
|   126   |  176  |     7E      | 01111110 | &#126; |                Tilde                 |
|   127   |  177  |     7F      | 01111111 |        |                Delete                |
