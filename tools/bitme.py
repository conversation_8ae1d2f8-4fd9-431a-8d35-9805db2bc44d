#!/usr/bin/env python3

# Copyright (c) the Selfie Project authors. All rights reserved.
# Please see the AUTHORS file for details. Use of this source code is
# governed by a BSD license that can be found in the LICENSE file.

# Selfie is a project of the Computational Systems Group at the
# Department of Computer Sciences of the University of Salzburg
# in Austria. For further information and code please refer to:

# selfie.cs.uni-salzburg.at

# Bitme is a bounded model checker for BTOR2 models using
# the Z3 and bitwuzla SMT solvers as reasoning engines.

# Bitme is designed to work with BTOR2 models generated by rotor
# for modeling RISC-V machines and RISC-V code. Rotor is a tool
# that is part of the selfie system.

# ------------------------------------------------------------

# for debugging segfaults: import faulthandler; faulthandler.enable()

import ctypes

try:
    rotor = ctypes.cdll.LoadLibrary("rotor")
    is_rotor_present = True
except OSError:
    print("rotor is not available")
    is_rotor_present = False

# requires Z3 and the Z3 Python API:
# pip install z3-solver

try:
    import z3
    is_Z3_present = True
except ImportError:
    print("Z3 is not available")
    is_Z3_present = False

# requires bitwuzla and the bitwuzla Python API:
# cd bitwuzla
# pip install .

try:
    import bitwuzla
    is_bitwuzla_present = True
except ImportError:
    print("bitwuzla is not available")
    is_bitwuzla_present = False

# BTOR2, Z3, and bitwuzla models

import math

# supported BTOR2 keywords and operators

def init_btor2_keywords_operators():
    global BITVEC
    global ARRAY

    global OP_SORT

    global OP_ZERO
    global OP_ONE

    global OP_CONST
    global OP_CONSTD
    global OP_CONSTH
    global OP_INPUT
    global OP_STATE

    global OP_INIT
    global OP_NEXT

    global OP_SEXT
    global OP_UEXT
    global OP_SLICE

    global OP_NOT
    global OP_INC
    global OP_DEC
    global OP_NEG

    global OP_IMPLIES
    global OP_EQ
    global OP_NEQ
    global OP_SGT
    global OP_UGT
    global OP_SGTE
    global OP_UGTE
    global OP_SLT
    global OP_ULT
    global OP_SLTE
    global OP_ULTE

    global OP_AND
    global OP_OR
    global OP_XOR

    global OP_SLL
    global OP_SRL
    global OP_SRA

    global OP_ADD
    global OP_SUB
    global OP_MUL
    global OP_SDIV
    global OP_UDIV
    global OP_SREM
    global OP_UREM

    global OP_CONCAT
    global OP_READ

    global OP_ITE
    global OP_WRITE

    global OP_BAD
    global OP_CONSTRAINT

    BITVEC = 'bitvec'
    ARRAY  = 'array'

    OP_SORT = 'sort'

    OP_ZERO = 'zero'
    OP_ONE  = 'one'

    OP_CONST  = 'const'
    OP_CONSTD = 'constd'
    OP_CONSTH = 'consth'
    OP_INPUT  = 'input'
    OP_STATE  = 'state'

    OP_INIT  = 'init'
    OP_NEXT  = 'next'

    OP_SEXT  = 'sext'
    OP_UEXT  = 'uext'
    OP_SLICE = 'slice'

    OP_NOT = 'not'
    OP_INC = 'inc'
    OP_DEC = 'dec'
    OP_NEG = 'neg'

    OP_IMPLIES = 'implies'
    OP_EQ      = 'eq'
    OP_NEQ     = 'neq'
    OP_SGT     = 'sgt'
    OP_UGT     = 'ugt'
    OP_SGTE    = 'sgte'
    OP_UGTE    = 'ugte'
    OP_SLT     = 'slt'
    OP_ULT     = 'ult'
    OP_SLTE    = 'slte'
    OP_ULTE    = 'ulte'

    OP_AND = 'and'
    OP_OR  = 'or'
    OP_XOR = 'xor'

    OP_SLL = 'sll'
    OP_SRL = 'srl'
    OP_SRA = 'sra'

    OP_ADD  = 'add'
    OP_SUB  = 'sub'
    OP_MUL  = 'mul'
    OP_SDIV = 'sdiv'
    OP_UDIV = 'udiv'
    OP_SREM = 'srem'
    OP_UREM = 'urem'

    OP_CONCAT = 'concat'
    OP_READ   = 'read'

    OP_ITE   = 'ite'
    OP_WRITE = 'write'

    OP_BAD        = 'bad'
    OP_CONSTRAINT = 'constraint'

init_btor2_keywords_operators()

current_nid = 0

def next_nid(nid = None):
    if nid is None:
        global current_nid
        current_nid += 1
        return current_nid
    else:
        return nid

class model_error(Exception):
    def __init__(self, expected, line_no):
        super().__init__(f"model error in line {line_no}: {expected} expected")

class Z3:
    def __init__(self):
        self.z3 = None

class Bitwuzla:
    def __init__(self):
        self.bitwuzla = None

class Line(Z3, Bitwuzla):
    lines = {}

    count = 0

    def __init__(self, nid, comment, line_no):
        Z3.__init__(self)
        Bitwuzla.__init__(self)
        self.nid = nid
        self.comment = "; " + comment if comment and comment[0] != ';' else comment
        self.line_no = line_no
        self.new_line()

    def __repr__(self):
        return self.__str__()

    def new_line(self):
        if self.nid is not None:
            assert self.nid not in Line.lines, f"nid {self.nid} already defined @ {self.line_no}"
            Line.lines[self.nid] = self
        type(self).count += 1

    def is_defined(nid):
        return nid in Line.lines

    def get(nid):
        assert Line.is_defined(nid), f"undefined nid {self.nid} @ {self.line_no}"
        return Line.lines[nid]

class Sort(Line):
    keyword = OP_SORT

    def __init__(self, nid, comment, line_no):
        super().__init__(nid, comment, line_no)

    def match_sorts(self, sort):
        return type(self) is type(sort)

class Bitvector(Sort):
    keyword = BITVEC

    def __init__(self, nid, size, comment, line_no):
        assert size > 0
        super().__init__(nid, comment, line_no)
        self.size = size

    def __str__(self):
        return f"{self.nid} {Sort.keyword} {Bitvec.keyword} {self.size} {self.comment}"

    def match_init_sorts(self, sort):
        return self.match_sorts(sort)

    def is_mapped_array(self):
        return False

    def is_unsigned_value(self, value):
        return 0 <= value < 2**self.size

    def is_signed_value(self, value):
        return -2**(self.size - 1) <= value < 2**(self.size - 1)

    def is_value(self, value):
        return self.is_unsigned_value(value) or self.is_signed_value(value)

    def get_unsigned_value(self, value):
        assert self.is_value(value)
        return 2**self.size + value if value < 0 else value

    def get_signed_value(self, value):
        assert self.is_value(value)
        return value - 2**self.size if value >= 2**(self.size - 1) else value

class Bool(Bitvector):
    boolean = None

    def __init__(self, nid, comment, line_no):
        super().__init__(nid, 1, comment, line_no)
        assert Bool.boolean is None
        Bool.boolean = self

    def get_z3(self):
        if self.z3 is None:
            self.z3 = z3.BoolSort()
        return self.z3

    def get_bitwuzla(self, tm):
        if self.bitwuzla is None:
            self.bitwuzla = tm.mk_bool_sort()
        return self.bitwuzla

class Bitvec(Bitvector):
    def __init__(self, nid, size, comment, line_no):
        super().__init__(nid, size, comment, line_no)

    def match_sorts(self, sort):
        return super().match_sorts(sort) and self.size == sort.size

    def get_z3(self):
        if self.z3 is None:
            self.z3 = z3.BitVecSort(self.size)
        return self.z3

    def get_bitwuzla(self, tm):
        if self.bitwuzla is None:
            self.bitwuzla = tm.mk_bv_sort(self.size)
        return self.bitwuzla

class Array(Sort):
    keyword = ARRAY

    # map arrays up to size bound to bitvectors

    ARRAY_SIZE_BOUND = 0 # array size in bits

    number_of_variable_arrays = 0
    number_of_mapped_arrays = 0

    def __init__(self, nid, array_size_line, element_size_line, comment, line_no):
        super().__init__(nid, comment, line_no)
        self.array_size_line = array_size_line
        self.element_size_line = element_size_line
        if not isinstance(array_size_line, Bitvec):
            raise model_error("array size bitvector", line_no)
        if not isinstance(element_size_line, Bitvec):
            raise model_error("element size bitvector", line_no)

    def __str__(self):
        return f"{self.nid} {Sort.keyword} {Array.keyword} {self.array_size_line.nid} {self.element_size_line.nid} {self.comment}"

    def match_sorts(self, sort):
        return (super().match_sorts(sort)
            and self.array_size_line.match_sorts(sort.array_size_line)
            and self.element_size_line.match_sorts(sort.element_size_line))

    def match_init_sorts(self, sort):
        # allow constant arrays: array init with bitvector
        return (self.match_sorts(sort)
            or (isinstance(sort, Bitvec) and self.element_size_line.match_sorts(sort)))

    def is_mapped_array(self):
        return self.array_size_line.size <= Array.ARRAY_SIZE_BOUND

    def accommodate_array_indexes(nid):
        if Array.ARRAY_SIZE_BOUND == 0:
            return nid
        else:
            # shift left by log10(2**n + 1) decimal digits where n is the array index space
            return nid * 10**(math.floor(math.log10(2**Array.ARRAY_SIZE_BOUND + 1)) + 1)

    def get_z3(self):
        if self.z3 is None:
            self.z3 = z3.ArraySort(self.array_size_line.get_z3(),
                self.element_size_line.get_z3())
        return self.z3

    def get_bitwuzla(self, tm):
        if self.bitwuzla is None:
            self.bitwuzla = tm.mk_array_sort(self.array_size_line.get_bitwuzla(tm),
                self.element_size_line.get_bitwuzla(tm))
        return self.bitwuzla

class BVDD:
    number_of_solutions = 0
    max_number_of_solutions = 0
    avg_number_of_solutions = 0
    total_number_of_solutions = 0

    def __init__(self, var_line):
        self.var_line = var_line
        self.inputs = {}

    def __str__(self):
        string = ""
        for input_value in self.inputs:
            if string:
                string += ",\n"
            string += f"{input_value}"
            if BVDD.is_inputs(self.inputs[input_value]):
                string += f" & {self.inputs[input_value]}"
            else:
                string += f" -> {self.inputs[input_value]}"
        return f"{{{string}}}"

    def __lt__(self, bvdd):
        # for sorting BVDDs when generating expressions for value sets
        return id(self) < id(bvdd)

    def number_of_inputs(bvdd):
        if BVDD.is_output(bvdd):
            return 1
        else:
            assert BVDD.is_inputs(bvdd)
            n = 0
            for input_value in bvdd.inputs:
                n += BVDD.number_of_inputs(bvdd.inputs[input_value])
        return n

    def is_always_false(bvdd):
        if bvdd is Constant.false:
            return True
        else:
            assert isinstance(bvdd, bool) or BVDD.is_inputs(bvdd)
            return False

    def is_always_true(bvdd):
        if isinstance(bvdd, bool):
            return True
        else:
            assert bvdd is Constant.false or BVDD.is_inputs(bvdd)
            return False

    def is_inputs(bvdd):
        if isinstance(bvdd, BVDD):
            return True
        else:
            assert isinstance(bvdd, bool) or isinstance(bvdd, int)
            return False

    def is_output(bvdd):
        if isinstance(bvdd, bool) or isinstance(bvdd, int):
            return True
        else:
            assert BVDD.is_inputs(bvdd)
            return False

    def set_input(self, sid_line, input_value, inputs_or_output):
        assert input_value not in self.inputs
        self.inputs[input_value] = inputs_or_output
        if BVDD.is_output(inputs_or_output):
            assert sid_line.is_unsigned_value(inputs_or_output)
            BVDD.number_of_solutions += 1
        return self

    def reduce(bvdd):
        if BVDD.is_inputs(bvdd):
            if not bvdd.inputs:
                return Constant.false
            elif len(bvdd.inputs) == 2**bvdd.var_line.sid_line.size:
                inputs_or_output = None
                for input_value in bvdd.inputs:
                    if inputs_or_output is None:
                        inputs_or_output = bvdd.inputs[input_value]
                    elif bvdd.inputs[input_value] != inputs_or_output:
                        return bvdd
                return inputs_or_output
        return bvdd

    def get_inputs(self, sid_line, output):
        inputs = BVDD(self.var_line)
        for input_value in self.inputs:
            inputs_or_output = self.inputs[input_value]
            if BVDD.is_output(inputs_or_output):
                if inputs_or_output == output:
                    inputs.set_input(sid_line, input_value, output)
            else:
                assert BVDD.is_inputs(inputs_or_output)
                inputs.set_input(sid_line, input_value, inputs_or_output.get_inputs(sid_line, output))
        return BVDD.reduce(inputs)

    def apply_unary(self, sid_line, op):
        inputs = BVDD(self.var_line)
        for input_value in self.inputs:
            inputs.set_input(sid_line, input_value, BVDD.apply(sid_line, op, self.inputs[input_value]))
        return BVDD.reduce(inputs)

    def apply_binary(self, sid_line, op, bvdd):
        assert BVDD.is_inputs(bvdd)
        if self.var_line > bvdd.var_line:
            inputs = BVDD(bvdd.var_line)
            for input_value in bvdd.inputs:
                inputs.set_input(sid_line, input_value, BVDD.apply(sid_line, op, self, bvdd.inputs[input_value]))
        else:
            inputs = BVDD(self.var_line)
            if self.var_line < bvdd.var_line:
                for input_value in self.inputs:
                    inputs.set_input(sid_line, input_value, BVDD.apply(sid_line, op, self.inputs[input_value], bvdd))
            else:
                for input_value in self.inputs:
                    if input_value in bvdd.inputs:
                        inputs.set_input(sid_line, input_value, BVDD.apply(sid_line, op,
                            self.inputs[input_value], bvdd.inputs[input_value]))
        return BVDD.reduce(inputs)

    def apply(sid_line, op, bvdd1, bvdd2 = None):
        if bvdd2 is None:
            if BVDD.is_output(bvdd1):
                return op(bvdd1)
            else:
                assert BVDD.is_inputs(bvdd1)
                return bvdd1.apply_unary(sid_line, op)
        else:
            if BVDD.is_output(bvdd1):
                if BVDD.is_output(bvdd2):
                    return op(bvdd1, bvdd2)
                else:
                    assert BVDD.is_inputs(bvdd2)
                    return bvdd2.apply_unary(sid_line, lambda y: op(bvdd1, y))
            else:
                assert BVDD.is_inputs(bvdd1)
                if BVDD.is_output(bvdd2):
                    return bvdd1.apply_unary(sid_line, lambda x: op(x, bvdd2))
                else:
                    assert BVDD.is_inputs(bvdd2)
                    return bvdd1.apply_binary(sid_line, op, bvdd2)

    def merge(self, sid_line, bvdd):
        assert BVDD.is_inputs(bvdd)
        if self.var_line > bvdd.var_line:
            return bvdd.merge(sid_line, self)
        else:
            inputs = BVDD(self.var_line)
            if self.var_line < bvdd.var_line:
                for input_value in range(2**self.var_line.sid_line.size):
                    if input_value in self.inputs:
                        # assert: intersection of self and bvdd is empty
                        assert BVDD.is_inputs(self.inputs[input_value])
                        inputs.set_input(sid_line, input_value, self.inputs[input_value].merge(sid_line, bvdd))
                    else:
                        inputs.set_input(sid_line, input_value, bvdd)
            else:
                assert self.var_line is bvdd.var_line
                for input_value in self.inputs:
                    if input_value in bvdd.inputs:
                        # assert: intersection of self and bvdd is empty
                        assert BVDD.is_inputs(self.inputs[input_value])
                        assert BVDD.is_inputs(bvdd.inputs[input_value])
                        inputs.set_input(sid_line, input_value,
                            self.inputs[input_value].merge(sid_line, bvdd.inputs[input_value]))
                    else:
                        inputs.set_input(sid_line, input_value, self.inputs[input_value])
                for input_value in bvdd.inputs:
                    if input_value not in self.inputs:
                        inputs.set_input(sid_line, input_value, bvdd.inputs[input_value])
        return BVDD.reduce(inputs)

    def get_expression(self, sid_line):
        exp_line = Zero(next_nid(), sid_line, "unreachable-value", "unreachable value", 0)
        # TODO: check if sorting (here by input value) is necessary for consistency
        for input_value in sorted(self.inputs):
            exp_line = Ite(next_nid(), sid_line,
                Comparison(next_nid(), OP_EQ, Bool.boolean,
                    self.var_line,
                    Constd(next_nid(), self.var_line.sid_line, input_value,
                        self.var_line.comment, self.var_line.line_no),
                    self.var_line.comment, self.var_line.line_no),
                BVDD.get_bvdd_expression(sid_line, self.inputs[input_value]),
                exp_line,
                self.var_line.comment, self.var_line.line_no)
        return exp_line

    def get_bvdd_expression(sid_line, bvdd):
        if BVDD.is_output(bvdd):
            return Constd(next_nid(), sid_line, int(bvdd), "domain-propagated value", 0)
        else:
            return bvdd.get_expression(sid_line)

class Values:
    total_number_of_constants = 0

    false = None
    true = None

    def __init__(self, sid_line):
        assert isinstance(sid_line, Bitvector)
        self.sid_line = sid_line
        self.values = None

    def __str__(self):
        return f"{self.sid_line}: {self.values}"

    def match_sorts(self, values):
        return self.sid_line.match_sorts(values.sid_line)

    def is_equal(self, values):
        return type(self) is type(values) and self.match_sorts(values) and self.values == values.values

    def set_values(self, sid_line, values):
        assert self.sid_line.match_sorts(sid_line)
        self.values = values
        if BVDD.is_output(values):
            assert sid_line.is_unsigned_value(values)
            Values.total_number_of_constants += 1
        return self

    def get_false_constraint(self):
        assert isinstance(self.sid_line, Bool)
        if BVDD.is_output(self.values):
            return False if self.values is False else Constant.false
        else:
            return self.values.get_inputs(self.sid_line, False)

    def get_true_constraint(self):
        assert isinstance(self.sid_line, Bool)
        if BVDD.is_output(self.values):
            return True if self.values is True else Constant.false
        else:
            return self.values.get_inputs(self.sid_line, True)

    def get_boolean_constraints(self):
        assert isinstance(self.sid_line, Bool)
        return self.get_false_constraint(), self.get_true_constraint()

    def get_expression(self):
        # naive transition from domain propagation to bit blasting
        assert isinstance(self.sid_line, Bitvector)
        return BVDD.get_bvdd_expression(self.sid_line, self.values)

    # per-value semantics of value sets

    # unary operators

    def apply_unary(self, sid_line, op):
        return Values(sid_line).set_values(sid_line, BVDD.apply(sid_line, op, self.values))

    def SignExt(self, sid_line):
        assert isinstance(self.sid_line, Bitvec)
        return self.apply_unary(sid_line, lambda x: self.sid_line.get_signed_value(x) % 2**sid_line.size)

    def ZeroExt(self, sid_line):
        assert isinstance(self.sid_line, Bitvec)
        return self.apply_unary(sid_line, lambda x: x)

    def Extract(self, sid_line, u, l):
        assert isinstance(self.sid_line, Bitvec)
        return self.apply_unary(sid_line, lambda x: (x & 2**(u + 1) - 1) >> l)

    def Not(self):
        assert isinstance(self.sid_line, Bool)
        return self.apply_unary(self.sid_line, lambda x: not x)

    def __invert__(self):
        assert isinstance(self.sid_line, Bitvec)
        return self.apply_unary(self.sid_line, lambda x: ~x % 2**self.sid_line.size)

    def Inc(self):
        assert isinstance(self.sid_line, Bitvec)
        return self.apply_unary(self.sid_line, lambda x: (x + 1) % 2**self.sid_line.size)

    def Dec(self):
        assert isinstance(self.sid_line, Bitvec)
        return self.apply_unary(self.sid_line, lambda x: (x - 1) % 2**self.sid_line.size)

    def __neg__(self):
        assert isinstance(self.sid_line, Bitvec)
        return self.apply_unary(self.sid_line, lambda x: -x % 2**self.sid_line.size)

    # binary operators

    def apply_binary(self, sid_line, values, op):
        return Values(sid_line).set_values(sid_line, BVDD.apply(sid_line, op, self.values, values.values))

    def FALSE():
        if Values.false is None:
            Values.false = Values(Bool.boolean).set_values(Bool.boolean, False)
        return Values.false

    def TRUE():
        if Values.true is None:
            Values.true = Values(Bool.boolean).set_values(Bool.boolean, True)
        return Values.true

    def Implies(self, values):
        assert isinstance(self.sid_line, Bool)
        false_constraint = self.get_false_constraint()
        if BVDD.is_always_true(false_constraint):
            assert values is None
            return Values.TRUE()
        else:
            # lazy evaluation of implied values
            assert isinstance(values, Values) and isinstance(values.sid_line, Bool)
            return self.apply_binary(Bool.boolean, values, lambda x, y: (not x) or y)

    def __eq__(self, values):
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(Bool.boolean, values, lambda x, y: x == y)

    def __ne__(self, values):
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(Bool.boolean, values, lambda x, y: x != y)

    def __gt__(self, values):
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(Bool.boolean, values,
            lambda x, y: self.sid_line.get_signed_value(x) > values.sid_line.get_signed_value(y))

    def UGT(self, values):
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(Bool.boolean, values, lambda x, y: x > y)

    def __ge__(self, values):
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(Bool.boolean, values,
            lambda x, y: self.sid_line.get_signed_value(x) >= values.sid_line.get_signed_value(y))

    def UGE(self, values):
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(Bool.boolean, values, lambda x, y: x >= y)

    def __lt__(self, values):
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(Bool.boolean, values,
            lambda x, y: self.sid_line.get_signed_value(x) < values.sid_line.get_signed_value(y))

    def ULT(self, values):
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(Bool.boolean, values, lambda x, y: x < y)

    def __le__(self, values):
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(Bool.boolean, values,
            lambda x, y: self.sid_line.get_signed_value(x) <= values.sid_line.get_signed_value(y))

    def ULE(self, values):
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(Bool.boolean, values, lambda x, y: x <= y)

    def And(self, values):
        assert isinstance(self.sid_line, Bool)
        false_constraint = self.get_false_constraint()
        if BVDD.is_always_true(false_constraint):
            assert values is None
            return Values.FALSE()
        else:
            # lazy evaluation of second operand
            assert isinstance(values, Values) and isinstance(values.sid_line, Bool)
            return self.apply_binary(Bool.boolean, values, lambda x, y: x and y)

    def Or(self, values):
        assert isinstance(self.sid_line, Bool)
        true_constraint = self.get_true_constraint()
        if BVDD.is_always_true(true_constraint):
            assert values is None
            return Values.TRUE()
        else:
            # lazy evaluation of second operand
            assert isinstance(values, Values) and isinstance(values.sid_line, Bool)
            return self.apply_binary(Bool.boolean, values, lambda x, y: x or y)

    def Xor(self, values):
        assert isinstance(self.sid_line, Bool) and isinstance(values.sid_line, Bool)
        return self.apply_binary(Bool.boolean, values, lambda x, y: x != y)

    def __and__(self, values):
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(self.sid_line, values, lambda x, y: x & y)

    def __or__(self, values):
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(self.sid_line, values, lambda x, y: x | y)

    def __xor__(self, values):
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(self.sid_line, values, lambda x, y: x ^ y)

    def __lshift__(self, values):
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(self.sid_line, values, lambda x, y: (x << y) % 2**self.sid_line.size)

    def LShR(self, values):
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(self.sid_line, values, lambda x, y: (x >> y) % 2**self.sid_line.size)

    def __rshift__(self, values):
        # right shift operator computes arithmetic right shift in Python
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(self.sid_line, values,
            lambda x, y: (self.sid_line.get_signed_value(x) >> y) % 2**self.sid_line.size)

    def __add__(self, values):
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(self.sid_line, values, lambda x, y: (x + y) % 2**self.sid_line.size)

    def __sub__(self, values):
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(self.sid_line, values, lambda x, y: (x - y) % 2**self.sid_line.size)

    def __mul__(self, values):
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(self.sid_line, values, lambda x, y: (x * y) % 2**self.sid_line.size)

    def __div__(self, values):
        # using the integer portion of division, not floor division with the // operator,
        # because int(x / y) != x // y in Python if x < 0 or y < 0 since
        # the integer portion of division truncates towards 0 whereas
        # floor division truncates towards negative infinity
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(self.sid_line, values,
            lambda x, y: (int(self.sid_line.get_signed_value(x) / values.sid_line.get_signed_value(y))
                if not (y == 0 or (self.sid_line.get_signed_value(x) == -2**(self.sid_line.size - 1) and
                    values.sid_line.get_signed_value(y) == -1))
                else -1 if y == 0 else -2**(self.sid_line.size - 1)) % 2**self.sid_line.size)

    def UDiv(self, values):
        # using floor division is ok since x >= 0 and y >= 0
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(self.sid_line, values,
            lambda x, y: x // y if y != 0 else 2**self.sid_line.size - 1)

    def SRem(self, values):
        # using the integer portion of division, not the % operator,
        # because x % y != x - int(x / y) * y in Python if x < 0 since
        # the % operator in Python computes Euclidean modulus, not remainder,
        # such that x // y * y + x % y == x holds in Python for all x and y even if x < 0
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(self.sid_line, values,
            lambda x, y: (self.sid_line.get_signed_value(x) -
                    int(self.sid_line.get_signed_value(x) / values.sid_line.get_signed_value(y)) *
                        values.sid_line.get_signed_value(y))
                    % 2**self.sid_line.size
                if not (y == 0 or (self.sid_line.get_signed_value(x) == -2**(self.sid_line.size - 1) and
                    values.sid_line.get_signed_value(y) == -1))
                else x if y == 0 else 0)

    def URem(self, values):
        # using the % operator is ok since x >= 0 and y >= 0
        assert isinstance(self.sid_line, Bitvec) and self.sid_line.match_sorts(values.sid_line)
        return self.apply_binary(self.sid_line, values, lambda x, y: x % y if y != 0 else x)

    def Concat(self, values, sid_line):
        assert isinstance(self.sid_line, Bitvec) and isinstance(values.sid_line, Bitvec)
        return self.apply_binary(sid_line, values, lambda x, y: (x << values.sid_line.size) + y)

    # ternary operators

    def constrain(self, constraint):
        assert not BVDD.is_always_false(constraint)
        return Values(self.sid_line).set_values(self.sid_line,
            BVDD.apply(self.sid_line, lambda x, y: x, self.values, constraint))

    def merge(self, values):
        assert isinstance(values, Values)
        assert self.match_sorts(values)
        assert BVDD.is_inputs(self.values) and BVDD.is_inputs(values.values)
        return Values(self.sid_line).set_values(self.sid_line, self.values.merge(self.sid_line, values.values))

    def If(self, values2, values3):
        false_constraint, true_constraint = self.get_boolean_constraints()
        if BVDD.is_always_false(false_constraint):
            assert values2 is not None
            return values2.constrain(true_constraint)
        elif BVDD.is_always_false(true_constraint):
            assert values3 is not None
            return values3.constrain(false_constraint)
        else:
            # lazy evaluation of true and false case
            values2 = values2.constrain(true_constraint)
            values3 = values3.constrain(false_constraint)
            return values2.merge(values3)

class Expression(Line):
    total_number_of_generated_expressions = 0

    def __init__(self, nid, sid_line, domain, depth, comment, line_no):
        super().__init__(nid, comment, line_no)
        self.sid_line = sid_line
        self.domain = domain
        self.depth = depth
        self.cache_values = {}
        self.z3_lambda = None
        self.bitwuzla_lambda = None
        if not isinstance(sid_line, Sort):
            raise model_error("sort", line_no)

    def print_deep(self):
        print(self)

    def get_domain(self):
        # filter out uninitialized states
        return [state for state in self.domain if state.init_line is not None]

    def is_equal(self, exp_line):
        # checking semantical equivalence is delegated to solvers
        return False

    def get_expression(self):
        return self

    def get_z3_lambda(self):
        if self.z3_lambda is None:
            domain = self.get_domain()
            if domain:
                self.z3_lambda = z3.Lambda([state.get_z3() for state in domain], self.get_z3())
            else:
                self.z3_lambda = self.get_z3()
        return self.z3_lambda

    def get_bitwuzla_lambda(self, tm):
        if self.bitwuzla_lambda is None:
            domain = self.get_domain()
            if domain:
                self.bitwuzla_lambda = tm.mk_term(bitwuzla.Kind.LAMBDA,
                    [*[state.get_bitwuzla(tm) for state in domain], self.get_bitwuzla(tm)])
            else:
                self.bitwuzla_lambda = self.get_bitwuzla(tm)
        return self.bitwuzla_lambda

class Constant(Expression):
    false = None
    true = None

    def __init__(self, nid, sid_line, value, comment, line_no):
        super().__init__(nid, sid_line, {}, 0, comment, line_no)
        if not sid_line.is_value(value):
            raise model_error(f"{value} in range of {sid_line.size}-bit bitvector", line_no)
        self.print_value = value
        self.signed_value = sid_line.get_signed_value(value)
        self.value = sid_line.get_unsigned_value(value)
        if sid_line is Bool.boolean:
            assert 0 <= self.value <= 1
            if self.value == 0:
                if Constant.false is None:
                    Constant.false = self
            else:
                assert self.value == 1
                if Constant.true is None:
                    Constant.true = self

    def print_deep(self):
        print(self)

    def get_mapped_array_expression_for(self, index):
        return self

    def get_values(self, step):
        if 0 not in self.cache_values:
            if Instance.PROPAGATE > 0:
                if isinstance(self.sid_line, Bool):
                    self.cache_values[0] = Values.TRUE() if bool(self.value) else Values.FALSE()
                else:
                    assert isinstance(self.sid_line, Bitvec)
                    self.cache_values[0] = Values(self.sid_line).set_values(self.sid_line, self.value)
            else:
                self.cache_values[0] = self
        return self.cache_values[0]

    def get_z3(self):
        if self.z3 is None:
            if isinstance(self.sid_line, Bool):
                self.z3 = z3.BoolVal(bool(self.value))
            else:
                self.z3 = z3.BitVecVal(self.value, self.sid_line.size)
        return self.z3

    def get_bitwuzla(self, tm):
        if self.bitwuzla is None:
            if isinstance(self.sid_line, Bool):
                self.bitwuzla = tm.mk_true() if bool(self.value) else tm.mk_false()
            else:
                self.bitwuzla = tm.mk_bv_value(self.sid_line.get_bitwuzla(tm), self.value)
        return self.bitwuzla

class Zero(Constant):
    keyword = OP_ZERO

    def __init__(self, nid, sid_line, symbol, comment, line_no):
        super().__init__(nid, sid_line, 0, comment, line_no)
        self.symbol = symbol

    def __str__(self):
        if self.symbol:
            return f"{self.nid} {Zero.keyword} {self.sid_line.nid} {self.symbol} {self.comment}"
        else:
            return f"{self.nid} {Zero.keyword} {self.sid_line.nid} {self.comment}"

class One(Constant):
    keyword = OP_ONE

    def __init__(self, nid, sid_line, symbol, comment, line_no):
        super().__init__(nid, sid_line, 1, comment, line_no)
        self.symbol = symbol

    def __str__(self):
        if self.symbol:
            return f"{self.nid} {One.keyword} {self.sid_line.nid} {self.symbol} {self.comment}"
        else:
            return f"{self.nid} {One.keyword} {self.sid_line.nid} {self.comment}"

class Constd(Constant):
    keyword = OP_CONSTD

    def __init__(self, nid, sid_line, value, comment, line_no):
        super().__init__(nid, sid_line, value, comment, line_no)

    def __str__(self):
        return f"{self.nid} {Constd.keyword} {self.sid_line.nid} {self.print_value} {self.comment}"

class Const(Constant):
    keyword = OP_CONST

    def __init__(self, nid, sid_line, value, comment, line_no):
        super().__init__(nid, sid_line, value, comment, line_no)

    def __str__(self):
        size = self.sid_line.size
        return f"{self.nid} {Const.keyword} {self.sid_line.nid} {self.value:0{size}b} {self.comment}"

class Consth(Constant):
    keyword = OP_CONSTH

    def __init__(self, nid, sid_line, value, comment, line_no):
        super().__init__(nid, sid_line, value, comment, line_no)

    def __str__(self):
        size = math.ceil(self.sid_line.size / 4)
        return f"{self.nid} {Consth.keyword} {self.sid_line.nid} {self.value:0{size}X} {self.comment}"

class Constant_Array(Expression):
    def __init__(self, sid_line, constant_line):
        super().__init__(None, sid_line, {}, 0, constant_line.comment, constant_line.line_no)
        self.nid = constant_line.nid # reuse nid of constant_line
        self.constant_line = constant_line
        if not isinstance(sid_line, Array):
            raise model_error("array sort", line_no)
        if not isinstance(constant_line, Constant):
            raise model_error("bitvector constant", line_no)
        if not sid_line.element_size_line.match_sorts(constant_line.sid_line):
            raise model_error("compatible sorts", line_no)

    def __str__(self):
        return f"{self.nid} {"consta"} {self.sid_line.nid} {self.constant_line.nid} {self.comment}"

    def get_mapped_array_expression_for(self, index):
        if index is not None:
            assert self.sid_line.is_mapped_array()
            return self.constant_line
        else:
            assert not self.sid_line.is_mapped_array()
            return self

    def get_values(self, step):
        return self

    def get_z3(self):
        if self.z3 is None:
            self.z3 = z3.K(self.sid_line.array_size_line.get_z3(), self.constant_line.get_z3())
        return self.z3

    def get_bitwuzla(self, tm):
        if self.bitwuzla is None:
            self.bitwuzla = tm.mk_const_array(self.sid_line.get_bitwuzla(tm), self.constant_line.get_bitwuzla(tm))
        return self.bitwuzla

class Variable(Expression):
    keywords = {OP_INPUT, OP_STATE}

    inputs = {}

    def __init__(self, nid, sid_line, domain, symbol, comment, line_no, index):
        super().__init__(nid, sid_line, domain, 0, comment, line_no)
        self.symbol = symbol
        if isinstance(sid_line, Array):
            Array.number_of_variable_arrays += 1
        self.new_mapped_array(index)

    def __lt__(self, variable):
        # ordering variables for constructing model input
        return self.nid < variable.nid

    def new_mapped_array(self, index):
        self.index = index
        if index is not None:
            if not isinstance(self.sid_line, Bitvector):
                raise model_error("bitvector", self.line_no)
        elif self.sid_line.is_mapped_array():
            Array.number_of_mapped_arrays += 1
            self.array = {}
            for index in range(2**self.sid_line.array_size_line.size):
                self.array[index] = type(self)(self.nid + index + 1, self.sid_line.element_size_line,
                    self.symbol, f"{self.comment} @ index {index}", self.line_no, index)

    def new_input(self, index):
        if index is not None or not self.sid_line.is_mapped_array():
            assert self.nid not in Variable.inputs, f"variable nid {self.nid} already defined @ {self.line_no}"
            Variable.inputs[self.nid] = self

    def get_mapped_array_expression_for(self, index):
        if index is not None:
            assert self.sid_line.is_mapped_array()
            return self.array[index]
        else:
            assert not self.sid_line.is_mapped_array()
            return self

    def get_values(self, step):
        if 0 not in self.cache_values:
            if isinstance(self.sid_line, Bitvector) and self.sid_line.size <= Instance.PROPAGATE:
                bvdd = BVDD(self)
                if isinstance(self.sid_line, Bool):
                    bvdd.set_input(self.sid_line, 0, False).set_input(self.sid_line, 1, True)
                else:
                    for value in range(2**self.sid_line.size):
                        bvdd.set_input(self.sid_line, value, value)
                self.cache_values[0] = Values(self.sid_line).set_values(self.sid_line, bvdd)
            else:
                self.cache_values[0] = self
        return self.cache_values[0]

    def get_z3(self):
        if self.z3 is None:
            self.z3 = z3.Const(self.name, self.sid_line.get_z3())
        return self.z3

class Input(Variable):
    keyword = OP_INPUT

    def __init__(self, nid, sid_line, symbol, comment, line_no, index = None):
        super().__init__(nid, sid_line, {}, symbol, comment, line_no, index)
        self.name = f"input{self.nid}"
        self.new_input(index)

    def __str__(self):
        return f"{self.nid} {Input.keyword} {self.sid_line.nid} {self.symbol} {self.comment}"

    def get_z3_name(self, step):
        return self.get_z3()

    def get_z3_instance(self, step):
        return self.get_z3()

    def get_bitwuzla(self, tm):
        if self.bitwuzla is None:
            self.bitwuzla = tm.mk_const(self.sid_line.get_bitwuzla(tm), self.name)
        return self.bitwuzla

    def get_bitwuzla_name(self, step, tm):
        return self.get_bitwuzla(tm)

    def get_bitwuzla_instance(self, step, tm):
        return self.get_bitwuzla(tm)

class Instance:
    PROPAGATE = None
    PROPAGATE_UNARY = True
    PROPAGATE_BINARY = True
    PROPAGATE_ITE = True
    LAMBDAS = True

    def __init__(self, instance_of):
        self.instance_of = instance_of
        self.cache_instance = {}
        self.cache_z3_instance = {}
        self.cache_bitwuzla_instance = {}

    def __str__(self):
        string = ""
        for step in self.cache_instance:
            if string:
                string += "\n"
            string += f"{step}: {self.cache_instance[step]}"
        return f"{self.instance_of}{"\n" if string else ""}{string}"

    def has_instance(self, step):
        return step in self.cache_instance

    def get_instance(self, step):
        assert self.has_instance(step)
        return self.cache_instance[step]

    def init_instance(self, instance):
        self.cache_instance[-1] = instance

    def set_instance(self, instance, step):
        # bad instances may be overwritten if proven false
        self.cache_instance[step] = instance
        if Instance.PROPAGATE is not None:
            self.cache_instance[step] = self.cache_instance[step].get_values(step)

    def get_z3_select(self, step):
        if step not in self.cache_z3_instance:
            instance = self.get_instance(step).get_expression()
            assert step not in self.cache_z3_instance
            domain = instance.get_domain()
            if domain:
                self.cache_z3_instance[step] = z3.Select(instance.get_z3_lambda(),
                    *[state.get_z3_name(step) for state in domain])
            else:
                self.cache_z3_instance[step] = instance.get_z3_lambda()
        return self.cache_z3_instance[step]

    def get_z3_substitute(self, step):
        if step not in self.cache_z3_instance:
            instance = self.get_instance(step).get_expression()
            assert step not in self.cache_z3_instance
            self.cache_z3_instance[step] = instance.get_z3()
            domain = instance.get_domain()
            if domain:
                current_states = [state.get_z3() for state in domain]
                next_states = [state.get_z3_name(step) for state in domain]
                renaming = list(zip(current_states, next_states))

                self.cache_z3_instance[step] = z3.substitute(self.cache_z3_instance[step], renaming)
        return self.cache_z3_instance[step]

    def get_z3_instance(self, step):
        if Instance.LAMBDAS:
            return self.get_z3_select(step)
        else:
            return self.get_z3_substitute(step)

    def get_bitwuzla_select(self, step, tm):
        if step not in self.cache_bitwuzla_instance:
            instance = self.get_instance(step).get_expression()
            assert step not in self.cache_bitwuzla_instance
            domain = instance.get_domain()
            if domain:
                self.cache_bitwuzla_instance[step] = tm.mk_term(bitwuzla.Kind.APPLY,
                    [instance.get_bitwuzla_lambda(tm),
                    *[state.get_bitwuzla_name(step, tm) for state in domain]])
            else:
                self.cache_bitwuzla_instance[step] = instance.get_bitwuzla_lambda(tm)
        return self.cache_bitwuzla_instance[step]

    def get_bitwuzla_substitute(self, step, tm):
        if step not in self.cache_bitwuzla_instance:
            instance = self.get_instance(step).get_expression()
            assert step not in self.cache_bitwuzla_instance
            self.cache_bitwuzla_instance[step] = instance.get_bitwuzla(tm)
            domain = instance.get_domain()
            if domain:
                current_states = [state.get_bitwuzla(tm) for state in domain]
                next_states = [state.get_bitwuzla_name(step, tm) for state in domain]
                renaming = dict(zip(current_states, next_states))

                self.cache_bitwuzla_instance[step] = tm.substitute_term(self.cache_bitwuzla_instance[step], renaming)
        return self.cache_bitwuzla_instance[step]

    def get_bitwuzla_instance(self, step, tm):
        if Instance.LAMBDAS:
            return self.get_bitwuzla_select(step, tm)
        else:
            return self.get_bitwuzla_substitute(step, tm)

class State(Variable):
    keyword = OP_STATE

    states = {}

    pc = None

    def __init__(self, nid, sid_line, symbol, comment, line_no, index = None):
        # domain is ordered set by using dictionary with None values
        super().__init__(nid, sid_line, {self:None}, symbol, comment, line_no, index)
        self.name = f"state{self.nid}"
        self.init_line = None
        self.next_line = None
        self.cache_z3_name = {}
        self.cache_bitwuzla_name = {}
        self.instance = Instance(self)
        self.instance.init_instance(self) # initialize with itself upon creation of state
        self.new_state(index)
        # rotor-dependent program counter declaration
        if comment == "; program counter":
            State.pc = self

    def __str__(self):
        return f"{self.nid} {State.keyword} {self.sid_line.nid} {self.symbol} {self.comment}"

    def new_state(self, index):
        if index is not None or not self.sid_line.is_mapped_array():
            assert self.nid not in State.states, f"state nid {self.nid} already defined @ {self.line_no}"
            State.states[self.nid] = self

    def remove_state(self):
        for key in State.states.keys():
            if State.states[key] is self:
                del State.states[key]
                return

    def get_mapped_array_expression_for(self, index):
        if isinstance(self.sid_line, Bitvector) or self.sid_line.is_mapped_array():
            if self.init_line is not None and self.next_line is not None and self.next_line.exp_line is self:
                # propagate initial value of initialized read-only bitvector states
                return self.init_line.exp_line.get_mapped_array_expression_for(index)
        return super().get_mapped_array_expression_for(index)

    def has_instance(self, step):
        return self.instance.has_instance(step)

    def get_instance(self, step):
        if self.next_line is None:
            # all instances of an untransitioned state are
            # the state itself, if uninitialized, or its initial state
            return self.instance.get_instance(-1)
        else:
            return self.instance.get_instance(step)

    def set_instance(self, instance, step):
        self.instance.set_instance(instance, step)

    def get_values(self, step):
        if step == -1:
            step = 0
        instance = self.get_instance(step - 1)
        if instance is self:
            # uninitialized state
            return super().get_values(step - 1)
        else:
            return instance

    def get_step_name(self, step):
        return f"{self.name}-{step}"

    def get_z3_name(self, step):
        if step == -1:
            step = 0
        if step not in self.cache_z3_name:
            self.cache_z3_name[step] = z3.Const(self.get_step_name(step), self.sid_line.get_z3())
        return self.cache_z3_name[step]

    def get_z3_instance(self, step):
        if self.next_line is None:
            # all instances of an untransitioned state are
            # the state itself, if uninitialized, or its initial state
            return self.instance.get_z3_instance(-1)
        else:
            return self.instance.get_z3_instance(step)

    def get_bitwuzla(self, tm):
        if self.bitwuzla is None:
            if self.init_line is None:
                self.bitwuzla = tm.mk_const(self.sid_line.get_bitwuzla(tm), self.name)
            else:
                self.bitwuzla = tm.mk_var(self.sid_line.get_bitwuzla(tm), self.name)
        return self.bitwuzla

    def get_bitwuzla_name(self, step, tm):
        if step == -1:
            step = 0
        if step not in self.cache_bitwuzla_name:
            self.cache_bitwuzla_name[step] = tm.mk_const(self.sid_line.get_bitwuzla(tm),
                self.get_step_name(step))
        return self.cache_bitwuzla_name[step]

    def get_bitwuzla_instance(self, step, tm):
        if self.next_line is None:
            # all instances of an untransitioned state are
            # the state itself, if uninitialized, or its initial state
            return self.instance.get_bitwuzla_instance(-1, tm)
        else:
            return self.instance.get_bitwuzla_instance(step, tm)

class Indexed(Expression):
    def __init__(self, nid, sid_line, arg1_line, comment, line_no):
        super().__init__(nid, sid_line, arg1_line.domain, arg1_line.depth + 1, comment, line_no)
        self.arg1_line = arg1_line
        if not isinstance(arg1_line, Expression):
            raise model_error("expression operand", line_no)
        if not isinstance(sid_line, Bitvec):
            raise model_error("bitvector result", line_no)
        if not isinstance(arg1_line.sid_line, Bitvec):
            raise model_error("bitvector operand", line_no)

    def get_mapped_array_expression_for(self, index):
        assert index is None
        arg1_line = self.arg1_line.get_mapped_array_expression_for(None)
        return self.copy(arg1_line)

class Ext(Indexed):
    keywords = {OP_SEXT, OP_UEXT}

    def __init__(self, nid, op, sid_line, arg1_line, w, comment, line_no):
        super().__init__(nid, sid_line, arg1_line, comment, line_no)
        assert op in Ext.keywords
        self.op = op
        self.w = w
        if sid_line.size != arg1_line.sid_line.size + w:
            raise model_error("compatible bitvector sorts", line_no)

    def __str__(self):
        return f"{self.nid} {self.op} {self.sid_line.nid} {self.arg1_line.nid} {self.w} {self.comment}"

    def copy(self, arg1_line):
        if self.arg1_line is not arg1_line:
            Expression.total_number_of_generated_expressions += 1
            return Ext(next_nid(), self.op, self.sid_line, arg1_line, self.w, self.comment, self.line_no)
        else:
            return self

    def get_values(self, step):
        if step not in self.cache_values:
            arg1_value = self.arg1_line.get_values(step)
            if Instance.PROPAGATE_UNARY and isinstance(arg1_value, Values):
                if self.op == OP_SEXT:
                    self.cache_values[step] = arg1_value.SignExt(self.sid_line)
                else:
                    assert self.op == OP_UEXT
                    self.cache_values[step] = arg1_value.ZeroExt(self.sid_line)
                return self.cache_values[step]
            arg1_value = arg1_value.get_expression()
            self.cache_values[step] = self.copy(arg1_value)
        return self.cache_values[step]

    def get_z3(self):
        if self.z3 is None:
            if self.op == OP_SEXT:
                self.z3 = z3.SignExt(self.w, self.arg1_line.get_z3())
            else:
                assert self.op == OP_UEXT
                self.z3 = z3.ZeroExt(self.w, self.arg1_line.get_z3())
        return self.z3

    def get_bitwuzla(self, tm):
        if self.bitwuzla is None:
            if self.op == OP_SEXT:
                bitwuzla_op = bitwuzla.Kind.BV_SIGN_EXTEND
            else:
                assert self.op == OP_UEXT
                bitwuzla_op = bitwuzla.Kind.BV_ZERO_EXTEND
            self.bitwuzla = tm.mk_term(bitwuzla_op,
                [self.arg1_line.get_bitwuzla(tm)], [self.w])
        return self.bitwuzla

class Slice(Indexed):
    keyword = OP_SLICE

    def __init__(self, nid, sid_line, arg1_line, u, l, comment, line_no):
        super().__init__(nid, sid_line, arg1_line, comment, line_no)
        self.u = u
        self.l = l
        if u >= arg1_line.sid_line.size:
            raise model_error("upper bit in range", line_no)
        if u < l:
            raise model_error("upper bit >= lower bit", line_no)
        if sid_line.size != u - l + 1:
            raise model_error("compatible bitvector sorts", line_no)

    def __str__(self):
        return f"{self.nid} {Slice.keyword} {self.sid_line.nid} {self.arg1_line.nid} {self.u} {self.l} {self.comment}"

    def copy(self, arg1_line):
        if self.arg1_line is not arg1_line:
            Expression.total_number_of_generated_expressions += 1
            return Slice(next_nid(), self.sid_line, arg1_line, self.u, self.l, self.comment, self.line_no)
        else:
            return self

    def get_values(self, step):
        if step not in self.cache_values:
            arg1_value = self.arg1_line.get_values(step)
            if Instance.PROPAGATE_UNARY and isinstance(arg1_value, Values):
                self.cache_values[step] = arg1_value.Extract(self.sid_line, self.u, self.l)
                return self.cache_values[step]
            arg1_value = arg1_value.get_expression()
            self.cache_values[step] = self.copy(arg1_value)
        return self.cache_values[step]

    def get_z3(self):
        if self.z3 is None:
            self.z3 = z3.Extract(self.u, self.l, self.arg1_line.get_z3())
        return self.z3

    def get_bitwuzla(self, tm):
        if self.bitwuzla is None:
            self.bitwuzla = tm.mk_term(bitwuzla.Kind.BV_EXTRACT,
                [self.arg1_line.get_bitwuzla(tm)], [self.u, self.l])
        return self.bitwuzla

class Unary(Expression):
    keywords = {OP_NOT, OP_INC, OP_DEC, OP_NEG}

    def __init__(self, nid, op, sid_line, arg1_line, comment, line_no):
        super().__init__(nid, sid_line, arg1_line.domain, arg1_line.depth + 1, comment, line_no)
        assert op in Unary.keywords
        self.op = op
        self.arg1_line = arg1_line
        if not isinstance(arg1_line, Expression):
            raise model_error("expression operand", line_no)
        if op == 'not' and not isinstance(sid_line, Bitvector):
            raise model_error("Boolean or bitvector result", line_no)
        if op != 'not' and not isinstance(sid_line, Bitvec):
            raise model_error("bitvector result", line_no)
        if not sid_line.match_sorts(arg1_line.sid_line):
            raise model_error("compatible sorts", line_no)

    def __str__(self):
        return f"{self.nid} {self.op} {self.sid_line.nid} {self.arg1_line.nid} {self.comment}"

    def print_deep(self):
        self.arg1_line.print_deep()
        print(self)

    def copy(self, arg1_line):
        if self.arg1_line is not arg1_line:
            Expression.total_number_of_generated_expressions += 1
            return type(self)(next_nid(), self.op, self.sid_line, arg1_line, self.comment, self.line_no)
        else:
            return self

    def get_mapped_array_expression_for(self, index):
        assert index is None
        arg1_line = self.arg1_line.get_mapped_array_expression_for(None)
        return self.copy(arg1_line)

    def get_values(self, step):
        if step not in self.cache_values:
            arg1_value = self.arg1_line.get_values(step)
            if Instance.PROPAGATE_UNARY and isinstance(arg1_value, Values):
                if self.op == OP_NOT:
                    if isinstance(self.sid_line, Bool):
                        self.cache_values[step] = arg1_value.Not()
                    else:
                        self.cache_values[step] = ~arg1_value
                elif self.op == OP_INC:
                    self.cache_values[step] = arg1_value.Inc()
                elif self.op == OP_DEC:
                    self.cache_values[step] = arg1_value.Dec()
                else:
                    assert self.op == OP_NEG
                    self.cache_values[step] = -arg1_value
                return self.cache_values[step]
            arg1_value = arg1_value.get_expression()
            self.cache_values[step] = self.copy(arg1_value)
        return self.cache_values[step]

    def get_z3(self):
        if self.z3 is None:
            z3_arg1 = self.arg1_line.get_z3()
            if self.op == OP_NOT:
                if isinstance(self.sid_line, Bool):
                    self.z3 = z3.Not(z3_arg1)
                else:
                    self.z3 = ~z3_arg1
            elif self.op == OP_INC:
                self.z3 = z3_arg1 + 1
            elif self.op == OP_DEC:
                self.z3 = z3_arg1 - 1
            else:
                assert self.op == OP_NEG
                self.z3 = -z3_arg1
        return self.z3

    def get_bitwuzla(self, tm):
        if self.bitwuzla is None:
            if self.op == OP_NOT:
                if isinstance(self.sid_line, Bool):
                    bitwuzla_op = bitwuzla.Kind.NOT
                else:
                    bitwuzla_op = bitwuzla.Kind.BV_NOT
            elif self.op == OP_INC:
                bitwuzla_op = bitwuzla.Kind.BV_INC
            elif self.op == OP_DEC:
                bitwuzla_op = bitwuzla.Kind.BV_DEC
            else:
                assert self.op == OP_NEG
                bitwuzla_op = bitwuzla.Kind.BV_NEG
            self.bitwuzla = tm.mk_term(bitwuzla_op, [self.arg1_line.get_bitwuzla(tm)])
        return self.bitwuzla

class Binary(Expression):
    keywords = {OP_IMPLIES, OP_EQ, OP_NEQ, OP_SGT, OP_UGT, OP_SGTE, OP_UGTE, OP_SLT, OP_ULT, OP_SLTE, OP_ULTE, OP_AND, OP_OR, OP_XOR, OP_SLL, OP_SRL, OP_SRA, OP_ADD, OP_SUB, OP_MUL, OP_SDIV, OP_UDIV, OP_SREM, OP_UREM, OP_CONCAT, OP_READ}

    def __init__(self, nid, op, sid_line, arg1_line, arg2_line, comment, line_no):
        super().__init__(nid, sid_line, arg1_line.domain | arg2_line.domain,
            max(arg1_line.depth, arg2_line.depth) + 1, comment, line_no)
        assert op in Binary.keywords
        self.op = op
        self.arg1_line = arg1_line
        self.arg2_line = arg2_line
        if not isinstance(arg1_line, Expression):
            raise model_error("expression left operand", line_no)
        if not isinstance(arg2_line, Expression):
            raise model_error("expression right operand", line_no)

    def __str__(self):
        return f"{self.nid} {self.op} {self.sid_line.nid} {self.arg1_line.nid} {self.arg2_line.nid} {self.comment}"

    def print_deep(self):
        self.arg1_line.print_deep()
        self.arg2_line.print_deep()
        print(self)

    def copy(self, arg1_line, arg2_line):
        if self.arg1_line is not arg1_line or self.arg2_line is not arg2_line:
            Expression.total_number_of_generated_expressions += 1
            return type(self)(next_nid(), self.op, self.sid_line, arg1_line, arg2_line, self.comment, self.line_no)
        else:
            return self

    def get_mapped_array_expression_for(self, index):
        assert index is None
        arg1_line = self.arg1_line.get_mapped_array_expression_for(None)
        arg2_line = self.arg2_line.get_mapped_array_expression_for(None)
        return self.copy(arg1_line, arg2_line)

class Implies(Binary):
    keyword = OP_IMPLIES

    def __init__(self, nid, op, sid_line, arg1_line, arg2_line, comment, line_no):
        assert op == Implies.keyword
        super().__init__(nid, Implies.keyword, sid_line, arg1_line, arg2_line, comment, line_no)
        if not isinstance(sid_line, Bool):
            raise model_error("Boolean result", line_no)
        if not sid_line.match_sorts(arg1_line.sid_line):
            raise model_error("compatible result and first operand sorts", line_no)
        if not arg1_line.sid_line.match_sorts(arg2_line.sid_line):
            raise model_error("compatible first and second operand sorts", line_no)

    def get_values(self, step):
        if step not in self.cache_values:
            arg1_value = self.arg1_line.get_values(step)
            if Instance.PROPAGATE_BINARY and isinstance(arg1_value, Values):
                false_constraint = arg1_value.get_false_constraint()
                if BVDD.is_always_true(false_constraint):
                    self.cache_values[step] = arg1_value.Implies(None)
                    return self.cache_values[step]
                else:
                    # lazy evaluation of implied values
                    arg2_value = self.arg2_line.get_values(step)
                    if isinstance(arg2_value, Values):
                        self.cache_values[step] = arg1_value.Implies(arg2_value)
                        return self.cache_values[step]
            else:
                arg2_value = self.arg2_line.get_values(step)
            arg1_value = arg1_value.get_expression()
            arg2_value = arg2_value.get_expression()
            self.cache_values[step] = self.copy(arg1_value, arg2_value)
        return self.cache_values[step]

    def get_z3(self):
        if self.z3 is None:
            self.z3 = z3.Implies(self.arg1_line.get_z3(), self.arg2_line.get_z3())
        return self.z3

    def get_bitwuzla(self, tm):
        if self.bitwuzla is None:
            self.bitwuzla = tm.mk_term(bitwuzla.Kind.IMPLIES,
                [self.arg1_line.get_bitwuzla(tm), self.arg2_line.get_bitwuzla(tm)])
        return self.bitwuzla

class Comparison(Binary):
    keywords = {OP_EQ, OP_NEQ, OP_SGT, OP_UGT, OP_SGTE, OP_UGTE, OP_SLT, OP_ULT, OP_SLTE, OP_ULTE}

    def __init__(self, nid, op, sid_line, arg1_line, arg2_line, comment, line_no):
        assert op in Comparison.keywords
        super().__init__(nid, op, sid_line, arg1_line, arg2_line, comment, line_no)
        if not isinstance(sid_line, Bool):
            raise model_error("Boolean result", line_no)
        if not isinstance(arg1_line.sid_line, Bitvec):
            raise model_error("bitvector first operand", line_no)
        if not arg1_line.sid_line.match_sorts(arg2_line.sid_line):
            raise model_error("compatible first and second operand sorts", line_no)

    def get_values(self, step):
        if step not in self.cache_values:
            arg1_value = self.arg1_line.get_values(step)
            arg2_value = self.arg2_line.get_values(step)
            if Instance.PROPAGATE_BINARY:
                if isinstance(arg1_value, Values) and isinstance(arg2_value, Values):
                    if self.op == OP_EQ:
                        self.cache_values[step] = arg1_value == arg2_value
                    elif self.op == OP_NEQ:
                        self.cache_values[step] = arg1_value != arg2_value
                    elif self.op == OP_SGT:
                        self.cache_values[step] = arg1_value > arg2_value
                    elif self.op == OP_UGT:
                        self.cache_values[step] = arg1_value.UGT(arg2_value)
                    elif self.op == OP_SGTE:
                        self.cache_values[step] = arg1_value >= arg2_value
                    elif self.op == OP_UGTE:
                        self.cache_values[step] = arg1_value.UGE(arg2_value)
                    elif self.op == OP_SLT:
                        self.cache_values[step] = arg1_value < arg2_value
                    elif self.op == OP_ULT:
                        self.cache_values[step] = arg1_value.ULT(arg2_value)
                    elif self.op == OP_SLTE:
                        self.cache_values[step] = arg1_value <= arg2_value
                    else:
                        assert self.op == OP_ULTE
                        self.cache_values[step] = arg1_value.ULE(arg2_value)
                    return self.cache_values[step]
            arg1_value = arg1_value.get_expression()
            arg2_value = arg2_value.get_expression()
            self.cache_values[step] = self.copy(arg1_value, arg2_value)
        return self.cache_values[step]

    def get_z3(self):
        if self.z3 is None:
            z3_arg1 = self.arg1_line.get_z3()
            z3_arg2 = self.arg2_line.get_z3()
            if self.op == OP_EQ:
                self.z3 = z3_arg1 == z3_arg2
            elif self.op == OP_NEQ:
                self.z3 = z3_arg1 != z3_arg2
            elif self.op == OP_SGT:
                self.z3 = z3_arg1 > z3_arg2
            elif self.op == OP_UGT:
                self.z3 = z3.UGT(z3_arg1, z3_arg2)
            elif self.op == OP_SGTE:
                self.z3 = z3_arg1 >= z3_arg2
            elif self.op == OP_UGTE:
                self.z3 = z3.UGE(z3_arg1, z3_arg2)
            elif self.op == OP_SLT:
                self.z3 = z3_arg1 < z3_arg2
            elif self.op == OP_ULT:
                self.z3 = z3.ULT(z3_arg1, z3_arg2)
            elif self.op == OP_SLTE:
                self.z3 = z3_arg1 <= z3_arg2
            else:
                assert self.op == OP_ULTE
                self.z3 = z3.ULE(z3_arg1, z3_arg2)
        return self.z3

    def get_bitwuzla(self, tm):
        if self.bitwuzla is None:
            if self.op == OP_EQ:
                bitwuzla_op = bitwuzla.Kind.EQUAL
            elif self.op == OP_NEQ:
                bitwuzla_op = bitwuzla.Kind.DISTINCT
            elif self.op == OP_SGT:
                bitwuzla_op = bitwuzla.Kind.BV_SGT
            elif self.op == OP_UGT:
                bitwuzla_op = bitwuzla.Kind.BV_UGT
            elif self.op == OP_SGTE:
                bitwuzla_op = bitwuzla.Kind.BV_SGE
            elif self.op == OP_UGTE:
                bitwuzla_op = bitwuzla.Kind.BV_UGE
            elif self.op == OP_SLT:
                bitwuzla_op = bitwuzla.Kind.BV_SLT
            elif self.op == OP_ULT:
                bitwuzla_op = bitwuzla.Kind.BV_ULT
            elif self.op == OP_SLTE:
                bitwuzla_op = bitwuzla.Kind.BV_SLE
            else:
                assert self.op == OP_ULTE
                bitwuzla_op = bitwuzla.Kind.BV_ULE
            self.bitwuzla = tm.mk_term(bitwuzla_op,
                [self.arg1_line.get_bitwuzla(tm), self.arg2_line.get_bitwuzla(tm)])
        return self.bitwuzla

class Logical(Binary):
    keywords = {OP_AND, OP_OR, OP_XOR}

    def __init__(self, nid, op, sid_line, arg1_line, arg2_line, comment, line_no):
        assert op in Logical.keywords
        super().__init__(nid, op, sid_line, arg1_line, arg2_line, comment, line_no)
        if not isinstance(sid_line, Bitvector):
            raise model_error("Boolean or bitvector result", line_no)
        if not sid_line.match_sorts(arg1_line.sid_line):
            raise model_error("compatible result and first operand sorts", line_no)
        if not arg1_line.sid_line.match_sorts(arg2_line.sid_line):
            raise model_error("compatible first and second operand sorts", line_no)

    def get_values(self, step):
        if step not in self.cache_values:
            if Instance.PROPAGATE_BINARY:
                if isinstance(self.sid_line, Bool):
                    arg1_value = self.arg1_line.get_values(step)
                    if isinstance(arg1_value, Values):
                        false_constraint, true_constraint = arg1_value.get_boolean_constraints()
                        if self.op == OP_AND:
                            if BVDD.is_always_true(false_constraint):
                                self.cache_values[step] = arg1_value.And(None)
                                return self.cache_values[step]
                            else:
                                # lazy evaluation of second operand
                                arg2_value = self.arg2_line.get_values(step)
                                if isinstance(arg2_value, Values):
                                    self.cache_values[step] = arg1_value.And(arg2_value)
                                    return self.cache_values[step]
                        elif self.op == OP_OR:
                            if BVDD.is_always_true(true_constraint):
                                self.cache_values[step] = arg1_value.Or(None)
                                return self.cache_values[step]
                            else:
                                # lazy evaluation of second operand
                                arg2_value = self.arg2_line.get_values(step)
                                if isinstance(arg2_value, Values):
                                    self.cache_values[step] = arg1_value.Or(arg2_value)
                                    return self.cache_values[step]
                        else:
                            assert self.op == OP_XOR
                            arg2_value = self.arg2_line.get_values(step)
                            if isinstance(arg2_value, Values):
                                self.cache_values[step] = arg1_value.Xor(arg2_value)
                                return self.cache_values[step]
                    arg2_value = self.arg2_line.get_values(step)
                else:
                    arg1_value = self.arg1_line.get_values(step)
                    arg2_value = self.arg2_line.get_values(step)
                    if isinstance(arg1_value, Values) and isinstance(arg2_value, Values):
                        if self.op == OP_AND:
                            self.cache_values[step] = arg1_value & arg2_value
                        elif self.op == OP_OR:
                            self.cache_values[step] = arg1_value | arg2_value
                        else:
                            assert self.op == OP_XOR
                            self.cache_values[step] = arg1_value ^ arg2_value
                        return self.cache_values[step]
            else:
                arg1_value = self.arg1_line.get_values(step)
                arg2_value = self.arg2_line.get_values(step)
            arg1_value = arg1_value.get_expression()
            arg2_value = arg2_value.get_expression()
            self.cache_values[step] = self.copy(arg1_value, arg2_value)
        return self.cache_values[step]

    def get_z3(self):
        if self.z3 is None:
            z3_arg1 = self.arg1_line.get_z3()
            z3_arg2 = self.arg2_line.get_z3()
            if isinstance(self.sid_line, Bool):
                if self.op == OP_AND:
                    self.z3 = z3.And(z3_arg1, z3_arg2)
                elif self.op == OP_OR:
                    self.z3 = z3.Or(z3_arg1, z3_arg2)
                else:
                    assert self.op == OP_XOR
                    self.z3 = z3.Xor(z3_arg1, z3_arg2)
            else:
                if self.op == OP_AND:
                    self.z3 = z3_arg1 & z3_arg2
                elif self.op == OP_OR:
                    self.z3 = z3_arg1 | z3_arg2
                else:
                    assert self.op == OP_XOR
                    self.z3 = z3_arg1 ^ z3_arg2
        return self.z3

    def get_bitwuzla(self, tm):
        if self.bitwuzla is None:
            if isinstance(self.sid_line, Bool):
                if self.op == OP_AND:
                    bitwuzla_op = bitwuzla.Kind.AND
                elif self.op == OP_OR:
                    bitwuzla_op = bitwuzla.Kind.OR
                else:
                    assert self.op == OP_XOR
                    bitwuzla_op = bitwuzla.Kind.XOR
            else:
                if self.op == OP_AND:
                    bitwuzla_op = bitwuzla.Kind.BV_AND
                elif self.op == OP_OR:
                    bitwuzla_op = bitwuzla.Kind.BV_OR
                else:
                    assert self.op == OP_XOR
                    bitwuzla_op = bitwuzla.Kind.BV_XOR
            self.bitwuzla = tm.mk_term(bitwuzla_op,
                [self.arg1_line.get_bitwuzla(tm), self.arg2_line.get_bitwuzla(tm)])
        return self.bitwuzla

class Computation(Binary):
    keywords = {OP_SLL, OP_SRL, OP_SRA, OP_ADD, OP_SUB, OP_MUL, OP_SDIV, OP_UDIV, OP_SREM, OP_UREM}

    def __init__(self, nid, op, sid_line, arg1_line, arg2_line, comment, line_no):
        assert op in Computation.keywords
        super().__init__(nid, op, sid_line, arg1_line, arg2_line, comment, line_no)
        if not isinstance(sid_line, Bitvec):
            raise model_error("bitvector result", line_no)
        if not sid_line.match_sorts(arg1_line.sid_line):
            raise model_error("compatible result and first operand sorts", line_no)
        if not arg1_line.sid_line.match_sorts(arg2_line.sid_line):
            raise model_error("compatible first and second operand sorts", line_no)

    def get_values(self, step):
        if step not in self.cache_values:
            arg1_value = self.arg1_line.get_values(step)
            arg2_value = self.arg2_line.get_values(step)
            if Instance.PROPAGATE_BINARY:
                if isinstance(arg1_value, Values) and isinstance(arg2_value, Values):
                    if self.op == OP_SLL:
                        self.cache_values[step] = arg1_value << arg2_value
                    elif self.op == OP_SRL:
                        self.cache_values[step] = arg1_value.LShR(arg2_value)
                    elif self.op == OP_SRA:
                        self.cache_values[step] = arg1_value >> arg2_value
                    elif self.op == OP_ADD:
                        self.cache_values[step] = arg1_value + arg2_value
                    elif self.op == OP_SUB:
                        self.cache_values[step] = arg1_value - arg2_value
                    elif self.op == OP_MUL:
                        self.cache_values[step] = arg1_value * arg2_value
                    elif self.op == OP_SDIV:
                        self.cache_values[step] = arg1_value / arg2_value
                    elif self.op == OP_UDIV:
                        self.cache_values[step] = arg1_value.UDiv(arg2_value)
                    elif self.op == OP_SREM:
                        self.cache_values[step] = arg1_value.SRem(arg2_value)
                    else:
                        assert self.op == OP_UREM
                        self.cache_values[step] = arg1_value.URem(arg2_value)
                    return self.cache_values[step]
            arg1_value = arg1_value.get_expression()
            arg2_value = arg2_value.get_expression()
            self.cache_values[step] = self.copy(arg1_value, arg2_value)
        return self.cache_values[step]

    def get_z3(self):
        if self.z3 is None:
            z3_arg1 = self.arg1_line.get_z3()
            z3_arg2 = self.arg2_line.get_z3()
            if self.op == OP_SLL:
                self.z3 = z3_arg1 << z3_arg2
            elif self.op == OP_SRL:
                self.z3 = z3.LShR(z3_arg1, z3_arg2)
            elif self.op == OP_SRA:
                self.z3 = z3_arg1 >> z3_arg2
            elif self.op == OP_ADD:
                self.z3 = z3_arg1 + z3_arg2
            elif self.op == OP_SUB:
                self.z3 = z3_arg1 - z3_arg2
            elif self.op == OP_MUL:
                self.z3 = z3_arg1 * z3_arg2
            elif self.op == OP_SDIV:
                self.z3 = z3_arg1 / z3_arg2
            elif self.op == OP_UDIV:
                self.z3 = z3.UDiv(z3_arg1, z3_arg2)
            elif self.op == OP_SREM:
                self.z3 = z3.SRem(z3_arg1, z3_arg2)
            else:
                assert self.op == OP_UREM
                self.z3 = z3.URem(z3_arg1, z3_arg2)
        return self.z3

    def get_bitwuzla(self, tm):
        if self.bitwuzla is None:
            if self.op == OP_SLL:
                bitwuzla_op = bitwuzla.Kind.BV_SHL
            elif self.op == OP_SRL:
                bitwuzla_op = bitwuzla.Kind.BV_SHR
            elif self.op == OP_SRA:
                bitwuzla_op = bitwuzla.Kind.BV_ASHR
            elif self.op == OP_ADD:
                bitwuzla_op = bitwuzla.Kind.BV_ADD
            elif self.op == OP_SUB:
                bitwuzla_op = bitwuzla.Kind.BV_SUB
            elif self.op == OP_MUL:
                bitwuzla_op = bitwuzla.Kind.BV_MUL
            elif self.op == OP_SDIV:
                bitwuzla_op = bitwuzla.Kind.BV_SDIV
            elif self.op == OP_UDIV:
                bitwuzla_op = bitwuzla.Kind.BV_UDIV
            elif self.op == OP_SREM:
                bitwuzla_op = bitwuzla.Kind.BV_SREM
            else:
                assert self.op == OP_UREM
                bitwuzla_op = bitwuzla.Kind.BV_UREM
            self.bitwuzla = tm.mk_term(bitwuzla_op,
                [self.arg1_line.get_bitwuzla(tm), self.arg2_line.get_bitwuzla(tm)])
        return self.bitwuzla

class Concat(Binary):
    keyword = OP_CONCAT

    def __init__(self, nid, op, sid_line, arg1_line, arg2_line, comment, line_no):
        assert op == Concat.keyword
        super().__init__(nid, Concat.keyword, sid_line, arg1_line, arg2_line, comment, line_no)
        if not isinstance(sid_line, Bitvec):
            raise model_error("bitvector result", line_no)
        if not isinstance(arg1_line.sid_line, Bitvec):
            raise model_error("bitvector first operand", line_no)
        if not isinstance(arg2_line.sid_line, Bitvec):
            raise model_error("bitvector second operand", line_no)
        if sid_line.size != arg1_line.sid_line.size + arg2_line.sid_line.size:
            raise model_error("compatible bitvector result", line_no)

    def get_values(self, step):
        if step not in self.cache_values:
            arg1_value = self.arg1_line.get_values(step)
            arg2_value = self.arg2_line.get_values(step)
            if Instance.PROPAGATE_BINARY:
                if isinstance(arg1_value, Values) and isinstance(arg2_value, Values):
                    self.cache_values[step] = arg1_value.Concat(arg2_value, self.sid_line)
                    return self.cache_values[step]
            arg1_value = arg1_value.get_expression()
            arg2_value = arg2_value.get_expression()
            self.cache_values[step] = self.copy(arg1_value, arg2_value)
        return self.cache_values[step]

    def get_z3(self):
        if self.z3 is None:
            self.z3 = z3.Concat(self.arg1_line.get_z3(), self.arg2_line.get_z3())
        return self.z3

    def get_bitwuzla(self, tm):
        if self.bitwuzla is None:
            self.bitwuzla = tm.mk_term(bitwuzla.Kind.BV_CONCAT,
                [self.arg1_line.get_bitwuzla(tm), self.arg2_line.get_bitwuzla(tm)])
        return self.bitwuzla

class Read(Binary):
    keyword = OP_READ

    READ_ARRAY_ITERATIVELY = True

    def __init__(self, nid, op, sid_line, arg1_line, arg2_line, comment, line_no):
        assert op == Read.keyword
        super().__init__(nid, Read.keyword, sid_line, arg1_line, arg2_line, comment, line_no)
        if not isinstance(arg1_line.sid_line, Array):
            raise model_error("array first operand", line_no)
        if not arg1_line.sid_line.array_size_line.match_sorts(arg2_line.sid_line):
            raise model_error("compatible first operand array size and second operand sorts", line_no)
        if not sid_line.match_sorts(arg1_line.sid_line.element_size_line):
            raise model_error("compatible result and first operand element size sorts", line_no)
        self.read_cache = None

    def read_array_iterative(self, array_line, index_line):
        for index in range(2**array_line.sid_line.array_size_line.size):
            if index == 0:
                read_line = array_line.get_mapped_array_expression_for(0)
            else:
                read_line = Ite(next_nid(), self.sid_line,
                    Comparison(next_nid(), OP_EQ, Bool.boolean,
                        index_line,
                        Constd(next_nid(), index_line.sid_line,
                            index, f"index {index}", self.line_no),
                        f"is address equal to index {index}?", self.line_no),
                    array_line.get_mapped_array_expression_for(index),
                    read_line,
                    f"read value from {array_line.comment[2:]} @ address if equal to index {index}", self.line_no)
        return read_line

    def read_array_recursive(self, array_line, index_line, index_array, zero_line):
        assert 2 <= len(index_array) == 2**math.log2(len(index_array))
        if len(index_array) == 2:
            even_line = array_line.get_mapped_array_expression_for(index_array[0])
            odd_line = array_line.get_mapped_array_expression_for(index_array[1])
        else:
            even_line = self.read_array_recursive(array_line, index_line,
                index_array[0:len(index_array)//2], zero_line)
            odd_line = self.read_array_recursive(array_line, index_line,
                index_array[len(index_array)//2:len(index_array)], zero_line)
        address_bit = int(math.log2(len(index_array))) - 1
        return Ite(next_nid(), self.sid_line,
            Comparison(next_nid(), OP_EQ, Bool.boolean,
                Slice(next_nid(), zero_line.sid_line, index_line,
                    address_bit, address_bit,
                    f"extract {address_bit}-th address bit", self.line_no),
                zero_line,
                f"is {address_bit}-th address bit set?", self.line_no),
            even_line,
            odd_line,
            f"read value from {array_line.comment[2:]} @ reset or set {address_bit}-th address bit", self.line_no)

    def read_array(self, array_line, index_line):
        if array_line.sid_line.is_mapped_array():
            if isinstance(index_line, Constant):
                return array_line.get_mapped_array_expression_for(index_line.value)
            else:
                if Read.READ_ARRAY_ITERATIVELY:
                    return self.read_array_iterative(array_line, index_line)
                else:
                    return self.read_array_recursive(array_line, index_line,
                        list(range(2**array_line.sid_line.array_size_line.size)),
                        Zero(next_nid(),
                            Bitvec(next_nid(), 1, "1-bit bitvector for testing bits", self.line_no),
                            "", "zero value for testing bits", self.line_no))
        else:
            return self.copy(array_line.get_mapped_array_expression_for(None), index_line)

    def get_mapped_array_expression_for(self, index):
        assert index is None
        if self.read_cache is None: # avoids quadratic blowup in mapped array size
            arg1_line = self.arg1_line # map later when index is known
            arg2_line = self.arg2_line.get_mapped_array_expression_for(None)
            self.read_cache = self.read_array(arg1_line, arg2_line)
        return self.read_cache

    def get_values(self, step):
        if step not in self.cache_values:
            arg1_value = self.arg1_line.get_values(step).get_expression()
            arg2_value = self.arg2_line.get_values(step).get_expression()
            self.cache_values[step] = self.copy(arg1_value, arg2_value)
        return self.cache_values[step]

    def get_z3(self):
        if self.z3 is None:
            self.z3 = z3.Select(self.arg1_line.get_z3(), self.arg2_line.get_z3())
        return self.z3

    def get_bitwuzla(self, tm):
        if self.bitwuzla is None:
            self.bitwuzla = tm.mk_term(bitwuzla.Kind.ARRAY_SELECT,
                [self.arg1_line.get_bitwuzla(tm), self.arg2_line.get_bitwuzla(tm)])
        return self.bitwuzla

class Ternary(Expression):
    keywords = {OP_ITE, OP_WRITE}

    def __init__(self, nid, op, sid_line, arg1_line, arg2_line, arg3_line, comment, line_no):
        super().__init__(nid, sid_line, arg1_line.domain | arg2_line.domain | arg3_line.domain,
            max(arg1_line.depth, arg2_line.depth, arg3_line.depth) + 1, comment, line_no)
        assert op in Ternary.keywords
        self.op = op
        self.arg1_line = arg1_line
        self.arg2_line = arg2_line
        self.arg3_line = arg3_line
        if not isinstance(arg1_line, Expression):
            raise model_error("expression first operand", line_no)
        if not isinstance(arg2_line, Expression):
            raise model_error("expression second operand", line_no)
        if not isinstance(arg3_line, Expression):
            raise model_error("expression third operand", line_no)

    def __str__(self):
        return f"{self.nid} {self.op} {self.sid_line.nid} {self.arg1_line.nid} {self.arg2_line.nid} {self.arg3_line.nid} {self.comment}"

class Ite(Ternary):
    keyword = OP_ITE

    branching_conditions = None
    non_branching_conditions = None

    def __init__(self, nid, sid_line, arg1_line, arg2_line, arg3_line, comment, line_no):
        super().__init__(nid, Ite.keyword, sid_line, arg1_line, arg2_line, arg3_line, comment, line_no)
        if not isinstance(arg1_line.sid_line, Bool):
            raise model_error("Boolean first operand", line_no)
        if not sid_line.match_sorts(arg2_line.sid_line):
            raise model_error("compatible result and second operand sorts", line_no)
        if not arg2_line.sid_line.match_sorts(arg3_line.sid_line):
            raise model_error("compatible second and third operand sorts", line_no)
        self.ite_cache = {}
        self.instance = Instance(self)
        if Ite.branching_conditions is None and comment == "; branch true condition":
            Ite.branching_conditions = self
        elif Ite.non_branching_conditions is None and comment == "; branch false condition":
            Ite.non_branching_conditions = self

    def copy(self, arg1_line, arg2_line, arg3_line):
        if self.arg1_line is not arg1_line or self.arg2_line is not arg2_line or self.arg3_line is not arg3_line:
            Expression.total_number_of_generated_expressions += 1
            return Ite(next_nid(), arg2_line.sid_line, arg1_line, arg2_line, arg3_line, self.comment, self.line_no)
        else:
            return self

    def get_mapped_array_expression_for(self, index):
        if index not in self.ite_cache:
            arg1_line = self.arg1_line.get_mapped_array_expression_for(None)
            arg2_line = self.arg2_line.get_mapped_array_expression_for(index)
            arg3_line = self.arg3_line.get_mapped_array_expression_for(index)
            self.ite_cache[index] = self.copy(arg1_line, arg2_line, arg3_line)
        return self.ite_cache[index]

    def get_values(self, step):
        if step not in self.cache_values:
            arg1_value = self.arg1_line.get_values(step)
            if Instance.PROPAGATE_ITE and isinstance(arg1_value, Values):
                false_constraint, true_constraint = arg1_value.get_boolean_constraints()
                if BVDD.is_always_false(false_constraint):
                    arg2_value = self.arg2_line.get_values(step)
                    if isinstance(arg2_value, Values):
                        self.cache_values[step] = arg1_value.If(arg2_value, None)
                        return self.cache_values[step]
                    elif BVDD.is_always_true(true_constraint):
                        # true case holds unconditionally
                        self.cache_values[step] = arg2_value.get_expression()
                        return self.cache_values[step]
                    else:
                        # lazy evaluation of false case into expression
                        arg3_value = self.arg3_line.get_values(step)
                elif BVDD.is_always_false(true_constraint):
                    arg3_value = self.arg3_line.get_values(step)
                    if isinstance(arg3_value, Values):
                        self.cache_values[step] = arg1_value.If(None, arg3_value)
                        return self.cache_values[step]
                    elif BVDD.is_always_true(false_constraint):
                        # false case holds unconditionally
                        self.cache_values[step] = arg3_value.get_expression()
                        return self.cache_values[step]
                    else:
                        # lazy evaluation of true case into expression
                        arg2_value = self.arg2_line.get_values(step)
                else:
                    # lazy evaluation of true and false case
                    arg2_value = self.arg2_line.get_values(step)
                    arg3_value = self.arg3_line.get_values(step)
                    if isinstance(arg2_value, Values) and isinstance(arg3_value, Values):
                        self.cache_values[step] = arg1_value.If(arg2_value, arg3_value)
                        return self.cache_values[step]
            else:
                arg2_value = self.arg2_line.get_values(step)
                arg3_value = self.arg3_line.get_values(step)
            arg1_value = arg1_value.get_expression()
            arg2_value = arg2_value.get_expression()
            arg3_value = arg3_value.get_expression()
            self.cache_values[step] = self.copy(arg1_value, arg2_value, arg3_value)
        return self.cache_values[step]

    def get_z3(self):
        if self.z3 is None:
            self.z3 = z3.If(self.arg1_line.get_z3(), self.arg2_line.get_z3(), self.arg3_line.get_z3())
        return self.z3

    def get_z3_step(self, step):
        # only needed for branching
        self.instance.set_instance(self, step)
        return self.instance.get_z3_instance(step)

    def get_bitwuzla(self, tm):
        if self.bitwuzla is None:
            self.bitwuzla = tm.mk_term(bitwuzla.Kind.ITE, [self.arg1_line.get_bitwuzla(tm),
                self.arg2_line.get_bitwuzla(tm), self.arg3_line.get_bitwuzla(tm)])
        return self.bitwuzla

    def get_bitwuzla_step(self, step, tm):
        # only needed for branching
        self.instance.set_instance(self, step)
        return self.instance.get_bitwuzla_instance(step, tm)

class Write(Ternary):
    keyword = OP_WRITE

    def __init__(self, nid, sid_line, arg1_line, arg2_line, arg3_line, comment, line_no):
        super().__init__(nid, Write.keyword, sid_line, arg1_line, arg2_line, arg3_line, comment, line_no)
        if not isinstance(sid_line, Array):
            raise model_error("array result", line_no)
        if not sid_line.match_sorts(arg1_line.sid_line):
            raise model_error("compatible result and first operand sorts", line_no)
        if not arg1_line.sid_line.array_size_line.match_sorts(arg2_line.sid_line):
            raise model_error("compatible first operand array size and second operand sorts", line_no)
        if not arg1_line.sid_line.element_size_line.match_sorts(arg3_line.sid_line):
            raise model_error("compatible first operand element size and third operand sorts", line_no)
        self.write_cache = {}

    def copy(self, arg1_line, arg2_line, arg3_line):
        if self.arg1_line is not arg1_line or self.arg2_line is not arg2_line or self.arg3_line is not arg3_line:
            Expression.total_number_of_generated_expressions += 1
            return Write(next_nid(), arg1_line.sid_line, arg1_line, arg2_line, arg3_line, self.comment, self.line_no)
        else:
            return self

    def write_array(self, array_line, index_line, value_line, index):
        if self.sid_line.is_mapped_array():
            assert index is not None
            if isinstance(index_line, Constant):
                if index_line.value == index:
                    return value_line
                else:
                    return array_line
            else:
                return Ite(next_nid(), value_line.sid_line,
                    Comparison(next_nid(), OP_EQ, Bool.boolean,
                        index_line,
                        Constd(next_nid(), index_line.sid_line,
                            index, f"index {index}", self.line_no),
                        f"is address equal to index {index}?", self.line_no),
                    value_line,
                    array_line,
                    f"write value to {array_line.comment[2:]} @ address if equal to index {index}", self.line_no)
        else:
            assert index is None
            return self.copy(array_line, index_line, value_line)

    def get_mapped_array_expression_for(self, index):
        if index not in self.write_cache:
            arg1_line = self.arg1_line.get_mapped_array_expression_for(index)
            arg2_line = self.arg2_line.get_mapped_array_expression_for(None)
            arg3_line = self.arg3_line.get_mapped_array_expression_for(None)
            self.write_cache[index] = self.write_array(arg1_line, arg2_line, arg3_line, index)
        return self.write_cache[index]

    def get_values(self, step):
        if step not in self.cache_values:
            arg1_value = self.arg1_line.get_values(step).get_expression()
            arg2_value = self.arg2_line.get_values(step).get_expression()
            arg3_value = self.arg3_line.get_values(step).get_expression()
            self.cache_values[step] = self.copy(arg1_value, arg2_value, arg3_value)
        return self.cache_values[step]

    def get_z3(self):
        if self.z3 is None:
            self.z3 = z3.Store(self.arg1_line.get_z3(), self.arg2_line.get_z3(), self.arg3_line.get_z3())
        return self.z3

    def get_bitwuzla(self, tm):
        if self.bitwuzla is None:
            self.bitwuzla = tm.mk_term(bitwuzla.Kind.ARRAY_STORE,
                [self.arg1_line.get_bitwuzla(tm),
                self.arg2_line.get_bitwuzla(tm),
                self.arg3_line.get_bitwuzla(tm)])
        return self.bitwuzla

class Transitional(Line):
    def __init__(self, nid, sid_line, state_line, exp_line, symbol, comment, line_no, array_line, index):
        super().__init__(nid, comment, line_no)
        self.sid_line = sid_line
        self.state_line = state_line
        self.exp_line = exp_line
        self.symbol = symbol
        if not isinstance(sid_line, Sort):
            raise model_error("sort", line_no)
        if not isinstance(state_line, State):
            raise model_error("state operand", line_no)
        if not isinstance(exp_line, Expression):
            raise model_error("expression operand", line_no)
        if not self.sid_line.match_sorts(state_line.sid_line):
            raise model_error("compatible line and state sorts", line_no)
        if not state_line.sid_line.match_init_sorts(exp_line.sid_line):
            raise model_error("compatible state and expression sorts", line_no)
        self.new_mapped_array(array_line, index)

    def new_mapped_array(self, array_line, index):
        self.array_line = array_line
        self.index = index
        if index is not None:
            if not isinstance(self.sid_line, Bitvector):
                raise model_error("bitvector", self.line_no)
        elif self.sid_line.is_mapped_array():
            self.array = {}
            for index in self.state_line.array.keys():
                self.array[index] = type(self)(self.nid + index + 1, self.sid_line.element_size_line,
                    self.state_line.array[index], self.state_line.array[index], self.symbol,
                    f"{self.comment} @ index {index}", self.line_no, self, index)

    def set_mapped_array_expression(self):
        if self.index is None:
            self.exp_line = self.exp_line.get_mapped_array_expression_for(None)
        else:
            self.exp_line = self.array_line.exp_line.get_mapped_array_expression_for(self.index)

    def remove_transition(state_line, transitions):
        for key in transitions.keys():
            if transitions[key].state_line is state_line:
                del transitions[key]
                return

    def new_transition(self, transitions, index):
        if index is not None or not self.sid_line.is_mapped_array():
            assert self.nid not in transitions, f"transition nid {self.nid} already defined @ {self.line_no}"
            transitions[self.nid] = self

class Init(Transitional):
    keyword = OP_INIT

    inits = {}

    def __init__(self, nid, sid_line, state_line, exp_line, symbol, comment, line_no, array_line = None, index = None):
        if isinstance(state_line.sid_line, Array) and isinstance(exp_line, Constant):
            exp_line = Constant_Array(state_line.sid_line, exp_line)
        super().__init__(nid, sid_line, state_line, exp_line, symbol, comment, line_no, array_line, index)
        if state_line.nid < exp_line.nid:
            raise model_error("state after expression", line_no)
        if isinstance(state_line, Input):
            raise model_error("state, not input", line_no)
        if self.state_line.init_line is None:
            self.state_line.init_line = self
        else:
            raise model_error("uninitialized state", line_no)
        self.new_transition(Init.inits, index)

    def __str__(self):
        if self.symbol:
            return f"{self.nid} {Init.keyword} {self.sid_line.nid} {self.state_line.nid} {self.exp_line.nid} {self.symbol} {self.comment}"
        else:
            return f"{self.nid} {Init.keyword} {self.sid_line.nid} {self.state_line.nid} {self.exp_line.nid} {self.comment}"

    def get_z3_step(self, step):
        assert step == 0, f"z3 init with {step} != 0"
        self.state_line.set_instance(self.exp_line, -1)
        if Instance.PROPAGATE is not None:
            return z3.BoolVal(True)
        else:
            return self.state_line.get_z3_name(0) == self.state_line.get_z3_instance(-1)

    def get_bitwuzla_step(self, step, tm):
        assert step == 0, f"bitwuzla init with {step} != 0"
        self.state_line.set_instance(self.exp_line, -1)
        if Instance.PROPAGATE is not None:
            return tm.mk_true()
        else:
            return tm.mk_term(bitwuzla.Kind.EQUAL,
                [self.state_line.get_bitwuzla_name(0, tm),
                self.state_line.get_bitwuzla_instance(-1, tm)])

class Next(Transitional):
    keyword = OP_NEXT

    nexts = {}

    def __init__(self, nid, sid_line, state_line, exp_line, symbol, comment, line_no, array_line = None, index = None):
        super().__init__(nid, sid_line, state_line, exp_line, symbol, comment, line_no, array_line, index)
        self.cache_z3_next_state = {}
        self.cache_z3_is_state_changing = {}
        self.cache_z3_state_is_not_changing = {}
        self.cache_bitwuzla_next_state = {}
        self.cache_bitwuzla_is_state_changing = {}
        self.cache_bitwuzla_state_is_not_changing = {}
        if self.state_line.next_line is None:
            self.state_line.next_line = self
        else:
            raise model_error("untransitioned state", line_no)
        self.new_transition(Next.nexts, index)

    def __str__(self):
        if self.symbol:
            return f"{self.nid} {Next.keyword} {self.sid_line.nid} {self.state_line.nid} {self.exp_line.nid} {self.symbol} {self.comment}"
        else:
            return f"{self.nid} {Next.keyword} {self.sid_line.nid} {self.state_line.nid} {self.exp_line.nid} {self.comment}"

    def get_z3_step(self, step):
        if step not in self.cache_z3_next_state:
            self.state_line.set_instance(self.exp_line, step)
            if Instance.PROPAGATE is not None:
                self.cache_z3_next_state[step] = z3.BoolVal(True)
            else:
                self.cache_z3_next_state[step] = self.state_line.get_z3_name(step + 1) == self.state_line.get_z3_instance(step)
        return self.cache_z3_next_state[step]

    def get_z3_is_state_changing(self, step):
        if step not in self.cache_z3_is_state_changing:
            self.state_line.set_instance(self.exp_line, step)
            if self.state_line.get_instance(step).is_equal(self.state_line.get_instance(step - 1)):
                self.cache_z3_is_state_changing[step] = z3.BoolVal(False)
            else:
                self.cache_z3_is_state_changing[step] = self.state_line.get_z3_instance(step) != self.state_line.get_z3_instance(step - 1)
        return self.cache_z3_is_state_changing[step]

    def get_z3_state_is_not_changing(self, step):
        if step not in self.cache_z3_state_is_not_changing:
            if Instance.PROPAGATE is not None:
                self.state_line.set_instance(self.exp_line, step)
                self.cache_z3_state_is_not_changing[step] = self.state_line.get_z3_instance(step) == self.state_line.get_z3_instance(step - 1)
            else:
                self.state_line.set_instance(self.state_line, step)
                self.cache_z3_state_is_not_changing[step] = self.state_line.get_z3_name(step + 1) == self.state_line.get_z3_name(step)
        return self.cache_z3_state_is_not_changing[step]

    def get_bitwuzla_step(self, step, tm):
        if step not in self.cache_bitwuzla_next_state:
            self.state_line.set_instance(self.exp_line, step)
            if Instance.PROPAGATE is not None:
                self.cache_bitwuzla_next_state[step] = tm.mk_true()
            else:
                self.cache_bitwuzla_next_state[step] = tm.mk_term(bitwuzla.Kind.EQUAL,
                    [self.state_line.get_bitwuzla_name(step + 1, tm),
                    self.state_line.get_bitwuzla_instance(step, tm)])
        return self.cache_bitwuzla_next_state[step]

    def get_bitwuzla_is_state_changing(self, step, tm):
        if step not in self.cache_bitwuzla_is_state_changing:
            self.state_line.set_instance(self.exp_line, step)
            if self.state_line.get_instance(step).is_equal(self.state_line.get_instance(step - 1)):
                self.cache_bitwuzla_is_state_changing[step] = tm.mk_false()
            else:
                self.cache_bitwuzla_is_state_changing[step] = tm.mk_term(bitwuzla.Kind.DISTINCT,
                    [self.state_line.get_bitwuzla_instance(step, tm),
                    self.state_line.get_bitwuzla_instance(step - 1, tm)])
        return self.cache_bitwuzla_is_state_changing[step]

    def get_bitwuzla_state_is_not_changing(self, step, tm):
        if step not in self.cache_bitwuzla_state_is_not_changing:
            if Instance.PROPAGATE is not None:
                self.state_line.set_instance(self.exp_line, step)
                self.cache_bitwuzla_state_is_not_changing[step] = tm.mk_term(bitwuzla.Kind.EQUAL,
                    [self.state_line.get_bitwuzla_instance(step, tm),
                    self.state_line.get_bitwuzla_instance(step - 1, tm)])
            else:
                self.state_line.set_instance(self.state_line, step)
                self.cache_bitwuzla_state_is_not_changing[step] = tm.mk_term(bitwuzla.Kind.EQUAL,
                    [self.state_line.get_bitwuzla_name(step + 1, tm),
                    self.state_line.get_bitwuzla_name(step, tm)])
        return self.cache_bitwuzla_state_is_not_changing[step]

class Property(Line):
    keywords = {OP_CONSTRAINT, OP_BAD}

    def __init__(self, nid, property_line, symbol, comment, line_no):
        super().__init__(nid, comment, line_no)
        self.property_line = property_line
        self.symbol = symbol
        self.instance = Instance(self)
        if not isinstance(property_line, Expression):
            raise model_error("expression operand", line_no)
        if not isinstance(property_line.sid_line, Bool):
            raise model_error("Boolean operand", line_no)

    def set_mapped_array_expression(self):
        self.property_line = self.property_line.get_mapped_array_expression_for(None)

    def get_z3_step(self, step):
        self.instance.set_instance(self.property_line, step)
        return self.instance.get_z3_instance(step)

    def get_bitwuzla_step(self, step, tm):
        self.instance.set_instance(self.property_line, step)
        return self.instance.get_bitwuzla_instance(step, tm)

class Constraint(Property):
    keyword = OP_CONSTRAINT

    constraints = {}

    def __init__(self, nid, property_line, symbol, comment, line_no):
        super().__init__(nid, property_line, symbol, comment, line_no)
        self.new_constraint()

    def __str__(self):
        return f"{self.nid} {Constraint.keyword} {self.property_line.nid} {self.symbol} {self.comment}"

    def new_constraint(self):
        assert self not in Constraint.constraints, f"constraint nid {self.nid} already defined @ {self.line_no}"
        Constraint.constraints[self.nid] = self

class Bad(Property):
    keyword = OP_BAD

    bads = {}

    def __init__(self, nid, property_line, symbol, comment, line_no):
        super().__init__(nid, property_line, symbol, comment, line_no)
        self.new_bad()

    def __str__(self):
        return f"{self.nid} {Bad.keyword} {self.property_line.nid} {self.symbol} {self.comment}"

    def new_bad(self):
        assert self.nid not in Bad.bads, f"bad nid {self.nid} already defined @ {self.line_no}"
        Bad.bads[self.nid] = self

def get_class(keyword):
    if keyword == Zero.keyword:
        return Zero
    elif keyword == One.keyword:
        return One
    elif keyword == Constd.keyword:
        return Constd
    elif keyword == Const.keyword:
        return Const
    elif keyword == Consth.keyword:
        return Consth
    elif keyword == Input.keyword:
        return Input
    elif keyword == State.keyword:
        return State
    elif keyword in Ext.keywords:
        return Ext
    elif keyword == Slice.keyword:
        return Slice
    elif keyword in Unary.keywords:
        return Unary
    elif keyword == Implies.keyword:
        return Implies
    elif keyword in Comparison.keywords:
        return Comparison
    elif keyword in Logical.keywords:
        return Logical
    elif keyword in Computation.keywords:
        return Computation
    elif keyword == Concat.keyword:
        return Concat
    elif keyword == Read.keyword:
        return Read
    elif keyword == Ite.keyword:
        return Ite
    elif keyword == Write.keyword:
        return Write
    elif keyword == Init.keyword:
        return Init
    elif keyword == Next.keyword:
        return Next
    elif keyword == Constraint.keyword:
        return Constraint
    elif keyword == Bad.keyword:
        return Bad

def new_boolean(nid = None, line_no = None):
    return Bool(next_nid(nid), "Boolean", line_no)

def new_bitvec(size_in_bits, comment, nid = None, line_no = None):
    return Bitvec(next_nid(nid), size_in_bits, comment, line_no)

def new_array(address_sid, element_sid, comment, nid = None, line_no = None):
    return Array(next_nid(nid), address_sid, element_sid, comment, line_no)

def new_zero_one(op, sid, symbol, comment, nid = None, line_no = None):
    assert op in {OP_ZERO, OP_ONE}
    return get_class(op)(next_nid(nid), sid, symbol, comment, line_no)

def new_constant(op, sid, constant, comment, nid = None, line_no = None):
    assert op in {OP_CONSTD, OP_CONST, OP_CONSTH}
    if op == OP_CONSTD:
        if constant == 0:
            return Zero(next_nid(nid), sid, "", comment, line_no)
        elif constant == 1:
            return One(next_nid(nid), sid, "", comment, line_no)
    return get_class(op)(next_nid(nid), sid, constant, comment, line_no)

def new_input(op, sid, symbol, comment, nid = None, line_no = None):
    assert op in Variable.keywords
    return get_class(op)(next_nid(nid), sid, symbol, comment, line_no)

def new_ext(op, sid, value_nid, w, comment, nid = None, line_no = None):
    assert op in Ext.keywords
    return get_class(op)(next_nid(nid), op, sid, value_nid, w, comment, line_no)

def new_slice(sid, value_nid, u, l, comment, nid = None, line_no = None):
    return Slice(next_nid(nid), sid, value_nid, u, l, comment, line_no)

def new_unary(op, sid, value_nid, comment, nid = None, line_no = None):
    assert op in Unary.keywords
    return get_class(op)(next_nid(nid), op, sid, value_nid, comment, line_no)

def new_unary_boolean(op, value_nid, comment, nid = None, line_no = None):
    assert op == OP_NOT
    return get_class(op)(next_nid(nid), op, SID_BOOLEAN, value_nid, comment, line_no)

def new_binary(op, sid, left_nid, right_nid, comment, nid = None, line_no = None):
    assert op in Binary.keywords
    return get_class(op)(next_nid(nid), op, sid, left_nid, right_nid, comment, line_no)

def new_binary_boolean(op, left_nid, right_nid, comment, nid = None, line_no = None):
    assert op in Implies.keyword + Comparison.keywords + Logical.keywords
    return get_class(op)(next_nid(nid), op, SID_BOOLEAN, left_nid, right_nid, comment, line_no)

def new_ternary(op, sid, first_nid, second_nid, third_nid, comment, nid = None, line_no = None):
    assert op in Ternary.keywords
    return get_class(op)(next_nid(nid), sid, first_nid, second_nid, third_nid, comment, line_no)

def new_init(sid, state_nid, value_nid, comment, nid = None, line_no = None):
    return Init(next_nid(nid), sid, state_nid, value_nid, comment, line_no)

def new_next(sid, state_nid, value_nid, comment, nid = None, line_no = None):
    return Next(next_nid(nid), sid, state_nid, value_nid, comment, line_no)

def new_init_next(op, sid, state_nid, value_nid, symbol, comment, nid = None, line_no = None):
    return get_class(op)(next_nid(nid), sid, state_nid, value_nid, symbol, comment, line_no)

def new_property(op, condition_nid, symbol, comment, nid = None, line_no = None):
    assert op in Property.keywords
    return get_class(op)(next_nid(nid), condition_nid, symbol, comment, line_no)

# RISC-V model generator

UNUSED = None

class system_error(Exception):
    def __init__(self, message):
        super().__init__(message)

# TODO: configure:

IS64BITTARGET = True

SIZEOFUINT64INBITS = 64

# avoiding 64-bit integer overflow
UINT64_MAX = ((2**(SIZEOFUINT64INBITS - 1) - 1) << 1) + 1

WORDSIZE       = 8
WORDSIZEINBITS = 64

INSTRUCTIONSIZE = 4

VIRTUALMEMORYSIZE = 4 # 4GB avoiding 32-bit integer overflow
GIGABYTE = 2**30

# unsigned integer arithmetic support

def is_unsigned_integer(n, b):
    assert 0 < b <= SIZEOFUINT64INBITS
    if b == SIZEOFUINT64INBITS:
        # avoiding 64-bit integer overflow
        return 0 <= n <= UINT64_MAX
    else:
        return 0 <= n < 2**b

def is_uint64(n):
    return is_unsigned_integer(n, SIZEOFUINT64INBITS)

def is_int64(n):
    return is_signed_integer(n, SIZEOFUINT64INBITS)

# ported from selfie library

def get_bits(n, i, b):
    assert is_uint64(n)
    assert 0 <= i + b <= SIZEOFUINT64INBITS
    assert 0 < b
    if b < SIZEOFUINT64INBITS:
        return (n >> i) % 2**b
    else:
        # avoiding 64-bit integer overflow
        return n >> i

def is_signed_integer(n, b):
    assert is_uint64(n)
    assert 0 < b <= SIZEOFUINT64INBITS
    # avoiding 64-bit integer overflow
    return 0 <= n < 2**(b - 1) or UINT64_MAX - 2**(b - 1) <= n - 1 < UINT64_MAX

def sign_shrink(n, b):
    assert is_uint64(n)
    assert 0 < b <= SIZEOFUINT64INBITS
    return get_bits(n, 0, b)

# ported from rotor model

def get_sid(line):
    return line.sid_line

# ported from rotor emulator

def eval_bitvec_size(line):
    assert isinstance(line, Bitvec)
    # TODO: tolerating but not yet supporting double machine word bitvectors
    assert (line.size > 0 and line.size <= SIZEOFUINT64INBITS) or line.size == 2 * WORDSIZEINBITS
    return line.size;

def fit_bitvec_sort(sid, value):
    size = eval_bitvec_size(sid)
    if size >= SIZEOFUINT64INBITS:
        # TODO: support of bitvectors larger than machine words
        return
    elif is_unsigned_integer(value, size):
        return
    raise system_error(f"{value} does not fit {size}-bit bitvector")

def signed_fit_bitvec_sort(sid, value):
    size = eval_bitvec_size(sid)
    if size >= SIZEOFUINT64INBITS:
        # TODO: support of bitvectors larger than machine words
        return
    elif is_signed_integer(value, size):
        return
    fit_bitvec_sort(sid, value)

def eval_constant_value(line):
    # TODO: check if really needed
    assert isinstance(line, Constant)
    sid   = get_sid(line)
    value = line.value
    if isinstance(line, Constd):
        signed_fit_bitvec_sort(sid, value)
        value = sign_shrink(value, eval_bitvec_size(sid))
    else:
        fit_bitvec_sort(sid, value)
    return value

# machine interface

HALFWORDSIZEINBITS = 16

SINGLEWORDSIZEINBITS = 32

DOUBLEWORDSIZE = 8
DOUBLEWORDSIZEINBITS = 64

def init_machine_interface():
    global SID_BOOLEAN

    global NID_FALSE
    global NID_TRUE

    global SID_BYTE

    global NID_BYTE_0
    global NID_BYTE_3

    global SID_HALF_WORD

    global NID_HALF_WORD_0
    global NID_HALF_WORD_1

    global SID_SINGLE_WORD

    global NID_SINGLE_WORD_0
    global NID_SINGLE_WORD_1
    global NID_SINGLE_WORD_2
    global NID_SINGLE_WORD_3
    global NID_SINGLE_WORD_4
    global NID_SINGLE_WORD_5
    global NID_SINGLE_WORD_6
    global NID_SINGLE_WORD_7
    global NID_SINGLE_WORD_8

    global NID_SINGLE_WORD_MINUS_1
    global NID_SINGLE_WORD_INT_MIN

    global SID_DOUBLE_WORD

    global NID_DOUBLE_WORD_0
    global NID_DOUBLE_WORD_1
    global NID_DOUBLE_WORD_2
    global NID_DOUBLE_WORD_3
    global NID_DOUBLE_WORD_4
    global NID_DOUBLE_WORD_5
    global NID_DOUBLE_WORD_6
    global NID_DOUBLE_WORD_7
    global NID_DOUBLE_WORD_8

    global NID_DOUBLE_WORD_MINUS_1
    global NID_DOUBLE_WORD_INT_MIN

    global SID_MACHINE_WORD

    global NID_MACHINE_WORD_0
    global NID_MACHINE_WORD_1
    global NID_MACHINE_WORD_2
    global NID_MACHINE_WORD_3
    global NID_MACHINE_WORD_4
    global NID_MACHINE_WORD_5
    global NID_MACHINE_WORD_6
    global NID_MACHINE_WORD_7
    global NID_MACHINE_WORD_8

    global NID_MACHINE_WORD_MINUS_1
    global NID_MACHINE_WORD_INT_MIN

    global NID_LSB_MASK

    global SID_DOUBLE_MACHINE_WORD

    SID_BOOLEAN = new_boolean()

    NID_FALSE = new_constant(OP_CONSTD, SID_BOOLEAN, 0, "false")
    NID_TRUE = new_constant(OP_CONSTD, SID_BOOLEAN, 1, "true")

    SID_BYTE = new_bitvec(8, "8-bit byte")

    NID_BYTE_0 = new_constant(OP_CONSTD, SID_BYTE, 0, "byte 0")
    NID_BYTE_3 = new_constant(OP_CONSTD, SID_BYTE, 3, "byte 3")

    SID_HALF_WORD = new_bitvec(HALFWORDSIZEINBITS, "16-bit half word")

    NID_HALF_WORD_0 = new_constant(OP_CONSTD, SID_HALF_WORD, 0, "half word 0")
    NID_HALF_WORD_1 = new_constant(OP_CONSTD, SID_HALF_WORD, 1, "half word 1")

    SID_SINGLE_WORD = new_bitvec(SINGLEWORDSIZEINBITS, "32-bit single word")

    NID_SINGLE_WORD_0 = new_constant(OP_CONSTD, SID_SINGLE_WORD, 0, "single-word 0")
    NID_SINGLE_WORD_1 = new_constant(OP_CONSTD, SID_SINGLE_WORD, 1, "single-word 1")
    NID_SINGLE_WORD_2 = new_constant(OP_CONSTD, SID_SINGLE_WORD, 2, "single-word 2")
    NID_SINGLE_WORD_3 = new_constant(OP_CONSTD, SID_SINGLE_WORD, 3, "single-word 3")
    NID_SINGLE_WORD_4 = new_constant(OP_CONSTD, SID_SINGLE_WORD, 4, "single-word 4")
    NID_SINGLE_WORD_5 = new_constant(OP_CONSTD, SID_SINGLE_WORD, 5, "single-word 5")
    NID_SINGLE_WORD_6 = new_constant(OP_CONSTD, SID_SINGLE_WORD, 6, "single-word 6")
    NID_SINGLE_WORD_7 = new_constant(OP_CONSTD, SID_SINGLE_WORD, 7, "single-word 7")
    NID_SINGLE_WORD_8 = new_constant(OP_CONSTD, SID_SINGLE_WORD, 8, "single-word 8")

    NID_SINGLE_WORD_MINUS_1 = new_constant(OP_CONSTD, SID_SINGLE_WORD, -1, "single-word -1")
    NID_SINGLE_WORD_INT_MIN = new_constant(OP_CONSTH, SID_SINGLE_WORD, 2**(SINGLEWORDSIZEINBITS - 1), "single-word INT_MIN")

    SID_DOUBLE_WORD = new_bitvec(DOUBLEWORDSIZEINBITS, "64-bit double word")

    NID_DOUBLE_WORD_0 = new_constant(OP_CONSTD, SID_DOUBLE_WORD, 0, "double-word 0")
    NID_DOUBLE_WORD_1 = new_constant(OP_CONSTD, SID_DOUBLE_WORD, 1, "double-word 1")
    NID_DOUBLE_WORD_2 = new_constant(OP_CONSTD, SID_DOUBLE_WORD, 2, "double-word 2")
    NID_DOUBLE_WORD_3 = new_constant(OP_CONSTD, SID_DOUBLE_WORD, 3, "double-word 3")
    NID_DOUBLE_WORD_4 = new_constant(OP_CONSTD, SID_DOUBLE_WORD, 4, "double-word 4")
    NID_DOUBLE_WORD_5 = new_constant(OP_CONSTD, SID_DOUBLE_WORD, 5, "double-word 5")
    NID_DOUBLE_WORD_6 = new_constant(OP_CONSTD, SID_DOUBLE_WORD, 6, "double-word 6")
    NID_DOUBLE_WORD_7 = new_constant(OP_CONSTD, SID_DOUBLE_WORD, 7, "double-word 7")
    NID_DOUBLE_WORD_8 = new_constant(OP_CONSTD, SID_DOUBLE_WORD, 8, "double-word 8")

    NID_DOUBLE_WORD_MINUS_1 = new_constant(OP_CONSTD, SID_DOUBLE_WORD, -1, "double-word -1")

    if IS64BITTARGET:
        NID_DOUBLE_WORD_INT_MIN = new_constant(OP_CONSTH, SID_DOUBLE_WORD, 2**(DOUBLEWORDSIZEINBITS - 1), "double-word INT_MIN")

        SID_MACHINE_WORD = SID_DOUBLE_WORD

        NID_MACHINE_WORD_0 = NID_DOUBLE_WORD_0
        NID_MACHINE_WORD_1 = NID_DOUBLE_WORD_1
        NID_MACHINE_WORD_2 = NID_DOUBLE_WORD_2
        NID_MACHINE_WORD_3 = NID_DOUBLE_WORD_3
        NID_MACHINE_WORD_4 = NID_DOUBLE_WORD_4
        NID_MACHINE_WORD_5 = NID_DOUBLE_WORD_5
        NID_MACHINE_WORD_6 = NID_DOUBLE_WORD_6
        NID_MACHINE_WORD_7 = NID_DOUBLE_WORD_7
        NID_MACHINE_WORD_8 = NID_DOUBLE_WORD_8

        NID_MACHINE_WORD_MINUS_1 = NID_DOUBLE_WORD_MINUS_1
        NID_MACHINE_WORD_INT_MIN = NID_DOUBLE_WORD_INT_MIN
    else:
        # 32-bit system
        SID_MACHINE_WORD = SID_SINGLE_WORD

        NID_MACHINE_WORD_0 = NID_SINGLE_WORD_0
        NID_MACHINE_WORD_1 = NID_SINGLE_WORD_1
        NID_MACHINE_WORD_2 = NID_SINGLE_WORD_2
        NID_MACHINE_WORD_3 = NID_SINGLE_WORD_3
        NID_MACHINE_WORD_4 = NID_SINGLE_WORD_4
        NID_MACHINE_WORD_5 = NID_SINGLE_WORD_5
        NID_MACHINE_WORD_6 = NID_SINGLE_WORD_6
        NID_MACHINE_WORD_7 = NID_SINGLE_WORD_7
        NID_MACHINE_WORD_8 = NID_SINGLE_WORD_8

        NID_MACHINE_WORD_MINUS_1 = NID_SINGLE_WORD_MINUS_1
        NID_MACHINE_WORD_INT_MIN = NID_SINGLE_WORD_INT_MIN

    NID_LSB_MASK = new_constant(OP_CONSTD, SID_MACHINE_WORD, -2, "all bits but LSB set")

    SID_DOUBLE_MACHINE_WORD = new_bitvec(2 * WORDSIZEINBITS, "double machine word")

# kernel interface

MAX_STRING_LENGTH = 128

def init_syscall_IDs():
    global SYSCALL_EXIT
    global SYSCALL_BRK
    global SYSCALL_OPENAT
    global SYSCALL_OPEN
    global SYSCALL_READ
    global SYSCALL_WRITE

    SYSCALL_EXIT   = 93
    SYSCALL_BRK    = 214
    SYSCALL_OPENAT = 56
    SYSCALL_OPEN   = 1024 # legacy syscall
    SYSCALL_READ   = 63
    SYSCALL_WRITE  = 64

BYTES_TO_READ = 1

def init_kernel_interface():
    global NID_MAX_STRING_LENGTH

    global NID_EXIT_SYSCALL_ID
    global NID_BRK_SYSCALL_ID
    global NID_OPENAT_SYSCALL_ID
    global NID_OPEN_SYSCALL_ID
    global NID_READ_SYSCALL_ID
    global NID_WRITE_SYSCALL_ID

    global NID_BYTES_TO_READ

    global INPUT_ADDRESS_SPACE

    global SID_INPUT_ADDRESS
    global SID_INPUT_BUFFER

    init_syscall_IDs()

    NID_MAX_STRING_LENGTH = new_constant(OP_CONSTD, SID_MACHINE_WORD,
        MAX_STRING_LENGTH, "maximum string length")

    NID_EXIT_SYSCALL_ID = new_constant(OP_CONSTD, SID_MACHINE_WORD,
        SYSCALL_EXIT, f"exit syscall ID {SYSCALL_EXIT:b}")
    NID_BRK_SYSCALL_ID = new_constant(OP_CONSTD, SID_MACHINE_WORD,
        SYSCALL_BRK, f"brk syscall ID {SYSCALL_BRK:b}")
    NID_OPENAT_SYSCALL_ID = new_constant(OP_CONSTD, SID_MACHINE_WORD,
        SYSCALL_OPENAT, f"openat syscall ID {SYSCALL_OPENAT:b}")
    NID_OPEN_SYSCALL_ID = new_constant(OP_CONSTD, SID_MACHINE_WORD,
        SYSCALL_OPEN, f"open syscall ID {SYSCALL_OPEN:b}")
    NID_READ_SYSCALL_ID = new_constant(OP_CONSTD, SID_MACHINE_WORD,
        SYSCALL_READ, f"read syscall ID {SYSCALL_READ:b}")
    NID_WRITE_SYSCALL_ID = new_constant(OP_CONSTD, SID_MACHINE_WORD,
        SYSCALL_WRITE, f"write syscall ID {SYSCALL_WRITE:b}")

    NID_BYTES_TO_READ = new_constant(OP_CONSTD, SID_MACHINE_WORD, BYTES_TO_READ, "bytes to read")

    INPUT_ADDRESS_SPACE = calculate_address_space(BYTES_TO_READ, 8)

    SID_INPUT_ADDRESS = new_bitvec(INPUT_ADDRESS_SPACE, f"{INPUT_ADDRESS_SPACE}-bit input address")
    SID_INPUT_BUFFER  = new_array(SID_INPUT_ADDRESS, SID_BYTE, "input buffer")

def get_power_of_two_size_in_bytes(size_in_bits):
    assert size_in_bits % 8 == 0
    size_in_bits = size_in_bits // 8
    assert size_in_bits == 2**int(math.log2(size_in_bits))
    return size_in_bits

def calculate_address_space(number_of_bytes, word_size_in_bits):
    if number_of_bytes < 2 * get_power_of_two_size_in_bytes(word_size_in_bits):
        number_of_bytes = 2 * get_power_of_two_size_in_bytes(word_size_in_bits)

    size_in_words = math.ceil(number_of_bytes / get_power_of_two_size_in_bytes(word_size_in_bits))
    address_space = int(math.log2(size_in_words))

    if size_in_words > 2**address_space:
        address_space += 1

    return address_space

# register sorts and specification

def init_register_IDs():
    global REG_ZR
    global REG_RA
    global REG_SP
    global REG_GP
    global REG_TP
    global REG_T0
    global REG_T1
    global REG_T2
    global REG_S0
    global REG_S1
    global REG_A0
    global REG_A1
    global REG_A2
    global REG_A3
    global REG_A4
    global REG_A5
    global REG_A6
    global REG_A7
    global REG_S2
    global REG_S3
    global REG_S4
    global REG_S5
    global REG_S6
    global REG_S7
    global REG_S8
    global REG_S9
    global REG_S10
    global REG_S11
    global REG_T3
    global REG_T4
    global REG_T5
    global REG_T6

    REG_ZR  = 0
    REG_RA  = 1
    REG_SP  = 2
    REG_GP  = 3
    REG_TP  = 4
    REG_T0  = 5
    REG_T1  = 6
    REG_T2  = 7
    REG_S0  = 8
    REG_S1  = 9
    REG_A0  = 10
    REG_A1  = 11
    REG_A2  = 12
    REG_A3  = 13
    REG_A4  = 14
    REG_A5  = 15
    REG_A6  = 16
    REG_A7  = 17
    REG_S2  = 18
    REG_S3  = 19
    REG_S4  = 20
    REG_S5  = 21
    REG_S6  = 22
    REG_S7  = 23
    REG_S8  = 24
    REG_S9  = 25
    REG_S10 = 26
    REG_S11 = 27
    REG_T3  = 28
    REG_T4  = 29
    REG_T5  = 30
    REG_T6  = 31

def init_register_file_sorts():
    global SID_REGISTER_ADDRESS

    global NID_ZR
    global NID_RA
    global NID_SP
    global NID_GP
    global NID_TP
    global NID_T0
    global NID_T1
    global NID_T2
    global NID_S0
    global NID_S1
    global NID_A0
    global NID_A1
    global NID_A2
    global NID_A3
    global NID_A4
    global NID_A5
    global NID_A6
    global NID_A7
    global NID_S2
    global NID_S3
    global NID_S4
    global NID_S5
    global NID_S6
    global NID_S7
    global NID_S8
    global NID_S9
    global NID_S10
    global NID_S11
    global NID_T3
    global NID_T4
    global NID_T5
    global NID_T6

    global SID_REGISTER_STATE

    init_register_IDs()

    SID_REGISTER_ADDRESS = new_bitvec(5, "5-bit register address")

    NID_ZR  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_ZR, "zero")
    NID_RA  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_RA, "ra")
    NID_SP  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_SP, "sp")
    NID_GP  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_GP, "gp")
    NID_TP  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_TP, "tp")
    NID_T0  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_T0, "t0")
    NID_T1  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_T1, "t1")
    NID_T2  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_T2, "t2")
    NID_S0  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_S0, "s0") # used to be fp
    NID_S1  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_S1, "s1")
    NID_A0  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_A0, "a0")
    NID_A1  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_A1, "a1")
    NID_A2  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_A2, "a2")
    NID_A3  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_A3, "a3")
    NID_A4  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_A4, "a4")
    NID_A5  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_A5, "a5")
    NID_A6  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_A6, "a6")
    NID_A7  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_A7, "a7")
    NID_S2  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_S2, "s2")
    NID_S3  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_S3, "s3")
    NID_S4  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_S4, "s4")
    NID_S5  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_S5, "s5")
    NID_S6  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_S6, "s6")
    NID_S7  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_S7, "s7")
    NID_S8  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_S8, "s8")
    NID_S9  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_S9, "s9")
    NID_S10 = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_S10, "s10")
    NID_S11 = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_S11, "s11")
    NID_T3  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_T3, "t3")
    NID_T4  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_T4, "t4")
    NID_T5  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_T5, "t5")
    NID_T6  = new_constant(OP_CONST, SID_REGISTER_ADDRESS, REG_T6, "t6")

    SID_REGISTER_STATE = new_array(SID_REGISTER_ADDRESS, SID_MACHINE_WORD, "register state")

def load_register_value(reg_nid, comment, register_file_nid):
    return new_binary(OP_READ, SID_MACHINE_WORD, register_file_nid, reg_nid, comment)

def store_register_value(reg_nid, value_nid, comment, register_file_nid):
    return new_ternary(OP_WRITE, SID_REGISTER_STATE, register_file_nid, reg_nid, value_nid, comment)

def get_5_bit_shamt(value_nid):
    return new_ext(OP_UEXT, SID_SINGLE_WORD,
        new_slice(SID_5_BIT_IMM, value_nid, 4, 0, "get 5-bit shamt"),
        SINGLEWORDSIZEINBITS - 5,
        "unsigned-extend 5-bit shamt")

def get_shamt(value_nid):
    if IS64BITTARGET:
        return new_ext(OP_UEXT, SID_MACHINE_WORD,
            new_slice(SID_6_BIT_IMM, value_nid, 5, 0, "get 6-bit shamt"),
            WORDSIZEINBITS - 6,
            "unsigned-extend 6-bit shamt")
    else:
        return get_5_bit_shamt(value_nid)

# memory sorts and specification

# virtual address space

VIRTUAL_ADDRESS_SPACE = 32 # number of bits in virtual addresses

# code segment

CODEWORDSIZEINBITS = 32

max_code_size = 0

code_start = 0
code_size  = 0

# main memory

MEMORYWORDSIZEINBITS = 64

# data segment

max_data_size = 0

data_start = 0
data_size  = 0

# heap segment

heap_initial_size = 0
heap_allowance    = 4096 # must be multiple of WORDSIZE

heap_start = 0
heap_size  = 0

# stack segment

stack_initial_size = 0
stack_allowance    = 2048 # must be multiple of WORDSIZE > 0

stack_start = 0
stack_size  = 0

def init_memory_sorts():
    global VIRTUAL_ADDRESS_SPACE

    global SID_VIRTUAL_ADDRESS

    global NID_VIRTUAL_ADDRESS_0
    global NID_VIRTUAL_ADDRESS_1
    global NID_VIRTUAL_ADDRESS_2
    global NID_VIRTUAL_ADDRESS_3
    global NID_VIRTUAL_ADDRESS_4
    global NID_VIRTUAL_ADDRESS_5
    global NID_VIRTUAL_ADDRESS_6
    global NID_VIRTUAL_ADDRESS_7
    global NID_VIRTUAL_ADDRESS_8

    global NID_VIRTUAL_HALF_WORD_SIZE
    global NID_VIRTUAL_SINGLE_WORD_SIZE
    global NID_VIRTUAL_DOUBLE_WORD_SIZE

    global NID_VIRTUAL_HALF_WORD_SIZE_MINUS_1
    global NID_VIRTUAL_SINGLE_WORD_SIZE_MINUS_1
    global NID_VIRTUAL_DOUBLE_WORD_SIZE_MINUS_1

    global NID_HIGHEST_VIRTUAL_ADDRESS

    global CODEWORDSIZEINBITS

    global SID_CODE_WORD

    global NID_CODE_WORD_0

    global CODE_ADDRESS_SPACE

    global SID_CODE_ADDRESS
    global SID_CODE_STATE

    global NID_CODE_START
    global NID_CODE_END

    global MEMORYWORDSIZEINBITS

    global SID_MEMORY_WORD

    global NID_MEMORY_WORD_0

    global DATA_ADDRESS_SPACE

    global SID_DATA_ADDRESS
    global SID_DATA_STATE

    global NID_DATA_START
    global NID_DATA_END

    global HEAP_ADDRESS_SPACE

    global SID_HEAP_ADDRESS
    global SID_HEAP_STATE

    global NID_HEAP_START
    global NID_HEAP_END

    global STACK_ADDRESS_SPACE

    global SID_STACK_ADDRESS
    global SID_STACK_STATE

    global NID_STACK_START
    global NID_STACK_END

    global NID_HALF_WORD_SIZE_MASK
    global NID_SINGLE_WORD_SIZE_MASK
    global NID_DOUBLE_WORD_SIZE_MASK

    global NID_BYTE_MASK
    global NID_HALF_WORD_MASK
    global NID_SINGLE_WORD_MASK

    global NID_SINGLE_WORD_SIZE_MINUS_HALF_WORD_SIZE
    global NID_DOUBLE_WORD_SIZE_MINUS_HALF_WORD_SIZE
    global NID_DOUBLE_WORD_SIZE_MINUS_SINGLE_WORD_SIZE

    global NID_BYTE_SIZE_IN_BASE_BITS

    if VIRTUAL_ADDRESS_SPACE < WORDSIZEINBITS:
        NID_HIGHEST_VIRTUAL_ADDRESS = new_constant(OP_CONSTD, SID_MACHINE_WORD,
            2**VIRTUAL_ADDRESS_SPACE - 1, "highest virtual address")
    elif VIRTUAL_ADDRESS_SPACE > WORDSIZEINBITS:
        VIRTUAL_ADDRESS_SPACE = WORDSIZEINBITS

    SID_VIRTUAL_ADDRESS = new_bitvec(VIRTUAL_ADDRESS_SPACE, f"{VIRTUAL_ADDRESS_SPACE}-bit virtual address")

    NID_VIRTUAL_ADDRESS_0 = new_constant(OP_CONSTD, SID_VIRTUAL_ADDRESS, 0, "virtual address 0")
    NID_VIRTUAL_ADDRESS_1 = new_constant(OP_CONSTD, SID_VIRTUAL_ADDRESS, 1, "virtual address 1")
    NID_VIRTUAL_ADDRESS_2 = new_constant(OP_CONSTD, SID_VIRTUAL_ADDRESS, 2, "virtual address 2")
    NID_VIRTUAL_ADDRESS_3 = new_constant(OP_CONSTD, SID_VIRTUAL_ADDRESS, 3, "virtual address 3")
    NID_VIRTUAL_ADDRESS_4 = new_constant(OP_CONSTD, SID_VIRTUAL_ADDRESS, 4, "virtual address 4")
    NID_VIRTUAL_ADDRESS_5 = new_constant(OP_CONSTD, SID_VIRTUAL_ADDRESS, 5, "virtual address 5")
    NID_VIRTUAL_ADDRESS_6 = new_constant(OP_CONSTD, SID_VIRTUAL_ADDRESS, 6, "virtual address 6")
    NID_VIRTUAL_ADDRESS_7 = new_constant(OP_CONSTD, SID_VIRTUAL_ADDRESS, 7, "virtual address 7")
    NID_VIRTUAL_ADDRESS_8 = new_constant(OP_CONSTD, SID_VIRTUAL_ADDRESS, 8, "virtual address 8")

    NID_VIRTUAL_HALF_WORD_SIZE   = NID_VIRTUAL_ADDRESS_2
    NID_VIRTUAL_SINGLE_WORD_SIZE = NID_VIRTUAL_ADDRESS_4
    NID_VIRTUAL_DOUBLE_WORD_SIZE = NID_VIRTUAL_ADDRESS_8

    NID_VIRTUAL_HALF_WORD_SIZE_MINUS_1   = NID_VIRTUAL_ADDRESS_1
    NID_VIRTUAL_SINGLE_WORD_SIZE_MINUS_1 = NID_VIRTUAL_ADDRESS_3
    NID_VIRTUAL_DOUBLE_WORD_SIZE_MINUS_1 = NID_VIRTUAL_ADDRESS_7

    # code segment

    if CODEWORDSIZEINBITS > WORDSIZEINBITS:
        CODEWORDSIZEINBITS = WORDSIZEINBITS

    SID_CODE_WORD = new_bitvec(CODEWORDSIZEINBITS, f"{CODEWORDSIZEINBITS}-bit code word")

    NID_CODE_WORD_0 = new_constant(OP_CONSTD, SID_CODE_WORD, 0, "code word 0")

    assert max_code_size >= WORDSIZE

    CODE_ADDRESS_SPACE = calculate_address_space(max_code_size, eval_bitvec_size(SID_CODE_WORD))

    SID_CODE_ADDRESS = new_bitvec(CODE_ADDRESS_SPACE, f"{CODE_ADDRESS_SPACE}-bit code segment address")

    SID_CODE_STATE = new_array(SID_CODE_ADDRESS, SID_CODE_WORD, "code segment state")

    # main memory

    if MEMORYWORDSIZEINBITS > WORDSIZEINBITS:
        MEMORYWORDSIZEINBITS = WORDSIZEINBITS

    SID_MEMORY_WORD = new_bitvec(MEMORYWORDSIZEINBITS, f"{MEMORYWORDSIZEINBITS}-bit memory word")

    NID_MEMORY_WORD_0 = new_constant(OP_CONSTD, SID_MEMORY_WORD, 0, "memory word 0")

    DATA_ADDRESS_SPACE = calculate_address_space(max_data_size, eval_bitvec_size(SID_MEMORY_WORD))

    SID_DATA_ADDRESS = new_bitvec(DATA_ADDRESS_SPACE,
        f"{DATA_ADDRESS_SPACE}-bit physical data segment address")

    SID_DATA_STATE = new_array(SID_DATA_ADDRESS, SID_MEMORY_WORD, "data segment state")

    # heap segment

    HEAP_ADDRESS_SPACE = calculate_address_space(heap_allowance, eval_bitvec_size(SID_MEMORY_WORD))

    SID_HEAP_ADDRESS = new_bitvec(HEAP_ADDRESS_SPACE,
        f"{HEAP_ADDRESS_SPACE}-bit physical heap segment address")

    SID_HEAP_STATE = new_array(SID_HEAP_ADDRESS, SID_MEMORY_WORD, "heap segment state")

    # stack segment

    STACK_ADDRESS_SPACE = calculate_address_space(stack_allowance, eval_bitvec_size(SID_MEMORY_WORD))

    SID_STACK_ADDRESS = new_bitvec(STACK_ADDRESS_SPACE,
        f"{STACK_ADDRESS_SPACE}-bit physical stack segment address")

    SID_STACK_STATE = new_array(SID_STACK_ADDRESS, SID_MEMORY_WORD, "stack segment state");

    # bit masks and factors

    NID_HALF_WORD_SIZE_MASK   = NID_VIRTUAL_ADDRESS_1
    NID_SINGLE_WORD_SIZE_MASK = NID_VIRTUAL_ADDRESS_3
    NID_DOUBLE_WORD_SIZE_MASK = NID_VIRTUAL_ADDRESS_7

    NID_BYTE_MASK        = new_constant(OP_CONSTH, SID_BYTE, 255, "maximum byte value")
    NID_HALF_WORD_MASK   = new_constant(OP_CONSTH, SID_HALF_WORD, 65535, "maximum half-word value")
    NID_SINGLE_WORD_MASK = new_constant(OP_CONSTH, SID_SINGLE_WORD, 4294967295, "maximum single-word value")

    NID_SINGLE_WORD_SIZE_MINUS_HALF_WORD_SIZE   = NID_VIRTUAL_ADDRESS_2
    NID_DOUBLE_WORD_SIZE_MINUS_HALF_WORD_SIZE   = NID_VIRTUAL_ADDRESS_6
    NID_DOUBLE_WORD_SIZE_MINUS_SINGLE_WORD_SIZE = NID_VIRTUAL_ADDRESS_4

    NID_BYTE_SIZE_IN_BASE_BITS = NID_VIRTUAL_ADDRESS_3

def new_segmentation():
    global NID_CODE_START
    global NID_CODE_END

    global NID_DATA_START
    global NID_DATA_END

    global NID_HEAP_START
    global NID_HEAP_END

    global NID_STACK_START
    global NID_STACK_END

    NID_CODE_START = new_constant(OP_CONSTH, SID_VIRTUAL_ADDRESS, code_start,
        f"start of code segment @ 0x{code_start:X}")

    NID_CODE_END = new_constant(OP_CONSTH, SID_VIRTUAL_ADDRESS, code_start + code_size,
        f"end of code segment accommodating at least {code_size // INSTRUCTIONSIZE} instructions")

    assert data_start >= code_start + code_size > 0

    NID_DATA_START = new_constant(OP_CONSTH, SID_VIRTUAL_ADDRESS, data_start,
        f"start of data segment @ 0x{data_start:X}")

    NID_DATA_END = new_constant(OP_CONSTH, SID_VIRTUAL_ADDRESS, data_start + data_size,
        f"end of data segment accommodating {data_size} bytes")

    assert heap_start >= data_start + data_size > 0

    NID_HEAP_START = new_constant(OP_CONSTH, SID_VIRTUAL_ADDRESS, heap_start,
        f"start of heap segment @ 0x{heap_start:X}")

    NID_HEAP_END = new_constant(OP_CONSTH, SID_VIRTUAL_ADDRESS, heap_start + heap_size,
        f"static end of heap segment accommodating {heap_size} bytes")

    assert stack_start >= heap_start + heap_size > 0

    NID_STACK_START = new_constant(OP_CONSTH, SID_VIRTUAL_ADDRESS, stack_start,
        f"static start of stack segment @ 0x{stack_start:X}")

    stack_end = stack_start + stack_size

    if stack_start < stack_end:
        low_stack_address_space = int(math.log2(stack_end))
        up_stack_address_space = low_stack_address_space

        if stack_end > 2**low_stack_address_space:
            up_stack_address_space += 1

        if up_stack_address_space < VIRTUAL_ADDRESS_SPACE:
            NID_STACK_END = new_constant(OP_CONSTH, SID_VIRTUAL_ADDRESS, stack_end,
                f"end of stack segment accommodating {stack_size} bytes")
        elif up_stack_address_space == VIRTUAL_ADDRESS_SPACE:
            if low_stack_address_space < up_stack_address_space:
                NID_STACK_END = new_constant(OP_CONSTH, SID_VIRTUAL_ADDRESS, stack_end,
                    f"end of stack segment accommodating {stack_size} bytes")
            else:
                NID_STACK_END = new_constant(OP_CONSTH, SID_VIRTUAL_ADDRESS, 0,
                    f"end of stack segment accommodating {stack_size} bytes")
        else:
            raise system_error(f"end of stack segment at 0x{stack_end:X} does not fit {VIRTUAL_ADDRESS_SPACE}-bit virtual address space")

    elif stack_end == 0:
        if VIRTUAL_ADDRESS_SPACE == WORDSIZEINBITS:
            NID_STACK_END = new_constant(OP_CONSTH, SID_VIRTUAL_ADDRESS, 0,
                f"end of stack segment accommodating {stack_size} bytes")
        else:
            raise system_error(f"end of stack segment wrapped around to 0x0")
    else:
        raise system_error(f"end of stack segment wrapped around to 0x{stack_end:X}")

def select_segment_feature(segment_nid, code_nid, data_nid, heap_nid, stack_nid):
    sid = get_sid(segment_nid)

    if sid == SID_CODE_STATE:
        return code_nid
    elif sid == SID_DATA_STATE:
        return data_nid
    elif sid == SID_HEAP_STATE:
        return heap_nid
    elif sid == SID_STACK_STATE:
        return stack_nid
    else:
        return UNUSED

def get_segment_start(segment_nid):
    return select_segment_feature(segment_nid,
        NID_CODE_START, NID_DATA_START, NID_HEAP_START, NID_STACK_START)

def get_segment_end(segment_nid):
    return select_segment_feature(segment_nid,
        NID_CODE_END, NID_DATA_END, NID_HEAP_END, NID_STACK_END)

def is_block_in_segment(start_nid, end_nid, segment_nid):
    start_comparison_nid = new_binary_boolean(OP_UGTE,
        start_nid,
        get_segment_start(segment_nid),
        "virtual address of start of block >= start of segment?")

    if eval_constant_value(get_segment_end(segment_nid)) == 0:
        # comparing with end of segment is unnecessary since end wrapped around to zero
        return start_comparison_nid
    else:
        # assert: block and segment start <= end
        return new_binary_boolean(OP_AND,
            start_comparison_nid,
            new_binary_boolean(OP_ULT,
                end_nid,
                get_segment_end(segment_nid),
                "virtual address of end of block < end of segment?"),
            "block in segment?")

def is_virtual_address_in_segment(vaddr_nid, segment_nid):
    return is_block_in_segment(vaddr_nid, vaddr_nid, segment_nid)

def vaddr_to_laddr(vaddr_nid, segment_nid):
    # TODO: distinguish linear addresses from virtual addresses
    return new_binary(OP_SUB, SID_VIRTUAL_ADDRESS,
        vaddr_nid, get_segment_start(segment_nid),
        "map virtual address to linear address in segment")

def store_if_in_segment(vaddr_nid, store_nid, segment_nid):
    return new_ternary(OP_ITE, get_sid(segment_nid),
        is_virtual_address_in_segment(vaddr_nid, segment_nid),
        store_nid,
        segment_nid,
        "store at virtual address if in segment")

# instruction codes

def init_instruction_codes():
    global OP_LOAD
    global OP_IMM
    global OP_STORE
    global OP_OP
    global OP_LUI
    global OP_BRANCH
    global OP_JALR
    global OP_JAL
    global OP_SYSTEM

    global F3_NOP
    global F3_ADDI
    global F3_ADD
    global F3_SUB
    global F3_MUL
    global F3_DIVU
    global F3_REMU
    global F3_SLTU
    global F3_LD
    global F3_SD
    global F3_LW
    global F3_SW
    global F3_BEQ
    global F3_JALR
    global F3_ECALL

    global F7_ADD
    global F7_MUL
    global F7_SUB
    global F7_DIVU
    global F7_REMU
    global F7_SLTU

    global F12_ECALL

    global OP_AUIPC

    global F3_BNE
    global F3_BLT
    global F3_BGE
    global F3_BLTU
    global F3_BGEU

    global F3_LB
    global F3_LH
    global F3_LBU
    global F3_LHU

    global F3_SB
    global F3_SH

    global F3_SLL
    global F3_SLT
    global F3_XOR
    global F3_SRL
    global F3_SRA
    global F3_OR
    global F3_AND

    global F6_SLL_SRL
    global F6_SRA

    global OP_IMM_32
    global OP_OP_32

    global F3_LWU

    global F3_MULH
    global F3_MULHSU
    global F3_MULHU
    global F3_DIV
    global F3_REM

    # RISC-U codes

    OP_LOAD   = 3   # 0000011, I format (LD,LW)
    OP_IMM    = 19  # 0010011, I format (ADDI, NOP)
    OP_STORE  = 35  # 0100011, S format (SD,SW)
    OP_OP     = 51  # 0110011, R format (ADD, SUB, MUL, DIVU, REMU, SLTU)
    OP_LUI    = 55  # 0110111, U format (LUI)
    OP_BRANCH = 99  # 1100011, B format (BEQ)
    OP_JALR   = 103 # 1100111, I format (JALR)
    OP_JAL    = 111 # 1101111, J format (JAL)
    OP_SYSTEM = 115 # 1110011, I format (ECALL)

    F3_NOP   = 0 # 000
    F3_ADDI  = 0 # 000
    F3_ADD   = 0 # 000
    F3_SUB   = 0 # 000
    F3_MUL   = 0 # 000
    F3_DIVU  = 5 # 101
    F3_REMU  = 7 # 111
    F3_SLTU  = 3 # 011
    F3_LD    = 3 # 011
    F3_SD    = 3 # 011
    F3_LW    = 2 # 010
    F3_SW    = 2 # 010
    F3_BEQ   = 0 # 000
    F3_JALR  = 0 # 000
    F3_ECALL = 0 # 000

    F7_ADD  = 0  # 0000000
    F7_MUL  = 1  # 0000001
    F7_SUB  = 32 # 0100000
    F7_DIVU = 1  # 0000001
    F7_REMU = 1  # 0000001
    F7_SLTU = 0  # 0000000

    F12_ECALL = 0 # 000000000000

    # RV32I codes missing in RISC-U

    OP_AUIPC = 23 # 0010111, U format (AUIPC)

    F3_BNE  = 1 # 001
    F3_BLT  = 4 # 100
    F3_BGE  = 5 # 101
    F3_BLTU = 6 # 110
    F3_BGEU = 7 # 111

    F3_LB  = 0 # 000
    F3_LH  = 1 # 001
    F3_LBU = 4 # 100
    F3_LHU = 5 # 101

    F3_SB = 0 # 000
    F3_SH = 1 # 001

    F3_SLL = 1 # 001
    F3_SLT = 2 # 010
    F3_XOR = 4 # 100
    F3_SRL = 5 # 101
    F3_SRA = 5 # 101
    F3_OR  = 6 # 110
    F3_AND = 7 # 111

    # RV64I codes missing in RISC-U

    F6_SLL_SRL = 0  # 000000
    F6_SRA     = 16 # 010000

    OP_IMM_32 = 27 # 0011011, I format
    OP_OP_32  = 59 # 0111011, I format

    F3_LWU = 6 # 110

    # RV32M codes missing in RISC-U

    F3_MULH   = 1 # 001
    F3_MULHSU = 2 # 010
    F3_MULHU  = 3 # 011
    F3_DIV    = 4 # 100
    F3_REM    = 6 # 110

RISCUONLY = False # restrict modeling to RISC-U only

RV32M = True # RV32M support
RV64M = True # RV64M support

# compressed instruction codes

def init_compressed_instruction_codes():
    global F3_C_LI
    global F3_C_LUI_ADDI16SP

    global F3_C_ADDI
    global F3_C_ADDIW_JAL

    global F3_C_ADDI4SPN

    global F3_C_SLLI
    global F3_C_SRLI_SRAI_ANDI

    global F2_C_SRLI
    global F2_C_SRAI
    global F2_C_ANDI

    global F6_C_SUB_XOR_OR_AND
    global F6_C_ADDW_SUBW

    global F2_C_SUB_SUBW
    global F2_C_XOR_ADDW
    global F2_C_OR
    global F2_C_AND

    global F3_C_LWSP_LW
    global F3_C_LDSP_LD

    global F3_C_SWSP_SW
    global F3_C_SDSP_SD

    global F3_C_BEQZ
    global F3_C_BNEZ

    global F3_C_J

    global F4_C_MV_JR
    global F4_C_ADD_JALR

    # RVC codes

    F3_C_LI           = 2 # 010
    F3_C_LUI_ADDI16SP = 3 # 011

    F3_C_ADDI      = 0 # 000
    F3_C_ADDIW_JAL = 1 # 001

    F3_C_ADDI4SPN = 0 # 000

    F3_C_SLLI           = 0 # 000
    F3_C_SRLI_SRAI_ANDI = 4 # 100

    F2_C_SRLI = 0 # 00
    F2_C_SRAI = 1 # 01
    F2_C_ANDI = 2 # 10

    F6_C_SUB_XOR_OR_AND = 35 # 100011
    F6_C_ADDW_SUBW      = 39 # 100111

    F2_C_SUB_SUBW = 0 # 00
    F2_C_XOR_ADDW = 1 # 01
    F2_C_OR       = 2 # 10
    F2_C_AND      = 3 # 11

    F3_C_LWSP_LW = 2 # 010
    F3_C_LDSP_LD = 3 # 011

    F3_C_SWSP_SW = 6 # 110
    F3_C_SDSP_SD = 7 # 111

    F3_C_BEQZ = 6 # 110
    F3_C_BNEZ = 7 # 111

    F3_C_J = 5 # 101

    F4_C_MV_JR    = 8 # 1000
    F4_C_ADD_JALR = 9 # 1001

RVC = True # RVC support

# instruction IDs

def init_instruction_IDs():
    global ID_UNKNOWN

    global ID_ECALL

    global ID_ADD
    global ID_SUB
    global ID_SLL
    global ID_SLT
    global ID_SLTU
    global ID_XOR
    global ID_SRL
    global ID_SRA
    global ID_OR
    global ID_AND

    global ID_ADDW
    global ID_SUBW
    global ID_SLLW
    global ID_SRLW
    global ID_SRAW

    global ID_MUL
    global ID_MULH
    global ID_MULHSU
    global ID_MULHU
    global ID_DIV
    global ID_DIVU
    global ID_REM
    global ID_REMU

    global ID_MULW
    global ID_DIVW
    global ID_DIVUW
    global ID_REMW
    global ID_REMUW

    global ID_JALR

    global ID_LB
    global ID_LH
    global ID_LW
    global ID_LBU
    global ID_LHU
    global ID_LWU
    global ID_LD

    global ID_ADDI
    global ID_SLTI
    global ID_SLTIU
    global ID_XORI
    global ID_ORI
    global ID_ANDI

    global ID_ADDIW

    global ID_SLLI
    global ID_SRLI
    global ID_SRAI

    global ID_SLLIW
    global ID_SRLIW
    global ID_SRAIW

    global ID_SB
    global ID_SH
    global ID_SW
    global ID_SD

    global ID_BEQ
    global ID_BNE
    global ID_BLT
    global ID_BGE
    global ID_BLTU
    global ID_BGEU

    global ID_LUI
    global ID_AUIPC

    global ID_JAL

    global ID_C_MV
    global ID_C_ADD

    global ID_C_JR
    global ID_C_JALR

    global ID_C_LI
    global ID_C_LUI

    global ID_C_ADDI
    global ID_C_ADDIW
    global ID_C_ADDI16SP

    global ID_C_ADDI4SPN

    global ID_C_SLLI

    global ID_C_LWSP
    global ID_C_LDSP

    global ID_C_LW
    global ID_C_LD

    global ID_C_SW
    global ID_C_SD

    global ID_C_SUB
    global ID_C_XOR
    global ID_C_OR
    global ID_C_AND

    global ID_C_ADDW
    global ID_C_SUBW

    global ID_C_SWSP
    global ID_C_SDSP

    global ID_C_BEQZ
    global ID_C_BNEZ

    global ID_C_ANDI

    global ID_C_SRLI
    global ID_C_SRAI

    global ID_C_J
    global ID_C_JAL

    global ID_P_NOP
    global ID_P_RET

    global ID_P_LI

    global ID_P_MV
    global ID_P_NOT
    global ID_P_SEXT_W
    global ID_P_SEQZ
    global ID_P_SLTZ
    global ID_P_ZEXT_B
    global ID_P_NEG
    global ID_P_NEGW
    global ID_P_SNEZ
    global ID_P_SGTZ

    global ID_P_BEQZ
    global ID_P_BNEZ
    global ID_P_BGEZ
    global ID_P_BLTZ
    global ID_P_BLEZ
    global ID_P_BGTZ

    global ID_P_J
    global ID_P_JAL

    global ID_P_JR
    global ID_P_JALR

    global RISC_V_MNEMONICS

    ID_UNKNOWN = 0

    ID_ECALL = 1

    # R-type

    ID_ADD  = 2
    ID_SUB  = 3
    ID_SLL  = 4
    ID_SLT  = 5
    ID_SLTU = 6
    ID_XOR  = 7
    ID_SRL  = 8
    ID_SRA  = 9
    ID_OR   = 10
    ID_AND  = 11

    ID_ADDW = 12
    ID_SUBW = 13
    ID_SLLW = 14
    ID_SRLW = 15
    ID_SRAW = 16

    ID_MUL    = 17
    ID_MULH   = 18
    ID_MULHSU = 19
    ID_MULHU  = 20
    ID_DIV    = 21
    ID_DIVU   = 22
    ID_REM    = 23
    ID_REMU   = 24

    ID_MULW  = 25
    ID_DIVW  = 26
    ID_DIVUW = 27
    ID_REMW  = 28
    ID_REMUW = 29

    # I-type

    ID_JALR = 30

    ID_LB  = 31
    ID_LH  = 32
    ID_LW  = 33
    ID_LBU = 34
    ID_LHU = 35
    ID_LWU = 36
    ID_LD  = 37

    ID_ADDI  = 38
    ID_SLTI  = 39
    ID_SLTIU = 40
    ID_XORI  = 41
    ID_ORI   = 42
    ID_ANDI  = 43

    ID_ADDIW = 44

    ID_SLLI = 45
    ID_SRLI = 46
    ID_SRAI = 47

    ID_SLLIW = 48
    ID_SRLIW = 49
    ID_SRAIW = 50

    # S-type

    ID_SB = 51
    ID_SH = 52
    ID_SW = 53
    ID_SD = 54

    # SB-type

    ID_BEQ  = 55
    ID_BNE  = 56
    ID_BLT  = 57
    ID_BGE  = 58
    ID_BLTU = 59
    ID_BGEU = 60

    # U-type

    ID_LUI   = 61
    ID_AUIPC = 62

    # UJ-type

    ID_JAL = 63

    # compressed instruction IDs

    # CR-type

    ID_C_MV  = 64
    ID_C_ADD = 65

    ID_C_JR   = 66
    ID_C_JALR = 67

    # CI-type

    ID_C_LI  = 68
    ID_C_LUI = 69

    ID_C_ADDI     = 70
    ID_C_ADDIW    = 71
    ID_C_ADDI16SP = 72

    # CIW-type

    ID_C_ADDI4SPN = 73

    # CI-type

    ID_C_SLLI = 74

    ID_C_LWSP = 75
    ID_C_LDSP = 76

    # CL-type

    ID_C_LW = 77
    ID_C_LD = 78

    # CS-type

    ID_C_SW = 79
    ID_C_SD = 80

    ID_C_SUB = 81
    ID_C_XOR = 82
    ID_C_OR  = 83
    ID_C_AND = 84

    ID_C_ADDW = 85
    ID_C_SUBW = 86

    # CSS-type

    ID_C_SWSP = 87
    ID_C_SDSP = 88

    # CB-type

    ID_C_BEQZ = 89
    ID_C_BNEZ = 90

    ID_C_ANDI = 91

    ID_C_SRLI = 92
    ID_C_SRAI = 93

    # CJ-type

    ID_C_J   = 94
    ID_C_JAL = 95

    # pseudoinstruction IDs

    # No operands

    ID_P_NOP = 96
    ID_P_RET = 97

    # rd,I_imm

    ID_P_LI = 98

    # rd,rsx

    ID_P_MV     = 99  # rs1 or rs2
    ID_P_NOT    = 100 # rs1
    ID_P_SEXT_W = 101 # rs1
    ID_P_SEQZ   = 102 # rs1
    ID_P_SLTZ   = 103 # rs1
    ID_P_ZEXT_B = 104 # rs1
    ID_P_NEG    = 105 # rs2
    ID_P_NEGW   = 106 # rs2
    ID_P_SNEZ   = 107 # rs2
    ID_P_SGTZ   = 108 # rs2

    # branch type (rsx,pc+SB_imm <SB_imm>)

    ID_P_BEQZ = 109 # rs1
    ID_P_BNEZ = 110 # rs1
    ID_P_BGEZ = 111 # rs1
    ID_P_BLTZ = 112 # rs1
    ID_P_BLEZ = 113 # rs2
    ID_P_BGTZ = 114 # rs2

    # jump type (pc + UJ_imm <UJ_imm>)

    ID_P_J   = 115
    ID_P_JAL = 116

    # jump register type (immx(rs1))

    ID_P_JR   = 117 # I_imm or 0
    ID_P_JALR = 118 # I_imm or 0

    RISC_V_MNEMONICS = {
        ID_UNKNOWN: "unknown RISC-V instruction",

        ID_ECALL: 'ecall',

        # R-type

        ID_ADD:  'add',
        ID_SUB:  'sub',
        ID_SLL:  'sll',
        ID_SLT:  'slt',
        ID_SLTU: 'sltu',
        ID_XOR:  'xor',
        ID_SRL:  'srl',
        ID_SRA:  'sra',
        ID_OR:   'or',
        ID_AND:  'and',

        ID_ADDW: 'addw',
        ID_SUBW: 'subw',
        ID_SLLW: 'sllw',
        ID_SRLW: 'srlw',
        ID_SRAW: 'sraw',

        ID_MUL:    'mul',
        ID_MULH:   'mulh',
        ID_MULHSU: 'mulhsu',
        ID_MULHU:  'mulhu',
        ID_DIV:    'div',
        ID_DIVU:   'divu',
        ID_REM:    'rem',
        ID_REMU:   'remu',

        ID_MULW:  'mulw',
        ID_DIVW:  'divw',
        ID_DIVUW: 'divuw',
        ID_REMW:  'remw',
        ID_REMUW: 'remuw',

        # I-type

        ID_JALR: 'jalr',

        ID_LB:  'lb',
        ID_LH:  'lh',
        ID_LW:  'lw',
        ID_LBU: 'lbu',
        ID_LHU: 'lhu',
        ID_LWU: 'lwu',
        ID_LD:  'ld',

        ID_ADDI:  'addi',
        ID_SLTI:  'slti',
        ID_SLTIU: 'sltiu',
        ID_XORI:  'xori',
        ID_ORI:   'ori',
        ID_ANDI:  'andi',

        ID_ADDIW: 'addiw',

        ID_SLLI: 'slli',
        ID_SRLI: 'srli',
        ID_SRAI: 'srai',

        ID_SLLIW: 'slliw',
        ID_SRLIW: 'srliw',
        ID_SRAIW: 'sraiw',

        # S-type

        ID_SB: 'sb',
        ID_SH: 'sh',
        ID_SW: 'sw',
        ID_SD: 'sd',

        # SB-type

        ID_BEQ:  'beq',
        ID_BNE:  'bne',
        ID_BLT:  'blt',
        ID_BGE:  'bge',
        ID_BLTU: 'bltu',
        ID_BGEU: 'bgeu',

        # U-type

        ID_LUI:   'lui',
        ID_AUIPC: 'auipc',

        # UJ-type

        ID_JAL: 'jal',

        # compressed instruction IDs

        # CR-type

        ID_C_MV:  'c.mv',
        ID_C_ADD: 'c.add',

        ID_C_JR:   'c.jr',
        ID_C_JALR: 'c.jalr',

        # CI-type

        ID_C_LI:  'c.li',
        ID_C_LUI: 'c.lui',

        ID_C_ADDI:     'c.addi',
        ID_C_ADDIW:    'c.addiw',
        ID_C_ADDI16SP: 'c.addi16sp',

        # CIW-type

        ID_C_ADDI4SPN: 'c.addi4spn',

        # CI-type

        ID_C_SLLI: 'c.slli',

        ID_C_LWSP: 'c.lwsp',
        ID_C_LDSP: 'c.ldsp',

        # CL-type

        ID_C_LW: 'c.lw',
        ID_C_LD: 'c.ld',

        # CS-type

        ID_C_SW: 'c.sw',
        ID_C_SD: 'c.sd',

        ID_C_SUB: 'c.sub',
        ID_C_XOR: 'c.xor',
        ID_C_OR:  'c.or',
        ID_C_AND: 'c.and',

        ID_C_ADDW: 'c.addw',
        ID_C_SUBW: 'c.subw',

        # CSS-type

        ID_C_SWSP: 'c.swsp',
        ID_C_SDSP: 'c.sdsp',

        # CB-type

        ID_C_BEQZ: 'c.beqz',
        ID_C_BNEZ: 'c.bnez',

        ID_C_ANDI: 'c.andi',

        ID_C_SRLI: 'c.srli',
        ID_C_SRAI: 'c.srai',

        # CJ-type

        ID_C_J:   'c.j',
        ID_C_JAL: 'c.jal',

        # pseudoinstruction IDs

        # No operands

        ID_P_NOP: 'nop',
        ID_P_RET: 'ret',

        # rd,I_imm

        ID_P_LI: 'li',

        # rd,rsx

        ID_P_MV:     'mv',
        ID_P_NOT:    'not',
        ID_P_SEXT_W: 'sext.w',
        ID_P_SEQZ:   'seqz',
        ID_P_SLTZ:   'sltz',
        ID_P_ZEXT_B: 'zext.b',
        ID_P_NEG:    'neg',
        ID_P_NEGW:   'negw',
        ID_P_SNEZ:   'snez',
        ID_P_SGTZ:   'sgtz',

        # branch type (rsx,pc+SB_imm <SB_imm>)

        ID_P_BEQZ: 'beqz',
        ID_P_BNEZ: 'bnez',
        ID_P_BGEZ: 'bgez',
        ID_P_BLTZ: 'bltz',
        ID_P_BLEZ: 'blez',
        ID_P_BGTZ: 'bgtz',

        # jump type (pc + UJ_imm <UJ_imm>)

        ID_P_J:   'j',
        ID_P_JAL: 'jal',

        # jump register type (immx(rs1))

        ID_P_JR:   'jr',
        ID_P_JALR: 'jalr'
    }

# instructions

def init_instruction_sorts():
    global SID_INSTRUCTION_WORD

    global NID_INSTRUCTION_WORD_SIZE_MASK

    global SID_OPCODE

    global NID_OP_LOAD
    global NID_OP_IMM
    global NID_OP_STORE
    global NID_OP_OP
    global NID_OP_LUI
    global NID_OP_BRANCH
    global NID_OP_JALR
    global NID_OP_JAL
    global NID_OP_SYSTEM

    global SID_FUNCT3

    global NID_F3_NOP
    global NID_F3_ADDI
    global NID_F3_ADD_SUB_MUL
    global NID_F3_DIVU
    global NID_F3_REMU
    global NID_F3_SLTU
    global NID_F3_LD
    global NID_F3_SD
    global NID_F3_LW
    global NID_F3_SW
    global NID_F3_BEQ
    global NID_F3_JALR
    global NID_F3_ECALL

    global SID_FUNCT7

    global NID_F7_ADD
    global NID_F7_MUL
    global NID_F7_SUB
    global NID_F7_DIVU
    global NID_F7_REMU
    global NID_F7_SLTU

    global NID_F7_MUL_DIV_REM

    global SID_FUNCT12

    global NID_F12_ECALL

    global NID_ECALL_I

    global SID_1_BIT_IMM
    global SID_4_BIT_IMM
    global SID_5_BIT_IMM
    global SID_6_BIT_IMM
    global SID_8_BIT_IMM
    global SID_10_BIT_IMM
    global SID_11_BIT_IMM
    global SID_12_BIT_IMM
    global SID_13_BIT_IMM
    global SID_20_BIT_IMM
    global SID_21_BIT_IMM
    global SID_32_BIT_IMM

    global NID_1_BIT_IMM_0
    global NID_12_BIT_IMM_0

    global SID_INSTRUCTION_ID

    global NID_DISABLED

    global NID_LUI
    global NID_ADDI

    global NID_ADD
    global NID_SUB
    global NID_MUL
    global NID_DIVU
    global NID_REMU
    global NID_SLTU

    global NID_LD
    global NID_SD
    global NID_LW
    global NID_SW

    global NID_BEQ
    global NID_JAL
    global NID_JALR

    global NID_ECALL

    global NID_OP_AUIPC

    global NID_F3_BNE
    global NID_F3_BLT
    global NID_F3_BGE
    global NID_F3_BLTU
    global NID_F3_BGEU

    global NID_F3_LB
    global NID_F3_LH
    global NID_F3_LBU
    global NID_F3_LHU

    global NID_F3_SB
    global NID_F3_SH

    global NID_F3_SLL
    global NID_F3_SLT
    global NID_F3_XOR
    global NID_F3_SRL
    global NID_F3_SRA
    global NID_F3_OR
    global NID_F3_AND

    global NID_F7_ADD_SLT_XOR_OR_AND_SLL_SRL
    global NID_F7_SUB_SRA

    global NID_F7_SLL_SRL_ILLEGAL
    global NID_F7_SRA_ILLEGAL

    global NID_AUIPC

    global NID_BNE
    global NID_BLT
    global NID_BGE
    global NID_BLTU
    global NID_BGEU

    global NID_LB
    global NID_LH
    global NID_LBU
    global NID_LHU

    global NID_SB
    global NID_SH

    global NID_SLTI
    global NID_SLTIU
    global NID_XORI
    global NID_ORI
    global NID_ANDI

    global NID_SLLI
    global NID_SRLI
    global NID_SRAI

    global NID_SLL
    global NID_SLT
    global NID_XOR
    global NID_SRL
    global NID_SRA

    global NID_OR
    global NID_AND

    global SID_FUNCT6

    global NID_F6_SLL_SRL
    global NID_F6_SRA

    global NID_OP_IMM_32
    global NID_OP_OP_32

    global NID_F3_LWU

    global NID_LWU

    global NID_ADDIW
    global NID_SLLIW
    global NID_SRLIW
    global NID_SRAIW

    global NID_ADDW
    global NID_SUBW
    global NID_SLLW
    global NID_SRLW
    global NID_SRAW

    global NID_F3_MULH
    global NID_F3_MULHSU
    global NID_F3_MULHU
    global NID_F3_DIV
    global NID_F3_REM

    global RV32M

    global NID_MULH
    global NID_MULHSU
    global NID_MULHU
    global NID_DIV
    global NID_REM

    global RV64M

    global NID_MULW
    global NID_DIVW
    global NID_DIVUW
    global NID_REMW
    global NID_REMUW

    init_instruction_codes()
    init_instruction_IDs()

    SID_INSTRUCTION_WORD = SID_SINGLE_WORD;

    if (RVC):
        NID_INSTRUCTION_WORD_SIZE_MASK = NID_MACHINE_WORD_1
    else:
        NID_INSTRUCTION_WORD_SIZE_MASK = NID_MACHINE_WORD_3

    SID_OPCODE = new_bitvec(7, "opcode sort");

    NID_OP_LOAD   = new_constant(OP_CONST, SID_OPCODE, OP_LOAD, "OP_LOAD")
    NID_OP_IMM    = new_constant(OP_CONST, SID_OPCODE, OP_IMM, "OP_IMM")
    NID_OP_STORE  = new_constant(OP_CONST, SID_OPCODE, OP_STORE, "OP_STORE")
    NID_OP_OP     = new_constant(OP_CONST, SID_OPCODE, OP_OP, "OP_OP")
    NID_OP_LUI    = new_constant(OP_CONST, SID_OPCODE, OP_LUI, "OP_LUI")
    NID_OP_BRANCH = new_constant(OP_CONST, SID_OPCODE, OP_BRANCH, "OP_BRANCH")
    NID_OP_JALR   = new_constant(OP_CONST, SID_OPCODE, OP_JALR, "OP_JALR")
    NID_OP_JAL    = new_constant(OP_CONST, SID_OPCODE, OP_JAL, "OP_JAL")
    NID_OP_SYSTEM = new_constant(OP_CONST, SID_OPCODE, OP_SYSTEM, "OP_SYSTEM")

    SID_FUNCT3 = new_bitvec(3, "funct3 sort")

    NID_F3_NOP         = new_constant(OP_CONST, SID_FUNCT3, F3_NOP, "F3_NOP")
    NID_F3_ADDI        = new_constant(OP_CONST, SID_FUNCT3, F3_ADDI, "F3_ADDI")
    NID_F3_ADD_SUB_MUL = new_constant(OP_CONST, SID_FUNCT3, F3_ADD, "F3_ADD_SUB_MUL")
    NID_F3_DIVU        = new_constant(OP_CONST, SID_FUNCT3, F3_DIVU, "F3_DIVU")
    NID_F3_REMU        = new_constant(OP_CONST, SID_FUNCT3, F3_REMU, "F3_REMU")
    NID_F3_SLTU        = new_constant(OP_CONST, SID_FUNCT3, F3_SLTU, "F3_SLTU")
    NID_F3_LD          = new_constant(OP_CONST, SID_FUNCT3, F3_LD, "F3_LD")
    NID_F3_SD          = new_constant(OP_CONST, SID_FUNCT3, F3_SD, "F3_SD")
    NID_F3_LW          = new_constant(OP_CONST, SID_FUNCT3, F3_LW, "F3_LW")
    NID_F3_SW          = new_constant(OP_CONST, SID_FUNCT3, F3_SW, "F3_SW")
    NID_F3_BEQ         = new_constant(OP_CONST, SID_FUNCT3, F3_BEQ, "F3_BEQ")
    NID_F3_JALR        = new_constant(OP_CONST, SID_FUNCT3, F3_JALR, "F3_JALR")
    NID_F3_ECALL       = new_constant(OP_CONST, SID_FUNCT3, F3_ECALL, "F3_ECALL")

    SID_FUNCT7 = new_bitvec(7, "funct7 sort")

    NID_F7_ADD  = new_constant(OP_CONST, SID_FUNCT7, F7_ADD, "F7_ADD")
    NID_F7_MUL  = new_constant(OP_CONST, SID_FUNCT7, F7_MUL, "F7_MUL")
    NID_F7_SUB  = new_constant(OP_CONST, SID_FUNCT7, F7_SUB, "F7_SUB")
    NID_F7_DIVU = new_constant(OP_CONST, SID_FUNCT7, F7_DIVU, "F7_DIVU")
    NID_F7_REMU = new_constant(OP_CONST, SID_FUNCT7, F7_REMU, "F7_REMU")
    NID_F7_SLTU = new_constant(OP_CONST, SID_FUNCT7, F7_SLTU, "F7_SLTU")

    NID_F7_MUL_DIV_REM = NID_F7_MUL

    SID_FUNCT12 = new_bitvec(12, "funct12 sort")

    NID_F12_ECALL = new_constant(OP_CONST, SID_FUNCT12, F12_ECALL, "F12_ECALL")

    NID_ECALL_I = new_constant(OP_CONST, SID_INSTRUCTION_WORD,
        (((((((F12_ECALL << 5) + REG_ZR) << 3) + F3_ECALL) << 5) + REG_ZR) << 7) + OP_SYSTEM,
        "ECALL instruction");

    # immediate sorts

    SID_1_BIT_IMM  = new_bitvec(1, "1-bit immediate sort")
    SID_4_BIT_IMM  = new_bitvec(4, "4-bit immediate sort")
    SID_5_BIT_IMM  = new_bitvec(5, "5-bit immediate sort")
    SID_6_BIT_IMM  = new_bitvec(6, "6-bit immediate sort")
    SID_8_BIT_IMM  = new_bitvec(8, "8-bit immediate sort")
    SID_10_BIT_IMM = new_bitvec(10, "10-bit immediate sort")
    SID_11_BIT_IMM = new_bitvec(11, "11-bit immediate sort")
    SID_12_BIT_IMM = new_bitvec(12, "12-bit immediate sort")
    SID_13_BIT_IMM = new_bitvec(13, "13-bit immediate sort")
    SID_20_BIT_IMM = new_bitvec(20, "20-bit immediate sort")
    SID_21_BIT_IMM = new_bitvec(21, "21-bit immediate sort")
    SID_32_BIT_IMM = new_bitvec(32, "32-bit immediate sort")

    NID_1_BIT_IMM_0  = new_constant(OP_CONST, SID_1_BIT_IMM, 0, "zeroed bit")
    NID_12_BIT_IMM_0 = new_constant(OP_CONST, SID_12_BIT_IMM, 0, "12 LSBs zeroed")

    # RISC-U instructions

    SID_INSTRUCTION_ID = new_bitvec(7, "7-bit instruction ID")

    NID_DISABLED = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_UNKNOWN, RISC_V_MNEMONICS[ID_UNKNOWN])

    NID_LUI  = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_LUI, RISC_V_MNEMONICS[ID_LUI])
    NID_ADDI = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_ADDI, RISC_V_MNEMONICS[ID_ADDI])

    NID_ADD  = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_ADD, RISC_V_MNEMONICS[ID_ADD])
    NID_SUB  = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_SUB, RISC_V_MNEMONICS[ID_SUB])
    NID_MUL  = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_MUL, RISC_V_MNEMONICS[ID_MUL])
    NID_DIVU = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_DIVU, RISC_V_MNEMONICS[ID_DIVU])
    NID_REMU = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_REMU, RISC_V_MNEMONICS[ID_REMU])
    NID_SLTU = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_SLTU, RISC_V_MNEMONICS[ID_SLTU])

    NID_LW = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_LW, RISC_V_MNEMONICS[ID_LW])
    NID_SW = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_SW, RISC_V_MNEMONICS[ID_SW])
    NID_LD = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_LD, RISC_V_MNEMONICS[ID_LD])
    NID_SD = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_SD, RISC_V_MNEMONICS[ID_SD])

    NID_BEQ  = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_BEQ, RISC_V_MNEMONICS[ID_BEQ])
    NID_JAL  = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_JAL, RISC_V_MNEMONICS[ID_JAL])
    NID_JALR = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_JALR, RISC_V_MNEMONICS[ID_JALR])

    NID_ECALL = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_ECALL, RISC_V_MNEMONICS[ID_ECALL])

    if IS64BITTARGET:
        if RISCUONLY:
            NID_LW = NID_DISABLED
            NID_SW = NID_DISABLED
    else:
        NID_LD = NID_DISABLED
        NID_SD = NID_DISABLED

    # RV32I codes missing in RISC-U

    NID_OP_AUIPC = new_constant(OP_CONST, SID_OPCODE, OP_AUIPC, "OP_AUIPC")

    NID_F3_BNE  = new_constant(OP_CONST, SID_FUNCT3, F3_BNE, "F3_BNE")
    NID_F3_BLT  = new_constant(OP_CONST, SID_FUNCT3, F3_BLT, "F3_BLT")
    NID_F3_BGE  = new_constant(OP_CONST, SID_FUNCT3, F3_BGE, "F3_BGE")
    NID_F3_BLTU = new_constant(OP_CONST, SID_FUNCT3, F3_BLTU, "F3_BLTU")
    NID_F3_BGEU = new_constant(OP_CONST, SID_FUNCT3, F3_BGEU, "F3_BGEU")

    NID_F3_LB  = new_constant(OP_CONST, SID_FUNCT3, F3_LB, "F3_LB")
    NID_F3_LH  = new_constant(OP_CONST, SID_FUNCT3, F3_LH, "F3_LH")
    NID_F3_LBU = new_constant(OP_CONST, SID_FUNCT3, F3_LBU, "F3_LBU")
    NID_F3_LHU = new_constant(OP_CONST, SID_FUNCT3, F3_LHU, "F3_LHU")

    NID_F3_SB = new_constant(OP_CONST, SID_FUNCT3, F3_SB, "F3_SB")
    NID_F3_SH = new_constant(OP_CONST, SID_FUNCT3, F3_SH, "F3_SH")

    NID_F3_SLL = new_constant(OP_CONST, SID_FUNCT3, F3_SLL, "F3_SLL")
    NID_F3_SLT = new_constant(OP_CONST, SID_FUNCT3, F3_SLT, "F3_SLT")
    NID_F3_XOR = new_constant(OP_CONST, SID_FUNCT3, F3_XOR, "F3_XOR")
    NID_F3_SRL = new_constant(OP_CONST, SID_FUNCT3, F3_SRL, "F3_SRL")
    NID_F3_SRA = new_constant(OP_CONST, SID_FUNCT3, F3_SRA, "F3_SRA")
    NID_F3_OR  = new_constant(OP_CONST, SID_FUNCT3, F3_OR, "F3_OR")
    NID_F3_AND = new_constant(OP_CONST, SID_FUNCT3, F3_AND, "F3_AND")

    NID_F7_ADD_SLT_XOR_OR_AND_SLL_SRL = NID_F7_ADD
    NID_F7_SUB_SRA                    = NID_F7_SUB

    NID_F7_SLL_SRL_ILLEGAL = new_constant(OP_CONST, SID_FUNCT7, F7_ADD + 1, "F7_SLL_SRL_ILLEGAL")
    NID_F7_SRA_ILLEGAL     = new_constant(OP_CONST, SID_FUNCT7, F7_SUB + 1, "F7_SRA_ILLEGAL")

    # RV32I instruction switches

    if RISCUONLY:
        NID_AUIPC = NID_DISABLED

        NID_BNE  = NID_DISABLED
        NID_BLT  = NID_DISABLED
        NID_BGE  = NID_DISABLED
        NID_BLTU = NID_DISABLED
        NID_BGEU = NID_DISABLED

        NID_LB  = NID_DISABLED
        NID_LH  = NID_DISABLED
        NID_LBU = NID_DISABLED
        NID_LHU = NID_DISABLED

        NID_SB = NID_DISABLED
        NID_SH = NID_DISABLED

        NID_SLTI  = NID_DISABLED
        NID_SLTIU = NID_DISABLED
        NID_XORI  = NID_DISABLED
        NID_ORI   = NID_DISABLED
        NID_ANDI  = NID_DISABLED

        NID_SLLI = NID_DISABLED
        NID_SRLI = NID_DISABLED
        NID_SRAI = NID_DISABLED

        NID_SLL = NID_DISABLED
        NID_SLT = NID_DISABLED
        NID_XOR = NID_DISABLED
        NID_SRL = NID_DISABLED
        NID_SRA = NID_DISABLED

        NID_OR  = NID_DISABLED
        NID_AND = NID_DISABLED;
    else:
        NID_AUIPC = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_AUIPC, RISC_V_MNEMONICS[ID_AUIPC])

        NID_BNE  = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_BNE, RISC_V_MNEMONICS[ID_BNE])
        NID_BLT  = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_BLT, RISC_V_MNEMONICS[ID_BLT])
        NID_BGE  = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_BGE, RISC_V_MNEMONICS[ID_BGE])
        NID_BLTU = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_BLTU, RISC_V_MNEMONICS[ID_BLTU])
        NID_BGEU = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_BGEU, RISC_V_MNEMONICS[ID_BGEU])

        NID_LB  = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_LB, RISC_V_MNEMONICS[ID_LB])
        NID_LH  = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_LH, RISC_V_MNEMONICS[ID_LH])
        NID_LBU = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_LBU, RISC_V_MNEMONICS[ID_LBU])
        NID_LHU = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_LHU, RISC_V_MNEMONICS[ID_LHU])

        NID_SB = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_SB, RISC_V_MNEMONICS[ID_SB])
        NID_SH = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_SH, RISC_V_MNEMONICS[ID_SH])

        NID_SLTI  = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_SLTI, RISC_V_MNEMONICS[ID_SLTI])
        NID_SLTIU = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_SLTIU, RISC_V_MNEMONICS[ID_SLTIU])
        NID_XORI  = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_XORI, RISC_V_MNEMONICS[ID_XORI])
        NID_ORI   = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_ORI, RISC_V_MNEMONICS[ID_ORI])
        NID_ANDI  = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_ANDI, RISC_V_MNEMONICS[ID_ANDI])

        NID_SLLI = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_SLLI, RISC_V_MNEMONICS[ID_SLLI])
        NID_SRLI = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_SRLI, RISC_V_MNEMONICS[ID_SRLI])
        NID_SRAI = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_SRAI, RISC_V_MNEMONICS[ID_SRAI])

        NID_SLL = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_SLL, RISC_V_MNEMONICS[ID_SLL])
        NID_SLT = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_SLT, RISC_V_MNEMONICS[ID_SLT])
        NID_XOR = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_XOR, RISC_V_MNEMONICS[ID_XOR])
        NID_SRL = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_SRL, RISC_V_MNEMONICS[ID_SRL])
        NID_SRA = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_SRA, RISC_V_MNEMONICS[ID_SRA])

        NID_OR  = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_OR, RISC_V_MNEMONICS[ID_OR])
        NID_AND = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_AND, RISC_V_MNEMONICS[ID_AND])

    # RV64I codes missing in RISC-U

    SID_FUNCT6 = new_bitvec(6, "funct6 sort")

    NID_F6_SLL_SRL = new_constant(OP_CONST, SID_FUNCT6, F6_SLL_SRL, "F6_SLL_SRL")
    NID_F6_SRA     = new_constant(OP_CONST, SID_FUNCT6, F6_SRA, "F6_SRA")

    NID_OP_IMM_32 = new_constant(OP_CONST, SID_OPCODE, OP_IMM_32, "OP_IMM_32")
    NID_OP_OP_32  = new_constant(OP_CONST, SID_OPCODE, OP_OP_32, "OP_OP_32")

    NID_F3_LWU = new_constant(OP_CONST, SID_FUNCT3, F3_LWU, "F3_LWU")

    # RV64I instruction switches

    NID_LWU = NID_DISABLED

    NID_ADDIW = NID_DISABLED
    NID_SLLIW = NID_DISABLED
    NID_SRLIW = NID_DISABLED
    NID_SRAIW = NID_DISABLED

    NID_ADDW = NID_DISABLED
    NID_SUBW = NID_DISABLED
    NID_SLLW = NID_DISABLED
    NID_SRLW = NID_DISABLED
    NID_SRAW = NID_DISABLED

    if not RISCUONLY:
        if IS64BITTARGET:
            NID_LWU = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_LWU, RISC_V_MNEMONICS[ID_LWU])

            NID_ADDIW = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_ADDIW, RISC_V_MNEMONICS[ID_ADDIW])
            NID_SLLIW = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_SLLIW, RISC_V_MNEMONICS[ID_SLLIW])
            NID_SRLIW = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_SRLIW, RISC_V_MNEMONICS[ID_SRLIW])
            NID_SRAIW = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_SRAIW, RISC_V_MNEMONICS[ID_SRAIW])

            NID_ADDW = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_ADDW, RISC_V_MNEMONICS[ID_ADDW])
            NID_SUBW = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_SUBW, RISC_V_MNEMONICS[ID_SUBW])
            NID_SLLW = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_SLLW, RISC_V_MNEMONICS[ID_SLLW])
            NID_SRLW = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_SRLW, RISC_V_MNEMONICS[ID_SRLW])
            NID_SRAW = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_SRAW, RISC_V_MNEMONICS[ID_SRAW])

    # RV32M codes missing in RISC-U

    NID_F3_MULH   = new_constant(OP_CONST, SID_FUNCT3, F3_MULH, "F3_MULH")
    NID_F3_MULHSU = new_constant(OP_CONST, SID_FUNCT3, F3_MULHSU, "F3_MULHSU")
    NID_F3_MULHU  = new_constant(OP_CONST, SID_FUNCT3, F3_MULHU, "F3_MULHU")
    NID_F3_DIV    = new_constant(OP_CONST, SID_FUNCT3, F3_DIV, "F3_DIV")
    NID_F3_REM    = new_constant(OP_CONST, SID_FUNCT3, F3_REM, "F3_REM")

    # RV32M instruction switches

    if RISCUONLY:
        RV32M = True

    NID_MULH   = NID_DISABLED
    NID_MULHSU = NID_DISABLED
    NID_MULHU  = NID_DISABLED
    NID_DIV    = NID_DISABLED
    NID_REM    = NID_DISABLED

    if not RISCUONLY:
        if RV32M:
            # MUL, DIVU, REMU already defined
            NID_MULH   = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_MULH, RISC_V_MNEMONICS[ID_MULH])
            NID_MULHSU = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_MULHSU, RISC_V_MNEMONICS[ID_MULHSU])
            NID_MULHU  = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_MULHU, RISC_V_MNEMONICS[ID_MULHU])
            NID_DIV    = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_DIV, RISC_V_MNEMONICS[ID_DIV])
            NID_REM    = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_REM, RISC_V_MNEMONICS[ID_REM])
        else:
            NID_MUL  = NID_DISABLED
            NID_DIVU = NID_DISABLED
            NID_REMU = NID_DISABLED

    # RV64M instruction switches

    if RISCUONLY:
        RV64M = False

    if not IS64BITTARGET:
        RV64M = False

    if RV64M:
        NID_MULW  = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_MULW, RISC_V_MNEMONICS[ID_MULW])
        NID_DIVW  = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_DIVW, RISC_V_MNEMONICS[ID_DIVW])
        NID_DIVUW = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_DIVUW, RISC_V_MNEMONICS[ID_DIVUW])
        NID_REMW  = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_REMW, RISC_V_MNEMONICS[ID_REMW])
        NID_REMUW = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_REMUW, RISC_V_MNEMONICS[ID_REMUW])
    else:
        NID_MULW  = NID_DISABLED
        NID_DIVW  = NID_DISABLED
        NID_DIVUW = NID_DISABLED
        NID_REMW  = NID_DISABLED
        NID_REMUW = NID_DISABLED

# compressed instructions

def init_compressed_instruction_sorts():
    global SID_OPCODE_C

    global NID_OP_C0
    global NID_OP_C1
    global NID_OP_C2
    global NID_OP_C3

    global NID_F3_C_LI
    global NID_F3_C_LUI_ADDI16SP

    global NID_F3_C_ADDI
    global NID_F3_C_ADDIW_JAL

    global NID_F3_C_ADDI4SPN

    global NID_F3_C_SLLI
    global NID_F3_C_SRLI_SRAI_ANDI

    global SID_FUNCT2

    global NID_F2_C_SRLI
    global NID_F2_C_SRAI
    global NID_F2_C_ANDI

    global NID_F6_C_SUB_XOR_OR_AND
    global NID_F6_C_ADDW_SUBW

    global NID_F2_C_SUB_SUBW
    global NID_F2_C_XOR_ADDW
    global NID_F2_C_OR
    global NID_F2_C_AND

    global NID_F3_C_LWSP_LW
    global NID_F3_C_LDSP_LD

    global NID_F3_C_SWSP_SW
    global NID_F3_C_SDSP_SD

    global NID_F3_C_BEQZ
    global NID_F3_C_BNEZ

    global NID_F3_C_J

    global SID_FUNCT4

    global NID_F4_C_MV_JR
    global NID_F4_C_ADD_JALR

    global SID_1_BIT_OFFSET
    global SID_2_BIT_OFFSET
    global SID_3_BIT_OFFSET
    global SID_4_BIT_OFFSET
    global SID_5_BIT_OFFSET
    global SID_6_BIT_OFFSET
    global SID_7_BIT_OFFSET
    global SID_8_BIT_OFFSET
    global SID_9_BIT_OFFSET
    global SID_10_BIT_OFFSET
    global SID_11_BIT_OFFSET
    global SID_12_BIT_OFFSET
    global SID_17_BIT_OFFSET
    global SID_18_BIT_OFFSET

    global NID_1_BIT_OFFSET_0
    global NID_1_BIT_OFFSET_1
    global NID_2_BIT_OFFSET_0
    global NID_2_BIT_OFFSET_1
    global NID_3_BIT_OFFSET_0
    global NID_4_BIT_OFFSET_0
    global NID_12_BIT_OFFSET_0

    global SID_COMPRESSED_REGISTER_ADDRESS

    global RVC

    global NID_C_LI
    global NID_C_LUI

    global NID_C_ADDI
    global NID_C_ADDIW
    global NID_C_ADDI16SP

    global NID_C_ADDI4SPN

    global NID_C_ANDI

    global NID_C_SLLI
    global NID_C_SRLI
    global NID_C_SRAI

    global NID_C_MV
    global NID_C_ADD

    global NID_C_SUB
    global NID_C_XOR
    global NID_C_OR
    global NID_C_AND

    global NID_C_ADDW
    global NID_C_SUBW

    global NID_C_LWSP
    global NID_C_LW

    global NID_C_LDSP
    global NID_C_LD

    global NID_C_SWSP
    global NID_C_SW

    global NID_C_SDSP
    global NID_C_SD

    global NID_C_BEQZ
    global NID_C_BNEZ

    global NID_C_J
    global NID_C_JAL

    global NID_C_JR
    global NID_C_JALR

    init_compressed_instruction_codes()

    # RVC codes

    SID_OPCODE_C = new_bitvec(2, "compressed opcode sort")

    NID_OP_C0 = new_constant(OP_CONST, SID_OPCODE_C, 0, "OP_C0")
    NID_OP_C1 = new_constant(OP_CONST, SID_OPCODE_C, 1, "OP_C1")
    NID_OP_C2 = new_constant(OP_CONST, SID_OPCODE_C, 2, "OP_C2")
    NID_OP_C3 = new_constant(OP_CONST, SID_OPCODE_C, 3, "OP_C3")

    NID_F3_C_LI           = new_constant(OP_CONST, SID_FUNCT3, F3_C_LI, "F3_C_LI")
    NID_F3_C_LUI_ADDI16SP = new_constant(OP_CONST, SID_FUNCT3, F3_C_LUI_ADDI16SP, "F3_C_LUI_ADDI16SP")

    NID_F3_C_ADDI      = new_constant(OP_CONST, SID_FUNCT3, F3_C_ADDI, "F3_C_ADDI")
    NID_F3_C_ADDIW_JAL = new_constant(OP_CONST, SID_FUNCT3, F3_C_ADDIW_JAL, "F3_C_ADDIW_JAL")

    NID_F3_C_ADDI4SPN = new_constant(OP_CONST, SID_FUNCT3, F3_C_ADDI4SPN, "F3_C_ADDI4SPN")

    NID_F3_C_SLLI           = new_constant(OP_CONST, SID_FUNCT3, F3_C_SLLI, "F3_C_SLLI")
    NID_F3_C_SRLI_SRAI_ANDI = new_constant(OP_CONST, SID_FUNCT3, F3_C_SRLI_SRAI_ANDI, "F3_C_SRLI_SRAI_ANDI")

    SID_FUNCT2 = new_bitvec(2, "compressed funct2 sort")

    NID_F2_C_SRLI = new_constant(OP_CONST, SID_FUNCT2, F2_C_SRLI, "F2_C_SRLI")
    NID_F2_C_SRAI = new_constant(OP_CONST, SID_FUNCT2, F2_C_SRAI, "F2_C_SRAI")
    NID_F2_C_ANDI = new_constant(OP_CONST, SID_FUNCT2, F2_C_ANDI, "F2_C_ANDI")

    NID_F6_C_SUB_XOR_OR_AND = new_constant(OP_CONST, SID_FUNCT6, F6_C_SUB_XOR_OR_AND, "F6_C_SUB_XOR_OR_AND")
    NID_F6_C_ADDW_SUBW      = new_constant(OP_CONST, SID_FUNCT6, F6_C_ADDW_SUBW, "F6_C_ADDW_SUBW")

    NID_F2_C_SUB_SUBW = new_constant(OP_CONST, SID_FUNCT2, F2_C_SUB_SUBW, "F2_C_SUB_SUBW")
    NID_F2_C_XOR_ADDW = new_constant(OP_CONST, SID_FUNCT2, F2_C_XOR_ADDW, "F2_C_XOR_ADDW")
    NID_F2_C_OR       = new_constant(OP_CONST, SID_FUNCT2, F2_C_OR, "F2_C_OR")
    NID_F2_C_AND      = new_constant(OP_CONST, SID_FUNCT2, F2_C_AND, "F2_C_AND")

    NID_F3_C_LWSP_LW = new_constant(OP_CONST, SID_FUNCT3, F3_C_LWSP_LW, "F3_C_LWSP_LW")
    NID_F3_C_LDSP_LD = new_constant(OP_CONST, SID_FUNCT3, F3_C_LDSP_LD, "F3_C_LDSP_LD")

    NID_F3_C_SWSP_SW = new_constant(OP_CONST, SID_FUNCT3, F3_C_SWSP_SW, "F3_C_SWSP_SW")
    NID_F3_C_SDSP_SD = new_constant(OP_CONST, SID_FUNCT3, F3_C_SDSP_SD, "F3_C_SDSP_SD")

    NID_F3_C_BEQZ = new_constant(OP_CONST, SID_FUNCT3, F3_C_BEQZ, "F3_C_BEQZ")
    NID_F3_C_BNEZ = new_constant(OP_CONST, SID_FUNCT3, F3_C_BNEZ, "F3_C_BNEZ")

    NID_F3_C_J = new_constant(OP_CONST, SID_FUNCT3, F3_C_J, "F3_C_J")

    SID_FUNCT4 = new_bitvec(4, "compressed funct4 sort")

    NID_F4_C_MV_JR    = new_constant(OP_CONST, SID_FUNCT4, F4_C_MV_JR, "F4_C_MV_JR")
    NID_F4_C_ADD_JALR = new_constant(OP_CONST, SID_FUNCT4, F4_C_ADD_JALR, "F4_C_ADD_JALR")

    # offset sorts

    SID_1_BIT_OFFSET  = new_bitvec(1, "1-bit offset sort")
    SID_2_BIT_OFFSET  = new_bitvec(2, "2-bit offset sort")
    SID_3_BIT_OFFSET  = new_bitvec(3, "3-bit offset sort")
    SID_4_BIT_OFFSET  = new_bitvec(4, "4-bit offset sort")
    SID_5_BIT_OFFSET  = new_bitvec(5, "5-bit offset sort")
    SID_6_BIT_OFFSET  = new_bitvec(6, "6-bit offset sort")
    SID_7_BIT_OFFSET  = new_bitvec(7, "7-bit offset sort")
    SID_8_BIT_OFFSET  = new_bitvec(8, "8-bit offset sort")
    SID_9_BIT_OFFSET  = new_bitvec(9, "9-bit offset sort")
    SID_10_BIT_OFFSET = new_bitvec(10, "10-bit offset sort")
    SID_11_BIT_OFFSET = new_bitvec(11, "11-bit offset sort")
    SID_12_BIT_OFFSET = new_bitvec(12, "12-bit offset sort")
    SID_17_BIT_OFFSET = new_bitvec(17, "17-bit offset sort")
    SID_18_BIT_OFFSET = new_bitvec(18, "18-bit offset sort")

    NID_1_BIT_OFFSET_0  = new_constant(OP_CONST, SID_1_BIT_OFFSET, 0, "1-bit offset 0")
    NID_1_BIT_OFFSET_1  = new_constant(OP_CONST, SID_1_BIT_OFFSET, 1, "1-bit offset 1")
    NID_2_BIT_OFFSET_0  = new_constant(OP_CONST, SID_2_BIT_OFFSET, 0, "2-bit offset 0")
    NID_2_BIT_OFFSET_1  = new_constant(OP_CONST, SID_2_BIT_OFFSET, 1, "2-bit offset 1, 01000 s0")
    NID_3_BIT_OFFSET_0  = new_constant(OP_CONST, SID_3_BIT_OFFSET, 0, "3-bit offset 0")
    NID_4_BIT_OFFSET_0  = new_constant(OP_CONST, SID_4_BIT_OFFSET, 0, "4-bit offset 0")
    NID_12_BIT_OFFSET_0 = new_constant(OP_CONST, SID_12_BIT_OFFSET, 0, "12-bit offset 0")

    SID_COMPRESSED_REGISTER_ADDRESS = new_bitvec(3, "3-bit compressed register address")

    # RVC instruction switches

    if RISCUONLY:
        RVC = False

    NID_C_LI  = NID_DISABLED
    NID_C_LUI = NID_DISABLED

    NID_C_ADDI     = NID_DISABLED
    NID_C_ADDIW    = NID_DISABLED
    NID_C_ADDI16SP = NID_DISABLED

    NID_C_ADDI4SPN = NID_DISABLED

    NID_C_ANDI = NID_DISABLED

    NID_C_SLLI = NID_DISABLED
    NID_C_SRLI = NID_DISABLED
    NID_C_SRAI = NID_DISABLED

    NID_C_MV  = NID_DISABLED
    NID_C_ADD = NID_DISABLED

    NID_C_SUB = NID_DISABLED
    NID_C_XOR = NID_DISABLED
    NID_C_OR  = NID_DISABLED
    NID_C_AND = NID_DISABLED

    NID_C_ADDW = NID_DISABLED
    NID_C_SUBW = NID_DISABLED

    NID_C_LWSP = NID_DISABLED
    NID_C_LW   = NID_DISABLED

    NID_C_LDSP = NID_DISABLED
    NID_C_LD   = NID_DISABLED

    NID_C_SWSP = NID_DISABLED
    NID_C_SW   = NID_DISABLED

    NID_C_SDSP = NID_DISABLED
    NID_C_SD   = NID_DISABLED

    NID_C_BEQZ = NID_DISABLED
    NID_C_BNEZ = NID_DISABLED

    NID_C_J   = NID_DISABLED
    NID_C_JAL = NID_DISABLED

    NID_C_JR   = NID_DISABLED
    NID_C_JALR = NID_DISABLED

    if not RVC:
        # avoiding oversized then case
        return

    NID_C_LI  = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_LI, RISC_V_MNEMONICS[ID_C_LI])
    NID_C_LUI = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_LUI, RISC_V_MNEMONICS[ID_C_LUI])

    NID_C_ADDI = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_ADDI, RISC_V_MNEMONICS[ID_C_ADDI])
    if IS64BITTARGET:
        NID_C_ADDIW = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_ADDIW, RISC_V_MNEMONICS[ID_C_ADDIW])
    else:
        NID_C_ADDIW = NID_DISABLED
    NID_C_ADDI16SP = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_ADDI16SP, RISC_V_MNEMONICS[ID_C_ADDI16SP])

    NID_C_ADDI4SPN = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_ADDI4SPN, RISC_V_MNEMONICS[ID_C_ADDI4SPN])

    NID_C_ANDI = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_ANDI, RISC_V_MNEMONICS[ID_C_ANDI])

    NID_C_SLLI = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_SLLI, RISC_V_MNEMONICS[ID_C_SLLI])
    NID_C_SRLI = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_SRLI, RISC_V_MNEMONICS[ID_C_SRLI])
    NID_C_SRAI = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_SRAI, RISC_V_MNEMONICS[ID_C_SRAI])

    NID_C_MV  = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_MV, RISC_V_MNEMONICS[ID_C_MV])
    NID_C_ADD = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_ADD, RISC_V_MNEMONICS[ID_C_ADD])

    NID_C_SUB = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_SUB, RISC_V_MNEMONICS[ID_C_SUB])
    NID_C_XOR = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_XOR, RISC_V_MNEMONICS[ID_C_XOR])
    NID_C_OR  = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_OR, RISC_V_MNEMONICS[ID_C_OR])
    NID_C_AND = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_AND, RISC_V_MNEMONICS[ID_C_AND])

    if IS64BITTARGET:
        NID_C_ADDW = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_ADDW, RISC_V_MNEMONICS[ID_C_ADDW])
        NID_C_SUBW = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_SUBW, RISC_V_MNEMONICS[ID_C_SUBW])
    else:
        NID_C_ADDW = NID_DISABLED
        NID_C_SUBW = NID_DISABLED

    NID_C_LWSP = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_LWSP, RISC_V_MNEMONICS[ID_C_LWSP])
    NID_C_LW   = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_LW, RISC_V_MNEMONICS[ID_C_LW])

    NID_C_SWSP = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_SWSP, RISC_V_MNEMONICS[ID_C_SWSP])
    NID_C_SW   = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_SW, RISC_V_MNEMONICS[ID_C_SW])

    if IS64BITTARGET:
        NID_C_LDSP = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_LDSP, RISC_V_MNEMONICS[ID_C_LDSP])
        NID_C_LD   = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_LD, RISC_V_MNEMONICS[ID_C_LD])

        NID_C_SDSP = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_SDSP, RISC_V_MNEMONICS[ID_C_SDSP])
        NID_C_SD   = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_SD, RISC_V_MNEMONICS[ID_C_SD])
    else:
        NID_C_LDSP = NID_DISABLED
        NID_C_LD   = NID_DISABLED

        NID_C_SDSP = NID_DISABLED
        NID_C_SD   = NID_DISABLED

    NID_C_BEQZ = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_BEQZ, RISC_V_MNEMONICS[ID_C_BEQZ])
    NID_C_BNEZ = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_BNEZ, RISC_V_MNEMONICS[ID_C_BNEZ])

    NID_C_J = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_J, RISC_V_MNEMONICS[ID_C_J])
    if IS64BITTARGET:
        NID_C_JAL = NID_DISABLED
    else:
        NID_C_JAL = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_JAL, RISC_V_MNEMONICS[ID_C_JAL])

    NID_C_JR   = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_JR, RISC_V_MNEMONICS[ID_C_JR])
    NID_C_JALR = new_constant(OP_CONSTD, SID_INSTRUCTION_ID, ID_C_JALR, RISC_V_MNEMONICS[ID_C_JALR])

# system model

class Bitvector_State:
    def __init__(self, core, sid, name, initials):
        assert isinstance(sid, Bitvector), f"got {sid} but expected bitvector"
        self.sid = sid
        if core >= 0:
            self.initial = new_constant(OP_CONSTD, self.sid, 0, f"initial core-{core} {name} value")
            self.state = new_input(OP_STATE, self.sid, f"core-{core}-{initials}", f"{sid.size}-bit {name}")
        else:
            self.initial = new_constant(OP_CONSTD, self.sid, 0, f"initial {name} value")
            self.state = new_input(OP_STATE, self.sid, f"{initials}", f"{sid.size}-bit {name}")
        self.init = new_init(self.sid, self.state, self.initial, f"initializing {name}")

    def __str__(self):
        return f"{self.state}"

class Array_State:
    def __init__(self, core, array_sid, name, initials):
        assert isinstance(array_sid, Array), f"got {array_sid} but expected array"
        self.array_sid = array_sid
        if core >= 0:
            self.initial = new_constant(OP_CONSTD, array_sid.element_size_line, 0, f"initial core-{core} {name} value")
            self.state = new_input(OP_STATE, array_sid, f"core-{core}-{initials}", f"{array_sid.array_size_line.size}-bit {name} of {array_sid.element_size_line.size}-bit bitvectors")
        else:
            self.initial = new_constant(OP_CONSTD, array_sid.element_size_line, 0, f"initial {name} value")
            self.state = new_input(OP_STATE, array_sid, f"{initials}", f"{array_sid.array_size_line.size}-bit {name} of {array_sid.element_size_line.size}-bit bitvectors")
        self.init = new_init(array_sid, self.state, self.initial, f"initializing {name}")

    def __str__(self):
        return f"{self.state}"

class PC(Bitvector_State):
    def __init__(self, core):
        super().__init__(core, SID_MACHINE_WORD, "program counter", 'pc')

class Registers(Array_State):
    def __init__(self, core):
        super().__init__(core, SID_REGISTER_STATE, "register file", 'register-file')

class Segment(Array_State):
    def __init__(self, core, array_sid, start_nid, end_nid, name, initials):
        assert isinstance(array_sid, Array) and isinstance(start_nid, Constant) and isinstance(end_nid, Constant)
        super().__init__(core, array_sid, name, initials)
        self.start_nid = start_nid
        self.end_nid = end_nid

class Memory:
    def __init__(self, core):
        self.vaddr_sort_nid = SID_VIRTUAL_ADDRESS
        self.code = Segment(core, SID_CODE_STATE, NID_CODE_START, NID_CODE_END, "code segment", 'code-segment')
        self.data = Segment(core, SID_DATA_STATE, NID_DATA_START, NID_DATA_END, "data segment", 'data-segment')
        self.heap = Segment(core, SID_HEAP_STATE, NID_HEAP_START, NID_HEAP_END, "heap segment", 'heap-segment')
        self.stack = Segment(core, SID_STACK_STATE, NID_STACK_START, NID_STACK_END, "stack segment", 'stack-segment')

    def __str__(self):
        return f"{self.vaddr_sort_nid.size}-bit virtual memory:\n{self.code}\n{self.data}\n{self.heap}\n{self.stack}"

class Kernel:
    def __init__(self, core, memory):
        assert isinstance(memory, Memory), f"got {memory} but expected memory"
        self.memory = memory
        self.program_break = Bitvector_State(-1, memory.vaddr_sort_nid, "program break", 'program-break')
        self.file_descriptor = Bitvector_State(-1, SID_MACHINE_WORD, "file descriptor", 'file-descriptor')
        self.input_buffer = Array_State(-1, SID_INPUT_BUFFER, "input buffer", 'input-buffer')
        self.readable_bytes = Bitvector_State(core, SID_MACHINE_WORD, "readable bytes", 'readable-bytes')
        self.read_bytes = Bitvector_State(core, SID_MACHINE_WORD, "read bytes", 'read-bytes')

    def __str__(self):
        return f"kernel:\n{self.program_break}\n{self.file_descriptor}\n{self.input_buffer}\n{self.readable_bytes}\n{self.read_bytes}"

class Core:
    cores = {}

    def __init__(self):
        self.core = len(Core.cores)
        self.memory = Memory(self.core)
        self.kernel = Kernel(self.core, self.memory)
        self.pc = PC(self.core)
        self.regs = Registers(self.core)
        self.new_core()

    def __str__(self):
        return f"{self.kernel}\n{self.memory}\ncore-{self.core}:\n{self.pc}\n{self.regs}"

    def new_core(self):
        assert self.core not in Core.cores, f"{self.core} already defined"
        Core.cores[self.core] = self

class System:
    def __init__(self):
        self.core = Core() # single core for now

    def __str__(self):
        return f"{SID_MACHINE_WORD.size}-bit single-core system:\n{self.core}"

# console output

def get_step(step, level):
    if step is None or level is None:
        return ""
    elif level == 0:
        return f"{step}: "
    else:
        return f"{step}-{level}: "

last_message_length = 0

def print_message(message, step = None, level = None):
    global last_message_length
    if last_message_length > 0:
        print("\r%s" % (" " * last_message_length), end='\r')
    message = f"{get_step(step, level)}{message}"
    print(message, end='', flush=True)
    last_message_length = len(message) if message[-1:] != '\n' else 0

def print_message_with_propagation_profile(message, step = None, level = None):
    if Instance.PROPAGATE is not None:

        BVDD.total_number_of_solutions += BVDD.number_of_solutions
        BVDD.max_number_of_solutions = max(BVDD.max_number_of_solutions, BVDD.number_of_solutions)
        BVDD.avg_number_of_solutions += BVDD.number_of_solutions
        if BVDD.avg_number_of_solutions > BVDD.number_of_solutions:
            BVDD.avg_number_of_solutions //= 2
        string = f"({Values.total_number_of_constants}, {BVDD.total_number_of_solutions}, "
        string += f"{BVDD.max_number_of_solutions}, {BVDD.number_of_solutions}, {BVDD.avg_number_of_solutions}, "
        string += f"{Expression.total_number_of_generated_expressions}) {message}"
        print_message(string, step, level)
        BVDD.number_of_solutions = 0
    else:
        print_message(message, step, level)

def print_separator(separator, step = None, level = None):
    print_message(f"{separator * (80 - len(get_step(step, level)))}\n", step, level)

# BTOR2 parser

import re

class syntax_error(Exception):
    def __init__(self, expected, line_no):
        super().__init__(f"syntax error in line {line_no}: {expected} expected")

def tokenize_btor2(line):
    # comment, non-comment no-space printable string,
    # signed integer, binary number, hexadecimal number
    btor2_token_pattern = r"(;.*|[^; \n\r]+|-?\d+|[0-1]|[0-9a-fA-F]+)"
    tokens = re.findall(btor2_token_pattern, line)
    return tokens

def get_token(tokens, expected, line_no):
    try:
        return tokens.pop(0)
    except:
        raise syntax_error(expected, line_no)

def get_decimal(tokens, expected, line_no):
    token = get_token(tokens, expected, line_no)
    if token.isdecimal():
        return int(token)
    else:
        raise syntax_error(expected, line_no)

def get_nid(tokens, expected, line_no):
    return Array.accommodate_array_indexes(get_decimal(tokens, expected, line_no))

def get_nid_line(tokens, clss, expected, line_no):
    nid = get_nid(tokens, expected, line_no)
    if Line.is_defined(nid):
        line = Line.get(nid)
        if isinstance(line, clss):
            return line
        else:
            raise syntax_error(expected, line_no)
    else:
        raise syntax_error(f"defined {expected}", line_no)

def get_bool_or_bitvec_sid_line(tokens, line_no):
    return get_nid_line(tokens, Bitvector, "Boolean or bitvector sort nid", line_no)

def get_bitvec_sid_line(tokens, line_no):
    return get_nid_line(tokens, Bitvec, "bitvector sort nid", line_no)

def get_sid_line(tokens, line_no):
    return get_nid_line(tokens, Sort, "sort nid", line_no)

def get_state_line(tokens, line_no):
    return get_nid_line(tokens, State, "state nid", line_no)

def get_exp_line(tokens, line_no):
    return get_nid_line(tokens, Expression, "expression nid", line_no)

def get_number(tokens, base, expected, line_no):
    token = get_token(tokens, expected, line_no)
    try:
        if (base == 10):
            return int(token)
        else:
            return int(token, base)
    except ValueError:
        raise syntax_error(expected, line_no)

def get_symbol(tokens):
    try:
        return get_token(tokens, None, None)
    except:
        return ""

def get_comment(tokens, line_no):
    comment = get_symbol(tokens)
    if comment:
        if comment[0] != ';':
            raise syntax_error("comment", line_no)
    return comment

def parse_sort_line(tokens, nid, line_no):
    token = get_token(tokens, "bitvector or array", line_no)
    if token == Bitvec.keyword:
        size = get_decimal(tokens, "bitvector size", line_no)
        comment = get_comment(tokens, line_no)
        # beator- and rotor-dependent Boolean declaration
        if comment == "; Boolean" and size == 1:
            return new_boolean(nid, line_no)
        else:
            return new_bitvec(size, comment, nid, line_no)
    elif token == Array.keyword:
        array_size_line = get_bitvec_sid_line(tokens, line_no)
        element_size_line = get_bitvec_sid_line(tokens, line_no)
        comment = get_comment(tokens, line_no)
        return new_array(array_size_line, element_size_line, comment, nid, line_no)
    else:
        raise syntax_error("bitvector or array", line_no)

def parse_zero_one_line(tokens, nid, op, line_no):
    sid_line = get_bool_or_bitvec_sid_line(tokens, line_no)
    symbol, comment = parse_symbol_comment(tokens, line_no)
    return new_zero_one(op, sid_line, symbol, comment, nid, line_no)

def parse_constant_line(tokens, nid, op, line_no):
    sid_line = get_bool_or_bitvec_sid_line(tokens, line_no)
    if op == Constd.keyword:
        value = get_number(tokens, 10, "signed integer", line_no)
    elif op == Const.keyword:
        value = get_number(tokens, 2, "binary number", line_no)
    elif op == Consth.keyword:
        value = get_number(tokens, 16, "hexadecimal number", line_no)
    comment = get_comment(tokens, line_no)
    return new_constant(op, sid_line, value, comment, nid, line_no)

def parse_symbol_comment(tokens, line_no):
    symbol = get_symbol(tokens)
    comment = get_comment(tokens, line_no)
    if symbol:
        if symbol[0] == ';':
            return "", symbol
    return symbol, comment

def parse_variable_line(tokens, nid, op, line_no):
    sid_line = get_sid_line(tokens, line_no)
    symbol, comment = parse_symbol_comment(tokens, line_no)
    return new_input(op, sid_line, symbol, comment, nid, line_no)

def parse_ext_line(tokens, nid, op, line_no):
    sid_line = get_sid_line(tokens, line_no)
    arg1_line = get_exp_line(tokens, line_no)
    w = get_decimal(tokens, "bit width", line_no)
    comment = get_comment(tokens, line_no)
    return new_ext(op, sid_line, arg1_line, w, comment, nid, line_no)

def parse_slice_line(tokens, nid, line_no):
    sid_line = get_sid_line(tokens, line_no)
    arg1_line = get_exp_line(tokens, line_no)
    u = get_decimal(tokens, "upper bit", line_no)
    l = get_decimal(tokens, "lower bit", line_no)
    comment = get_comment(tokens, line_no)
    return new_slice(sid_line, arg1_line, u, l, comment, nid, line_no)

def parse_unary_line(tokens, nid, op, line_no):
    sid_line = get_sid_line(tokens, line_no)
    arg1_line = get_exp_line(tokens, line_no)
    comment = get_comment(tokens, line_no)
    return new_unary(op, sid_line, arg1_line, comment, nid, line_no)

def parse_binary_line(tokens, nid, op, line_no):
    sid_line = get_sid_line(tokens, line_no)
    arg1_line = get_exp_line(tokens, line_no)
    arg2_line = get_exp_line(tokens, line_no)
    comment = get_comment(tokens, line_no)
    return new_binary(op, sid_line, arg1_line, arg2_line, comment, nid, line_no)

def parse_ternary_line(tokens, nid, op, line_no):
    sid_line = get_sid_line(tokens, line_no)
    arg1_line = get_exp_line(tokens, line_no)
    arg2_line = get_exp_line(tokens, line_no)
    arg3_line = get_exp_line(tokens, line_no)
    comment = get_comment(tokens, line_no)
    return new_ternary(op, sid_line, arg1_line, arg2_line, arg3_line, comment, nid, line_no)

def parse_init_next_line(tokens, nid, op, line_no):
    sid_line = get_sid_line(tokens, line_no)
    state_line = get_state_line(tokens, line_no)
    exp_line = get_exp_line(tokens, line_no)
    symbol, comment = parse_symbol_comment(tokens, line_no)
    return new_init_next(op, sid_line, state_line, exp_line, symbol, comment, nid, line_no)

def parse_property_line(tokens, nid, op, line_no):
    property_line = get_exp_line(tokens, line_no)
    symbol, comment = parse_symbol_comment(tokens, line_no)
    return new_property(op, property_line, symbol, comment, nid, line_no)

def parse_btor2_line(line, line_no):
    global current_nid # only necessary for mapping arrays

    if line.strip():
        tokens = tokenize_btor2(line)
        token = get_token(tokens, None, None)
        if token[0] != ';':
            if token.isdecimal():
                nid = Array.accommodate_array_indexes(int(token))
                if nid > current_nid:
                    current_nid = nid
                    token = get_token(tokens, "keyword", line_no)
                    if token == Sort.keyword:
                        return parse_sort_line(tokens, nid, line_no)
                    elif token in {Zero.keyword, One.keyword}:
                        return parse_zero_one_line(tokens, nid, token, line_no)
                    elif token in {Constd.keyword, Const.keyword, Consth.keyword}:
                        return parse_constant_line(tokens, nid, token, line_no)
                    elif token in Variable.keywords:
                        return parse_variable_line(tokens, nid, token, line_no)
                    elif token in Ext.keywords:
                        return parse_ext_line(tokens, nid, token, line_no)
                    elif token == Slice.keyword:
                        return parse_slice_line(tokens, nid, line_no)
                    elif token in Unary.keywords:
                        return parse_unary_line(tokens, nid, token, line_no)
                    elif token in Binary.keywords:
                        return parse_binary_line(tokens, nid, token, line_no)
                    elif token in Ternary.keywords:
                        return parse_ternary_line(tokens, nid, token, line_no)
                    elif token in {Init.keyword, Next.keyword}:
                        return parse_init_next_line(tokens, nid, token, line_no)
                    elif token in Property.keywords:
                        return parse_property_line(tokens, nid, token, line_no)
                    else:
                        raise syntax_error(f"unknown operator {token}", line_no)
                raise syntax_error("increasing nid", line_no)
            raise syntax_error("nid", line_no)
    return line.strip()

def parse_btor2(modelfile, outputfile):
    print_separator('#')
    print(f"model file: {modelfile.name}")

    lines = {}
    line_no = 1
    for line in modelfile:
        try:
            lines[line_no] = parse_btor2_line(line, line_no)
            line_no += 1
        except (model_error, syntax_error) as message:
            print(f"parsing exception: {message}")
            exit(1)

    # start: mapping arrays to bitvectors

    if Array.ARRAY_SIZE_BOUND > 0:
        for init in Init.inits.values():
            init.set_mapped_array_expression()
        for constraint in Constraint.constraints.values():
            constraint.set_mapped_array_expression()
        for bad in Bad.bads.values():
            bad.set_mapped_array_expression()
        for next_line in Next.nexts.values():
            next_line.set_mapped_array_expression()

        for state in list(State.states.values()):
            if isinstance(state.sid_line, Bitvector):
                if state.init_line is not None and state.next_line is not None:
                    if state.init_line.exp_line is state.next_line.exp_line or state.next_line.exp_line is state:
                        # remove initialized read-only bitvector states
                        state.remove_state()
                        Transitional.remove_transition(state, Init.inits)
                        Transitional.remove_transition(state, Next.nexts)

        if Ite.branching_conditions and Ite.non_branching_conditions:
            Ite.branching_conditions.get_mapped_array_expression_for(None)
            Ite.non_branching_conditions.get_mapped_array_expression_for(None)

    # end: mapping arrays to bitvectors

    for state in State.states.values():
        if state.init_line is None:
            # state has no init
            state.new_input(state.index)

    are_there_uninitialized_states = False
    are_there_untransitioned_states = False
    are_there_state_transitions = False

    for state in State.states.values():
        if state.init_line is None:
            are_there_uninitialized_states = True
        if state.next_line is None:
            are_there_untransitioned_states = True
        else:
            are_there_state_transitions = True

    print_separator('-')

    if are_there_state_transitions:
        print("sequential problem:")
    else:
        print("combinational problem:")

    for input_line in Input.inputs.values():
        if isinstance(input_line, Input):
            print(input_line)
    for state in State.states.values():
        print(state)

    if are_there_uninitialized_states:
        print("uninitialized states:")
        for state in State.states.values():
            if state.init_line is None:
                print(state)
    if are_there_untransitioned_states:
        print("untransitioned states:")
        for state in State.states.values():
            if state.next_line is None:
                print(state)

    if Ite.branching_conditions and Ite.non_branching_conditions:
        print("branching conditions:")
        print(Ite.branching_conditions)
        print(Ite.non_branching_conditions)

    print("model profile:")
    print(f"{len(Line.lines)} lines in total")
    print(f"{Input.count} input, {State.count} state, {Init.count} init, {Next.count} next, {Constraint.count} constraint, {Bad.count} bad")
    print(f"{Bool.count} bool, {Bitvec.count} bitvec, {Array.count} array")
    print(f"{Zero.count} zero, {One.count} one, {Constd.count} constd, {Const.count} const, {Consth.count} consth")
    print(f"{Ext.count} ext, {Slice.count} slice, {Unary.count} unary")
    print(f"{Implies.count} implies, {Comparison.count} comparison, {Logical.count} logical, {Computation.count} computation")
    print(f"{Concat.count} concat, {Ite.count} ite, {Read.count} read, {Write.count} write")

    if Array.ARRAY_SIZE_BOUND > 0:
        print("array mapping profile:")
        print(f"out of {Array.number_of_variable_arrays} arrays {Array.number_of_mapped_arrays} mapped")
        print(f"{Expression.total_number_of_generated_expressions} generated expressions")
        Expression.total_number_of_generated_expressions = 0

    if outputfile:
        print_separator('-')
        print(f"output file: {outputfile.name}")
        for line in lines.values():
            print(line, file=outputfile)

    return are_there_state_transitions

# Z3 and bitwuzla solver interface

class Solver:
    def __init__(self, solver):
        self.solver = solver

    def push(self):
        self.solver.push()

    def pop(self):
        self.solver.pop()

class Z3_Solver(Solver):
    def __init__(self):
        super().__init__(z3.Solver())

    def assert_this(self, assertions, step):
        for assertion in assertions:
            self.solver.add(assertion.get_z3_step(step))

    def assert_not_this(self, assertions, step):
        for assertion in assertions:
            self.solver.add(assertion.get_z3_step(step) == False)

    def simplify(self):
        # no effective simplification yet found in Z3
        return self

    def prove(self):
        return self.solver.check()

    def is_SAT(self, result):
        return result == z3.sat

    def is_UNSAT(self, result):
        return result == z3.unsat

    def assert_is_state_changing(self, next_line, step):
        return self.solver.add(next_line.get_z3_is_state_changing(step))

    def assert_state_is_not_changing(self, next_line, step):
        return self.solver.add(next_line.get_z3_state_is_not_changing(step))

    def print_pc(self, pc, step, level):
        self.prove()
        model = self.solver.model()
        print_message(f"{pc}\n", step, level)
        print_message("%s = 0x%X\n" % (pc.get_z3_name(step),
            int(model.evaluate(pc.get_z3_instance(step - 1)).as_long())), step, level)

    def print_inputs(self, inputs, step, level):
        model = self.solver.model()
        for input_variable in inputs.values():
            # only print value of uninitialized states
            print_message(f"{input_variable}\n", step, level)
            print_message("%s = %s\n" % (input_variable.get_z3_name(step),
                model.evaluate(input_variable.get_z3_instance(step - 1))), step, level)

class Bitwuzla_Solver(Solver):
    def __init__(self):
        self.tm = bitwuzla.TermManager()
        self.options = bitwuzla.Options()
        self.options.set(bitwuzla.Option.PRODUCE_MODELS, True)
        super().__init__(bitwuzla.Bitwuzla(self.tm, self.options))

    def assert_this(self, assertions, step):
        for assertion in assertions:
            self.solver.assert_formula(assertion.get_bitwuzla_step(step, self.tm))

    def assert_not_this(self, assertions, step):
        for assertion in assertions:
            self.solver.assert_formula(self.tm.mk_term(bitwuzla.Kind.NOT, [assertion.get_bitwuzla_step(step, self.tm)]))

    def simplify(self):
        # possibly increases performance
        return self.prove()

    def prove(self):
        return self.solver.check_sat()

    def is_SAT(self, result):
        return result is bitwuzla.Result.SAT

    def is_UNSAT(self, result):
        return result is bitwuzla.Result.UNSAT

    def assert_is_state_changing(self, next_line, step):
        return self.solver.assert_formula(next_line.get_bitwuzla_is_state_changing(step, self.tm))

    def assert_state_is_not_changing(self, next_line, step):
        return self.solver.assert_formula(next_line.get_bitwuzla_state_is_not_changing(step, self.tm))

    def print_pc(self, pc, step, level):
        self.prove()
        pc_value = int(self.solver.get_value(pc.get_bitwuzla_instance(step - 1, self.tm)).value(16), 16)
        print_message(f"{pc}\n", step, level)
        print_message("%s = 0x%X\n" % (pc.get_bitwuzla_name(step, self.tm), pc_value), step, level)

    def print_inputs(self, inputs, step, level):
        for input_variable in inputs.values():
            # only print value of uninitialized states
            print_message(f"{input_variable}\n", step, level)
            print_message("%s = %s\n" % (input_variable.get_bitwuzla_name(step, self.tm),
                self.solver.get_value(input_variable.get_bitwuzla_instance(step - 1, self.tm))),
                step, level)

# bitme bounded model checker

def branching_bmc(solver, kmin, kmax, args, step, level):
    while step <= kmax:
        # check model up to kmax steps

        if args.print_pc and State.pc:
            # print current program counter value of single-core rotor model
            solver.print_pc(State.pc, step, level)

        # assert all constraints
        solver.assert_this(Constraint.constraints.values(), step)

        if step >= kmin:
            # check bad properties from kmin on
            for bad in Bad.bads.values():
                print_message_with_propagation_profile(bad.symbol, step, level)
                solver.push()
                solver.assert_this([bad], step)
                result = solver.prove()
                if solver.is_SAT(result):
                    print_separator('v', step, level)
                    print_message(f"{bad}\n", step, level)
                    solver.print_inputs(Variable.inputs, step, level)
                    if Instance.PROPAGATE is not None:
                        print_message_with_propagation_profile("propagation profile\n", step, level)
                    print_separator('^', step, level)
                solver.pop()

        if not args.unconstraining_bad:
            # assert all bad properties as negated constraints
            solver.assert_not_this(Bad.bads.values(), step)

        if args.check_termination and step >= kmin:
            state_change = False
            for next_line in Next.nexts.values():
                # check if state changes
                solver.push()
                solver.assert_is_state_changing(next_line, step)
                result = solver.prove()
                solver.pop()
                if solver.is_SAT(result):
                    state_change = True
                    print_message(f"state change: {next_line}\n", step, level)
                    # compute next step
                    solver.assert_this([next_line], step)
                else:
                    solver.assert_state_is_not_changing(next_line, step)
                if not state_change and next_line is list(Next.nexts.values())[-1]:
                    print_message_with_propagation_profile("no states changed: terminating\n", step, level)
                    return
        else:
            # compute next step
            solver.assert_this(Next.nexts.values(), step)

        if args.print_transition:
            print_message_with_propagation_profile("transitioning\n", step, level)
        else:
            print_message("transitioning", step, level)
        solver.simplify()

        if args.branching and Ite.branching_conditions and Ite.non_branching_conditions:
            print_message_with_propagation_profile("checking branching", step, level)

            solver.push()
            solver.assert_this([Ite.branching_conditions], step)
            branching_result = solver.is_SAT(solver.prove())
            solver.pop()

            solver.push()
            solver.assert_not_this([Ite.non_branching_conditions], step)
            non_branching_result = solver.is_SAT(solver.prove())
            solver.pop()

            if branching_result != non_branching_result:
                if branching_result:
                    solver.assert_this([Ite.branching_conditions], step)
                elif non_branching_result:
                    solver.assert_not_this([Ite.non_branching_conditions], step)

            if branching_result and non_branching_result:
                print_separator('v', step, level)
                print_message("branching:\n", step, level)

                solver.push()
                solver.assert_this([Ite.branching_conditions], step)
                branching_bmc(solver, kmin, kmax, args, step + 1, level + 1)
                solver.pop()

                print_separator('-', step, level)
                print_message("not branching:\n", step, level)

                solver.push()
                solver.assert_not_this([Ite.non_branching_conditions], step)
                branching_bmc(solver, kmin, kmax, args, step + 1, level + 1)
                solver.pop()

                print_separator('^', step, level)
                return

        step += 1

    print_message_with_propagation_profile("reached kmax: terminating\n", step, level)

def bmc(solver, kmin, kmax, args):
    print_separator('-')
    print_message(f"bounded model checking: -kmin {kmin} -kmax {kmax}\n")
    print_separator('-')

    # initialize all states
    solver.assert_this(Init.inits.values(), 0)

    print_message("initializing", 0, 0)
    solver.simplify()

    branching_bmc(solver, kmin, kmax, args, 0, 0)

# rotor model generator

def load_binary():
    global max_code_size

    global code_start
    global code_size

    global max_data_size

    global data_start
    global data_size

    global heap_initial_size

    global heap_start
    global heap_size

    global stack_initial_size

    global stack_start
    global stack_size

    max_code_size = 7 * INSTRUCTIONSIZE

    code_start = 4096;
    code_size  = max_code_size;

    max_data_size = WORDSIZE

    data_start = 8192;
    data_size  = max_data_size;

    heap_initial_size = 0;

    heap_start = 12288;
    heap_size  = heap_allowance;

    stack_initial_size = 0;

    stack_start = VIRTUALMEMORYSIZE * GIGABYTE - stack_allowance;
    stack_size  = stack_allowance;

    assert stack_start >= heap_start + heap_size > 0

def rotor_model():
    try:
        load_binary()

        init_machine_interface()
        init_kernel_interface()
        init_register_file_sorts()
        init_memory_sorts()

        new_segmentation()

        init_instruction_sorts()
        init_compressed_instruction_sorts()

        print(System())
    except Exception as message:
        print(f"modeling exception: {message}")
        exit(1)

import sys

def try_rotor():
    if is_rotor_present and len(sys.argv) > 1 and sys.argv[1] == '--rotor':
        # just run rotor
        argv = [sys.argv[0]] + sys.argv[2:] # remove --rotor but keep all other arguments
        rotor.main.argtypes = ctypes.c_int, ctypes.POINTER(ctypes.c_char_p)
        rotor.main(len(argv), (ctypes.c_char_p * len(argv))(*[arg.encode('utf-8') for arg in argv]))
        exit(0)

import argparse

def main():
    try_rotor()

    parser = argparse.ArgumentParser(prog='bitme',
        description="bitme is a bounded model checker for BTOR2 models, see github.com/cksystemsteaching/selfie for more details.",
        epilog="bitme is designed to work with BTOR2 models generated by rotor for modeling RISC-V machines and RISC-V code.")

    parser.add_argument('modelfile', type=argparse.FileType('r'))
    parser.add_argument('outputfile', nargs='?', type=argparse.FileType('w', encoding='UTF-8'))

    parser.add_argument('--use-Z3', action='store_true')
    parser.add_argument('--use-bitwuzla', action='store_true')

    parser.add_argument('-propagate', nargs=1, type=int)
    parser.add_argument('--substitute', action='store_true')

    parser.add_argument('-array', nargs=1, type=int)
    parser.add_argument('--recursive-array', action='store_true')

    parser.add_argument('-kmin', nargs=1, type=int)
    parser.add_argument('-kmax', nargs=1, type=int)

    parser.add_argument('--print-pc', action='store_true') # only for rotor models
    parser.add_argument('--check-termination', action='store_true')
    parser.add_argument('--unconstraining-bad', action='store_true')
    parser.add_argument('--print-transition', action='store_true')
    parser.add_argument('--branching', action='store_true') # only for rotor models

    args = parser.parse_args()

    Instance.PROPAGATE = args.propagate[0] if args.propagate and args.propagate[0] >= 0 else None
    Instance.LAMBDAS = not args.substitute

    Array.ARRAY_SIZE_BOUND = args.array[0] if args.array else 0
    Read.READ_ARRAY_ITERATIVELY = not args.recursive_array

    are_there_state_transitions = parse_btor2(args.modelfile, args.outputfile)

    if args.kmin or args.kmax:
        kmin = args.kmin[0] if args.kmin else 0
        kmax = args.kmax[0] if args.kmax else 0

        if are_there_state_transitions:
            kmax = max(kmin, kmax)
        else:
            kmin = kmax = 0

        if is_Z3_present and args.use_Z3:
            solver = Z3_Solver()
            bmc(solver, kmin, kmax, args)

        if is_bitwuzla_present and args.use_bitwuzla:
            solver = Bitwuzla_Solver()
            bmc(solver, kmin, kmax, args)

    print_separator('#')

if __name__ == '__main__':
    main()