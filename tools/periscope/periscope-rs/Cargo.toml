[package]
name = "periscope"
description = "To be added"
version = "0.1.0"
edition = "2021"
authors = [ "<PERSON><PERSON> <<EMAIL>>" ]
license = "MIT"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
anyhow = "1.0.83"
clap = { version = "4.5.4", features = ["derive"] }
nom = "7.1.3"
serde = { version = "1.0.201", features = ["serde_derive"] }
serde_json = "1.0.117"
serde_yaml = "0.9.34"
