{"cells": [{"cell_type": "code", "execution_count": 1, "id": "certain-designation", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "2\n", "3\n", "4\n", "5\n", "6\n", "7\n", "8\n", "9\n", "10\n", "11\n", "12\n", "13\n", "14\n", "15\n", "16\n", "17\n", "18\n", "19\n", "20\n", "21\n", "22\n", "23\n", "24\n", "25\n", "26\n", "27\n", "28\n", "29\n", "30\n", "31\n", "32\n", "stack:  686939\n"]}], "source": ["import sys\n", "sys.path.append(\"../\")\n", "from utils import *\n", "from instructions import Instruction\n", "from settings import *\n", "from uncompute import *\n", "\n", "n = 32\n", "input_file = \"../32symbolic_nomem/simple-assignment-1-35.btor2\"\n", "current_settings = get_btor2_settings(input_file)\n", "Instruction.all_instructions = read_file(input_file, modify_memory_sort=True, setting=current_settings)\n", "Instruction.with_grover = 0\n", "\n", "for i in range(1, n+1):\n", "    print(i)\n", "    Instruction.current_n = i\n", "    for instruction in Instruction.all_instructions.values():\n", "        if instruction[1] == INIT and i == 1:\n", "            Instruction(instruction).execute()\n", "        elif instruction[1] == NEXT or instruction[1] == BAD:\n", "            Instruction(instruction).execute()\n", "\n", "result_bad_states = Instruction.or_bad_states()\n", "assert(len(result_bad_states) == 1)\n", "\n", "print(\"stack: \", Instruction.global_stack.size)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "surprised-reading", "metadata": {}, "outputs": [], "source": ["circuit_queue = get_circuit_queue(Instruction.global_stack)"]}, {"cell_type": "code", "execution_count": 3, "id": "stunning-spray", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["68240 686939\n"]}], "source": ["print(Instruction.circuit.width(), circuit_queue.size)"]}, {"cell_type": "code", "execution_count": 4, "id": "returning-fruit", "metadata": {}, "outputs": [], "source": ["def are_all_controls_true(values, controls):\n", "    for c in controls:\n", "        if values[c] == 0:\n", "            return False\n", "        else:\n", "            assert(values[c] == 1)\n", "    return True\n", "\n", "def check_input(value):\n", "    # we only set the value of the first input the other ones are set to |0>\n", "    qubit_values = {}\n", "    for qword in Instruction.input_nids:\n", "        for i in range(n+1):\n", "            if i in qword.states.keys():\n", "                temp_value = value\n", "                for qubit in qword.states[i][0]:\n", "                    qubit_values[qubit] = temp_value % 2\n", "                    temp_value = temp_value // 2\n", "    element: Element = circuit_queue.pop()\n", "    assert(element.element_type != CHECKPOINT_TYPE)\n", "    while element.element_type != CHECKPOINT_TYPE:\n", "        for o in element.operands:\n", "            if o not in qubit_values.keys():\n", "                qubit_values[o] = 0\n", "\n", "        assert (element.target is not None)\n", "\n", "        if element.target not in qubit_values.keys():\n", "            qubit_values[element.target] = 0\n", "\n", "        flip_target = True\n", "        if element.gate_name == X:\n", "            assert(len(element.operands) == 0)\n", "\n", "        else:\n", "            assert((element.gate_name == CX and len(element.operands) ==1) or\n", "                   (element.gate_name == CCX and len(element.operands) == 2) or\n", "                    element.gate_name == MCX)\n", "            flip_target = are_all_controls_true(qubit_values, element.operands)\n", "\n", "        if flip_target:\n", "            qubit_values[element.target] = (qubit_values[element.target] + 1) % 2\n", "        circuit_queue.push(element)\n", "        element = circuit_queue.pop()\n", "    assert element.element_type == CHECKPOINT_TYPE\n", "    circuit_queue.push(element)\n", "    lines = []\n", "    for (bad_state, line) in Instruction.bad_states_to_line_no.items():\n", "        if qubit_values[bad_state] == 1:\n", "            lines.append(line)\n", "    return qubit_values[result_bad_states[0]], lines"]}, {"cell_type": "code", "execution_count": 5, "id": "current-exclusive", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0 (1, [80000013])\n", "1 (1, [80000013])\n", "2 (1, [80000013])\n", "3 (1, [80000013])\n", "4 (1, [80000013])\n", "5 (1, [80000013])\n", "6 (0, [])\n", "7 (0, [])\n", "8 (0, [])\n", "9 (0, [])\n", "10 (0, [])\n", "11 (0, [])\n", "12 (0, [])\n", "13 (0, [])\n", "14 (0, [])\n", "15 (0, [])\n", "16 (0, [])\n", "17 (0, [])\n", "18 (0, [])\n", "19 (0, [])\n", "20 (0, [])\n", "21 (0, [])\n", "22 (0, [])\n", "23 (0, [])\n", "24 (0, [])\n", "25 (0, [])\n", "26 (0, [])\n", "27 (0, [])\n", "28 (0, [])\n", "29 (0, [])\n", "30 (0, [])\n", "31 (0, [])\n", "32 (0, [])\n", "33 (0, [])\n", "34 (0, [])\n", "35 (0, [])\n", "36 (0, [])\n", "37 (0, [])\n", "38 (0, [])\n", "39 (0, [])\n", "40 (0, [])\n", "41 (0, [])\n", "42 (0, [])\n", "43 (0, [])\n", "44 (0, [])\n", "45 (0, [])\n", "46 (0, [])\n", "47 (0, [])\n", "48 (0, [])\n", "49 (1, [80000013])\n", "50 (1, [80000013])\n", "51 (1, [80000013])\n", "52 (1, [80000013])\n", "53 (1, [80000013])\n", "54 (1, [80000013])\n", "55 (1, [80000013])\n", "56 (1, [80000013])\n", "57 (1, [80000013])\n", "58 (1, [80000013])\n", "59 (1, [80000013])\n", "60 (1, [80000013])\n", "61 (1, [80000013])\n", "62 (1, [80000013])\n", "63 (1, [80000013])\n", "64 (1, [80000013])\n", "65 (1, [80000013])\n", "66 (1, [80000013])\n", "67 (1, [80000013])\n", "68 (1, [80000013])\n", "69 (1, [80000013])\n", "70 (1, [80000013])\n", "71 (1, [80000013])\n", "72 (1, [80000013])\n", "73 (1, [80000013])\n", "74 (1, [80000013])\n", "75 (1, [80000013])\n", "76 (1, [80000013])\n", "77 (1, [80000013])\n", "78 (1, [80000013])\n", "79 (1, [80000013])\n", "80 (1, [80000013])\n", "81 (1, [80000013])\n", "82 (1, [80000013])\n", "83 (1, [80000013])\n", "84 (1, [80000013])\n", "85 (1, [80000013])\n", "86 (1, [80000013])\n", "87 (1, [80000013])\n", "88 (1, [80000013])\n", "89 (1, [80000013])\n", "90 (1, [80000013])\n", "91 (1, [80000013])\n", "92 (1, [80000013])\n", "93 (1, [80000013])\n", "94 (1, [80000013])\n", "95 (1, [80000013])\n", "96 (1, [80000013])\n", "97 (1, [80000013])\n", "98 (1, [80000013])\n", "99 (1, [80000013])\n", "100 (1, [80000013])\n", "101 (1, [80000013])\n", "102 (1, [80000013])\n", "103 (1, [80000013])\n", "104 (1, [80000013])\n", "105 (1, [80000013])\n", "106 (1, [80000013])\n", "107 (1, [80000013])\n", "108 (1, [80000013])\n", "109 (1, [80000013])\n", "110 (1, [80000013])\n", "111 (1, [80000013])\n", "112 (1, [80000013])\n", "113 (1, [80000013])\n", "114 (1, [80000013])\n", "115 (1, [80000013])\n", "116 (1, [80000013])\n", "117 (1, [80000013])\n", "118 (1, [80000013])\n", "119 (1, [80000013])\n", "120 (1, [80000013])\n", "121 (1, [80000013])\n", "122 (1, [80000013])\n", "123 (1, [80000013])\n", "124 (1, [80000013])\n", "125 (1, [80000013])\n", "126 (1, [80000013])\n", "127 (1, [80000013])\n", "128 (1, [80000013])\n", "129 (1, [80000013])\n", "130 (1, [80000013])\n", "131 (1, [80000013])\n", "132 (1, [80000013])\n", "133 (1, [80000013])\n", "134 (1, [80000013])\n", "135 (1, [80000013])\n", "136 (1, [80000013])\n", "137 (1, [80000013])\n", "138 (1, [80000013])\n", "139 (1, [80000013])\n", "140 (1, [80000013])\n", "141 (1, [80000013])\n", "142 (1, [80000013])\n", "143 (1, [80000013])\n", "144 (1, [80000013])\n", "145 (1, [80000013])\n", "146 (1, [80000013])\n", "147 (1, [80000013])\n", "148 (1, [80000013])\n", "149 (1, [80000013])\n", "150 (1, [80000013])\n", "151 (1, [80000013])\n", "152 (1, [80000013])\n", "153 (1, [80000013])\n", "154 (1, [80000013])\n", "155 (1, [80000013])\n", "156 (1, [80000013])\n", "157 (1, [80000013])\n", "158 (1, [80000013])\n", "159 (1, [80000013])\n", "160 (1, [80000013])\n", "161 (1, [80000013])\n", "162 (1, [80000013])\n", "163 (1, [80000013])\n", "164 (1, [80000013])\n", "165 (1, [80000013])\n", "166 (1, [80000013])\n", "167 (1, [80000013])\n", "168 (1, [80000013])\n", "169 (1, [80000013])\n", "170 (1, [80000013])\n", "171 (1, [80000013])\n", "172 (1, [80000013])\n", "173 (1, [80000013])\n", "174 (1, [80000013])\n", "175 (1, [80000013])\n", "176 (1, [80000013])\n", "177 (1, [80000013])\n", "178 (1, [80000013])\n", "179 (1, [80000013])\n", "180 (1, [80000013])\n", "181 (1, [80000013])\n", "182 (1, [80000013])\n", "183 (1, [80000013])\n", "184 (1, [80000013])\n", "185 (1, [80000013])\n", "186 (1, [80000013])\n", "187 (1, [80000013])\n", "188 (1, [80000013])\n", "189 (1, [80000013])\n", "190 (1, [80000013])\n", "191 (1, [80000013])\n", "192 (1, [80000013])\n", "193 (1, [80000013])\n", "194 (1, [80000013])\n", "195 (1, [80000013])\n", "196 (1, [80000013])\n", "197 (1, [80000013])\n", "198 (1, [80000013])\n", "199 (1, [80000013])\n", "200 (1, [80000013])\n", "201 (1, [80000013])\n", "202 (1, [80000013])\n", "203 (1, [80000013])\n", "204 (1, [80000013])\n", "205 (1, [80000013])\n", "206 (1, [80000013])\n", "207 (1, [80000013])\n", "208 (1, [80000013])\n", "209 (1, [80000013])\n", "210 (1, [80000013])\n", "211 (1, [80000013])\n", "212 (1, [80000013])\n", "213 (1, [80000013])\n", "214 (1, [80000013])\n", "215 (1, [80000013])\n", "216 (1, [80000013])\n", "217 (1, [80000013])\n", "218 (1, [80000013])\n", "219 (1, [80000013])\n", "220 (1, [80000013])\n", "221 (1, [80000013])\n", "222 (1, [80000013])\n", "223 (1, [80000013])\n", "224 (1, [80000013])\n", "225 (1, [80000013])\n", "226 (1, [80000013])\n", "227 (1, [80000013])\n", "228 (1, [80000013])\n", "229 (1, [80000013])\n", "230 (1, [80000013])\n", "231 (1, [80000013])\n", "232 (1, [80000013])\n", "233 (1, [80000013])\n", "234 (1, [80000013])\n", "235 (1, [80000013])\n", "236 (1, [80000013])\n", "237 (1, [80000013])\n", "238 (1, [80000013])\n", "239 (1, [80000013])\n", "240 (1, [80000013])\n", "241 (1, [80000013])\n", "242 (1, [80000013])\n", "243 (1, [80000013])\n", "244 (1, [80000013])\n", "245 (1, [80000013])\n", "246 (1, [80000013])\n", "247 (1, [80000013])\n", "248 (1, [80000013])\n", "249 (1, [80000013])\n", "250 (1, [80000013])\n", "251 (1, [80000013])\n", "252 (1, [80000013])\n", "253 (1, [80000013])\n", "254 (1, [80000013])\n", "255 (1, [80000013])\n"]}], "source": ["for i in range(256):\n", "    print(i, check_input(i))"]}, {"cell_type": "code", "execution_count": null, "id": "broken-electric", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}