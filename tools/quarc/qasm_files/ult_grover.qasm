OPENQASM 2.0;
include "qelib1.inc";
qreg in2[1];
qreg in3[1];
qreg q2[1];
qreg an[3];
qreg q4[1];
qreg gout[1];
creg c0[2];
h in2[0];
h in3[0];
x in3[0];
x an[1];
cx in3[0],an[1];
x in3[0];
ccx an[2],in2[0],q2[0];
cx in2[0],an[2];
cx an[0],q2[0];
ccx an[2],in3[0],q2[0];
cx in3[0],an[2];
cx an[1],q2[0];
x in3[0];
cx in3[0],an[1];
x an[1];
x in3[0];
cx q2[0],q4[0];
x gout[0];
barrier in2[0],in3[0],q2[0],an[0],an[1],an[2],q4[0],gout[0];
cz q4[0],gout[0];
barrier in2[0],in3[0],q2[0],an[0],an[1],an[2],q4[0],gout[0];
cx q2[0],q4[0];
x in3[0];
x an[1];
cx in3[0],an[1];
x in3[0];
cx an[1],q2[0];
cx in3[0],an[2];
ccx an[2],in3[0],q2[0];
cx an[0],q2[0];
cx in2[0],an[2];
ccx an[2],in2[0],q2[0];
x in3[0];
cx in3[0],an[1];
x an[1];
x in3[0];
barrier in2[0],in3[0],q2[0],an[0],an[1],an[2],q4[0],gout[0];
h in2[0];
h in3[0];
x in2[0];
x in3[0];
h in2[0];
cx in3[0],in2[0];
h in2[0];
x in2[0];
x in3[0];
h in2[0];
h in3[0];
barrier in2[0],in3[0],q2[0],an[0],an[1],an[2],q4[0],gout[0];
measure in2[0] -> c0[0];
measure in3[0] -> c0[1];
