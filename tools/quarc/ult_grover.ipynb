{"cells": [{"cell_type": "code", "execution_count": 104, "id": "insured-sister", "metadata": {}, "outputs": [], "source": ["from qiskit import *"]}, {"cell_type": "code", "execution_count": 105, "id": "controlling-perry", "metadata": {}, "outputs": [], "source": ["circuit = QuantumCircuit.from_qasm_file(\"qasm_files/ult_grover.qasm\")"]}, {"cell_type": "code", "execution_count": 106, "id": "valued-terrorist", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABM8AAAOACAYAAAAtvlT5AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjMuNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8QVMy6AAAACXBIWXMAAAsTAAALEwEAmpwYAADELklEQVR4nOzdeXxU9b3/8feZLTtL2LKw72FLlEUtolZAWysFrdZel2pvbRVLrbbWe73SX3/10qu99SeF3taqt9jaFqqgVlyqYBdxQxEJi0SQNexbQvZMZjm/P4YEQphkAjNzzsy8no8HD5LJmZlPPvnOOd/v53zP9xgVFRWmAAAAAAAAALThsDoAAAAAAAAAwK4ongEAAAAAAABhUDwDAAAAAAAAwqB4BgAAAAAAAIRB8QwAAAAAAAAIg+IZAAAAAAAAEAbFMwAAAAAAACAMimcAAAAAAABAGBTPAAAAAAAAgDAongEAAAAAAABhUDwDAAAAAAAAwqB4BgAAAAAAAIRB8QwAAAAAAAAIg+IZAAAAAAAAEAbFMwAAAAAAACAMimcAAAAAAABAGBTPAAAAAAAAgDAongEAAAAAAABhUDwDAAAAAAAAwqB4BgAAAAAAAIThsjoAAAAAxFd5eXmH2zz++OOaPXt2u9v0798/WiElLXINAEDiY+YZAAAA2njiiSesDiFlkGsAAOyN4hkAAAAAAAAQBsUzAAAAAAAAIAyKZwAAAGhj8eLFVoeQMsg1AAD2RvEMAAAAAAAACIPiGQAAANq48cYbrQ4hZZBrAADszWV1AMnu9c0ZOljtjPv75nUJ6AujGuL+vgAAAAAAAMmE4lmMHax2aneF2+owAAAAAAAAcBa4bBMAAABt3HHHHVaHkDLINQAA9kbxDAAAAG3Mnj3b6hBSBrkGAMDeKJ4BAACgjenTp1sdQsog1wAA2BvFMwAAALRx5MgRq0NIGeQaAAB7o3gGAAAAAAAAhEHxDAAAAG0UFRVZHULKINcAANgbxTMAAAC0sWTJEqtDSBnkGgAAe0vo4tnmzZuVm5urlStXRvyct956S3fddZcmTJigwsJCFRcXa/bs2dq9e3cMIwUAAEgsDz30kNUhpAxyDQCAvRkVFRWm1UGcrYqKCm3fvl3FxcXyeDwRPee2225TVVWVZs2apaFDh2rfvn169NFHdfToUf3zn/9U//79oxrj71Zna3eFO+Ltl827TP3HTNOkWXMjejycAbk+3XZhbadiTTVmUKra49axbR75Gwy5Mkz1GNKkrv19MhK6rGw//oBUdsitT/Z71OAz1CXdVElfrwb39MswrI4OAFJPeXl5h9uUlJSotLS03W2i3W9KRuQaAEJqvYbWlqdpd4VLQVMq6BrQhP5e5WYFrQ4NNhAMSMd3u1Wx3aOA15A701SP4V51KbTHmNFldQDnIjc3V7m5uZ16zqOPPqqePXu2euzCCy/Ueeedp0WLFun//t//G8UIYVe+BkPb3shW/TGXZJiSaUiGqeO7PMro4dewK2vlzkjYurKtHG9w6A8fZKui3ilDpkwZMmRq0wGPBvbw6Wvja5WW0HsiAAAAAO355IBbL5ZmKWAakkLjrN0VLr2/M13TRtZr8mCvtQHCUk21hra+niNvlbPV+Lxyp0fZfXwaekWtnJHNl4qZhJ5fM23aNN12220t319++eW6/fbbtXz5ck2bNk2FhYUqKSnRCy+80LLN6YUzKXQmr0ePHtq/f388wobFTFPavjJb9cecJx4wWv3fcMypbSuzZVI7O2f+gPSHD7JVWR/a1ZgyWv2/65hLL5ZmWRYfAAAAgNjaU+nU86VZCrSMr4xT/pl689NMbdwX+dVaSC5mUCcKZyfKU6eNz2sPubTjH9aPGRO2eBYIBFRWVqbi4mJJkt/vV1lZmdauXaunnnpKc+bM0TPPPKOePXtqzpw5qq6uDvtamzdv1tGjRzVy5Mh4hQ8L1R50qe6IS1K4uZ+G6o+4VHOA6VDnquyQWxX1zpZiWVuGthz26HBNwu6KACBprVixwuoQUga5BpDM3tuRfmKy2ZnGBKEC2qptGUxeSFHHd7tDM87aGTNW7/WcnPxikYStDmzdulUNDQ0aO3asJGnLli3yer0aPHiwli5dKocjNBhvamrSTTfdpL1792rUqFFtXsfn8+n73/++evTo0WoWW0civVz0Kw/+Q32LLov4dSXpw5d+qrWvPdrqMV9jrfqPmRbxa7z77rv6/lWf79T7porvXfu4rrrgW3I4wn/4AsGAFv7fZ/XYsm/FMbLkc/W9f9Gg865uN9emGdRtP1io1cv+TxwjA4DUdu+993a4zfbt2zVkyJB2t5k/f360Qkpa5BpAKnN5MnTX/9bIcLS3aJWho3VODS++RMf2bopbbLCHH92yVJPHzJLTEb48FTSD+vHdv9Zv//pA1N+/oqIiou0SdrrHhg0bJEnjxo1r9f0DDzzQUjiTpMrKSklSXl5em9cwTVPf+973tG7dOj3xxBOdXj8tVibNfFCznzze6l/B8IutDitpZGd0l6kOTmuYprIzu8cnoCSWntW93cKZJJnBoNLJNQDYzvLly60OIWWQawDJypOeI6OD8UCztCzGBKkoJ6O7HEbHY0arx+cJO/Nsw4YNys/PV+/evVu+79atmyZMmNBqu40bN6qgoOCMhbH7779fzz33nJ588kldfvnlnXr/SKuTobttduqlo2Ly5Ml6KsIYU82e1Rk6/En7H06n06mv3PAl3fMLcngunl+XpU8OmO1ctik5nC59/7v/qhd+cVMcIwOA1BbJHSDnz5+vW2+9td1tfvSjH0UrpKRFrgGkMn9AemSlqUCw49slrnrzZe68mYJ2vpWpiu1Se/NbnE6nvn3X1/Xjp74atfedMWNGp7ZP2JlnGzdubLlks/n75vXPTrV+/fozPj537lwtWrRIv/jFL3TttdfGNFbYS4/hTQp/PXUz48R2OBfFfb3tFs4kyZCpcQXkGgAAAEg2Lqc0rqBJRjuVEUOm+nX3UThLUT2GNZ28SUA7codaO2ZMiuKZaZrauHGjSkpKWm0TDAa1adOmVkU2SZo3b55+/etf65FHHtHNN98cr5BhE5m5AXUf3P4Hr9ugJmX2CMQpouQ1pKdfA3N9au80wqSBXnXJYHVQALCbuXPnWh1CyiDXAJLZ5CGN8rh0xgKaIVMypMuHN1oQGewgJ9+vnIL2x4y9irxKy7G2uJqQl23u2rVLVVVVLTPKdu3apZqamjbFs23btqm2trbVzLOFCxfqscce0zXXXKOSkhKtWbOm5Wc5OTmW33Hzurn/7NTjODsDL6mT4TBVsS1NoQ/pyUp37hCvBlxcb1lsycQwpK9NqNULpVnaetjT+mcyNWmgV1cUNVgUHQCgPdddd53VIaQMcg0gmfXICurrk2r07MdZqm5svXxOmsvUrOJ6Dezhtyg6WM0wpCHTarXrrSwd333qmDFUTOtV5FW/C60fMyZk8SzczQJOL56tX7++1XaStHLlSknSiy++qBdffLHV9pMnT9bLL78ck5hhLw6nNOjSehWc16hj2z068HGGJGn09VVK78J04WhKc0n/MqFOh2sa9PjbXSVJU0fUa1xBEzPOAMDGSkpKVFpaanUYKYFcA0h2Bd0C+t5l1frsiFt/XpstSZo1rk6j8pvkjux+AkhiTrc0ZFqdGiobtPmF0JixYHyjegz1ypNtjzFjQhbPvvzlL7dasH/mzJlnXMD/+uuv1/XXX9/qMYpjOFVal6AKzmtsKZ5ROIud3qdMs714iNfCSAAAAADEm8Mhjejja/m+uC/rHqO1jO4nx4z5Jfa6lDdh1zwDAAAAAAAAYo3iGQAAANq45JJLrA4hZZBrAADsjeIZAAAA2li4cKHVIaQMcg0AgL1RPAMAAEAbd999t9UhpAxyDQCAvVE8AwAAQBurVq2yOoSUQa4BALA3imcAAAAAAABAGBTPAAAAAAAAgDAongEAAKCN0tJSq0NIGeQaAAB7c1kdQLLL6xJIqfcFAADJYdmyZbruuuusDiMlkGsAAOyN4lmMfWFUg9UhAAAAdNq8efMo6MQJuQYAwN64bBMAAAAAAAAIg+IZAAAAAAAAEAbFMwAAALSxYMECq0NIGeQaAAB7o3gGAACANoqKiqwOIWWQawAA7I3iGQAAANq44oorrA4hZZBrAADsjeIZAAAAAAAAEAbFMwAAALQxfvx4q0NIGeQaAAB7o3gGAACANtauXWt1CCmDXAMAYG8UzwAAAAAAAIAwKJ4BAAAAAAAAYVA8AwAAQBuLFy+2OoSUQa4BALA3imcAAAAAAABAGBTPAAAA0MaNN95odQgpg1wDAGBvLqsDSHavb87QwWpn3N83r0tAXxjVcFbPnVu+S5/U10U5osiMzszSvP4DLXlvJDfaNdpD+0BHrGojtA/EEu0a7aF9IBnRrnG2KJ7F2MFqp3ZXuK0Oo1M+qa/Te7U1VocBRBXtGu2hfaAjtBEkI9o12kP7QDKiXeNscdkmAAAA2rjjjjusDiFlkGsAAOyN4hkAAADamD17ttUhpAxyDQCAvVE8AwAAQBvTp0+3OoSUQa4BALA3imcAAABo48iRI1aHkDLINQAA9kbxDAAAAAAAAAiD4hkAAADaKCoqsjqElEGuAQCwN4pnAAAAaGPJkiVWh5AyyDUAAPaW0MWzzZs3Kzc3VytXroz4Of/85z81a9YsFRUVKS8vT0VFRfra176mDz/8MIaRAgAAJJaHHnrI6hBSBrkGAMDeErp4lpeXpzfeeEOXXnppxM+pqKjQ6NGj9fDDD+v555/XT3/6Ux07dkxXX321PvjggxhGC0RPTaOhrYdd+uywS/VNhtXhwGaCAal6v0vHd7vVUJE4u/mjtQ5tOeTWjqMu+QJWRwO7afQZ2nbEpa2H3KpqYL8XDy+88ILVIaQMco0z8QekHUdd2nLIrSO1iXM8T0Rev7T9xDGmsj4xcm2aUv1Rp47vdqvmgEtm0OqIYDe13pNjxjovfadz5bI6gHORm5ur3NzcTj3n2muv1bXXXtvqsWnTpmn48OFavHixLrjggmiG2GnL5l2m/mOmadKsuRE9bgemzyf/d++V47xiOe/4VsvjgRf/ouDzL8r1m1/JyM62MMLkUd1o6PVPMvXpIbdMhXaATsPUmIImXVnUoAyPaXGEySMR27VpSgfXp+vQpjQFvCc7fpk9/ep7QYNy8vwWRhfe/uNOvVGWofJKd8tjaS5TEwc06rJhjXLasA+biO0jUTX5pRVlmVq/zyN/sLnjZ2pYL5++MKpBuVn2HC3QRpBsaNPxEwxKb21L14e70tToP3kQ7NvNryuL6tW3uz3PMCViG/EHpDe3ZOjjPWnyBU4WFwb38OkLo+rVK8eex5iqvS7t+zBDDZUnh/OujKDyxjWq92ivDOokUZGIbVoKFc1e35ypzQfdMs1QY3AYpsbkN+nKUQ3KZMx4Vmw4JInctGnTdNttt7V8f/nll+v222/X8uXLNW3aNBUWFqqkpKTDs3nZ2dnyeDxyu93tboczM9xuuf79hwq+8pqC60olSebOnQou+r2c999nyx1KIqppNPTb93JaFc4kKWAaWr/Po0Wrc9To40gZLYnWrk1T2v1OpvavzVDgtDNL9cec+uyv2areZ7/zJfuOO/X06hztqWwdm9dv6J3tGVq2LktBGx7fE619JCpfQHrmwxyt3XNq4UySDG074tb/vpejijp7dmVoI0g2tOn4ME3phfVZWrUtQ43+1sfzfced+t0HOSqvcFoUXfsSrY0EgtLij7L1wa70VoUzSdp5zKXfvt9Fh2vsd4yp3OXWtjey1VDZuh34Gwzt/SBTez/MsCiy5JNobVoKFc7+970cbT5wsnAmSUHT0Ib9Hi16P0cNXLl0Vuy3N4hQIBBQWVmZiouLJUl+v19lZWVau3atnnrqKc2ZM0fPPPOMevbsqTlz5qi6urrN830+n/bs2aMf/vCHkqRvfOMbcf89koUxcIAc/3qrAo/Ol1lRIf8jP5dj5gw5xo21OrSk8fetGappdLQqnJ1k6GitQ+9sT4t7XMkskdp1zQGXjm1t/vuf1kZMQ2ZQ2vV2pq2m9Jum9PLGTAWCCtOupU8PeVR20J4nNhKpfSSqD3enad9xl9q0aYXaTKPP0IpP7TtISPQ2smLFCqtDSBmJkutEb9OJYOthtz454DnxXet9nylDwaD00sYsmTY8sSQlVhsp3evRzmNn7mOYMtTkl177JDPOUbUv6Jd2v90c0+nHxtD3hzelq/6oPQusiSiR2rQk/XNrhqoawo8Zj9U59Pb29LjHlQwStni2detWNTQ0aOzYUKPdsmWLvF6vBg8erJdeekmzZs3S1KlT9YMf/ECNjY3au3dvq+dfffXV6tOnj4qLi/Xqq6/q2Wef1ejRo634VZKGY9ZMGf37yX/HdySnU45bb7E6pKTR6DO0cb8nbIGh2do9aQrYqDiSDBKlXR8pS5OM9nrShnx1TlvNPtt33KlDNa5227UhU2t227conCjtIxGZpk787cO3a1OGth5yq9rGa6AlchspKyuzOoSUkUi5TuQ2nQg+Kk+T0cF+r6LOqd0V9jmeny5R2sia3R3neneFW0dttN5c5S6PAk0Onemk0kmmjnxq375TIkqUNu31S+v3edR++5A+3uOR355Xf9uaffe6HdiwYYMkady4ca2+f+CBB+RwnNzBVVZWSgrdXOBUCxcuVHV1tQ4cOKA//OEPuuGGG7R48WJdfPHFEb1/pGutfeXBf6hv0WURbdvsw5d+qrWvPdrqMV9jrfqPmRbxa7z77rv6/lWf79T7NnP+/BE5isd1+nmGYcgYN1bm2o/l+NpXZZzFZbDvvPuucj83pdPPO1crfx46cHZ2Db146T1ovP7lPz/qYKvQLIzBI4pVc2xPXOLqrO/90bo8J3u7/v2/faaCnkM73O6Be/5Lf/7HIzGNJVJjPv8tTf3mk+1uY8rQlj21yr0qtm0m2dtHInKlZeo7v63rcDtThi658qsq3xT5nbfPhlVtJFbt49577+1wm/nz53e43fz586MVUtKyc67Ppl2z34ut23+5T1ndCzrc7l/nzFXpGwtjGkuy7fdO991nfHI4Oj75ctV1t2vbh8tiHk8kbr/qEV1/2Q/lMNor6Bl69831Ou/L1q7l3Z5EGxMkyn6vZ/9xuum/1newlSGv39Cw0RNUdXhHTOM5W3Ydm9unjN5JGzZsUH5+vnr37t3yfbdu3TRhwoRW223cuFEFBQVtEj9s2DCNHz9eV199tZYsWaLhw4frgQceiFv87Zk080HNfvJ4q38FwyMr6lnJ3LlTwcV/luOG6xX842KZhw9bHVLSCAZ8EW8b6MS26FiitGt/hH93f9A+7SPSdt2Z9h9vidI+EpEZiPwGF7QRIH5o07EVaT+O/d65C0Z4nLFTrv0Bn4wOZhWZpimf3z4xJ4OEadOd+LvbqV0nioSdebZx48aWSzabv29e/+xU69evP+Pjp3I4HDrvvPP0pz/9KeL3r6ioiGi7363O1u7INo2qyZMn66kIYzzdNZ9+ovdqazr1HLPJF7r++9pZcn7jVpmVlQr8/DE5f/ZfMhyR12gvnjxZL55l3Odi7W9D/0f6d423QFCa//eg6poMhZ+Ga6pndlB7tm+27R12fvJa6H8r8pzs7XrP6gwd/sRUR9O0f/G//0dP9ngwprFE6niDQwv+0X7MhkxdVNRVP4tx/pK9fSSqp9/3aU9l+5f2epymPnrrBXli3KOxqo3Eqn2Ul5d3uM38+fN16623trvNj370o2iFlLTsnOvOtmv2e7H38sZMrdtjdrhUx/OLHlaP7J/GNJZk2++dbslHDn12uP1cOw1Tq179nW3uTlhzwKWtr7XfNgzD0PSvnK+Kefb9jCXSmCCR9nvBoPSLfwRV421/zJibGdSurettO2aM19h8xowZndo+YWeenVo8M01TGzduVElJSattgsGgNm3a1KrIdiY+n0+rV6/WoEGDYhVu0gsuelqGyyXHLTdJkpx33Snz4CEFn3/R4siSg9MhTRroVfuFEUMXDmy07U4wESVSu+5V5D3RPMJ17kxl9/Eps4d9FjjolhHUyD6+dtYbCXVoJw7wxjWuSCVS+0hUFwz0djCANHV+P2/MC2dnK9HbyNy5c60OIWUkSq4TvU0ngokDvCeOimc+NhoyNaSnTz2y7bnIbSK1kUkDOj7GjC1ssk3hTJKy8/xK7+5vZ51bU4bDVM/h9uw7JaJEatMOhzRpYKM6GjNeMNDLmPEsJGTxbNeuXaqqqmqZUbZr1y7V1NS0KZ5t27ZNtbW1rWae3XzzzXrkkUf0yiuv6N1339Wzzz6rmTNnqqysTA8+aI/ZGIkmuK5Uwddel/Pf75fhCo1gjMxMOf/tPgWf+aPMnTstjjA5TB7cqOG9mySpVbGh+euSvl6d36/JktiSUaK16/SuQQ2cUh/6plWHypRkypMd1KDPd7x+VLzNGFt/ygDg9HZt6Auj6lXYzT4Fv2aJ1j4SVVGeTxcNapR05v3egFy/Lh/RYElsHUmGNnLddddZHULKSIRcJ0ObTgR5XQK6ekzoeN765FLo6+5ZQc0qtt/xXEq8NjKkl1+XDQsdQ86U64KuAX2hqN6CyMIzDGnI1Dq50kP9O50Wt+GQBn++Tp4s+xT8ElmitWlJ+twgr4r6hB8zjivw2vbEtN3Z9Fxt+8LdLOD04tn69etbbSdJkyZN0ksvvaQnn3xStbW16t69uyZOnKhXXnlFF154YRyib991c//ZqcftwHFeiRzLX2j7+JjRcrxsv4p8onI6pBvOr1PpPp8+3JWmQzWhj29Bt4AuGNCoMQU+ziBEUSK26x7DmpTWNaDDm9JVuTN0m3tXhqleI73qPcp7oqNlL5keU9+8qFprdqfro/I0VTeGGvGw3j5dNMirgT0iX/cqnhKxfSQiw5Cmj2xQ/+5+fbArTbsqQgv05mYFNWmAV+f388rltDjIMJKhjZSUlKi0tNTqMFJCIuQ6Gdp0ohjfv0m9sgN6f2e6Pj0UOp7npJmaMKBREwd4leG23/FcSsw2cumwRhV09euDXenafjR0jOmWETrGTBjglduGx5j0rkGNmlWtw5+k6ciWNAW8ob5T7tAm9RntVWZP+510TFSJ2KYdDum68+u0fm9ozHjwxJgxv2tAFwz0amxBE2PGs5SQxbMvf/nLra5/nTlz5hmvh73++ut1/fXXt3rs7rvv1t133x3zGIFYcDik8/s16fx+TfrJa90lSbd/rnNrUSC5ZfcOKPvyOq39baizXXxjlcURdSzdLU0Z2qgpQxtb2vW/TLDnWXXEn2FII/N8Gpnna2kfcy6ttjgqAIit/rkB9c+t009eCx3Pvz/V/sfzRDWst1/Dete2HGO+93n7H2PcmaYKJzaqYEKjPl4UinvQpfaaJQfrOAzpvH5NOu+UMeO3JjNmPFcJedkmAAAAAACpjBlEQPxQPAMAAEAbl1xyidUhpAxyDQCAvVE8AwAAQBsLFy60OoSUQa4BALA3imcAAABogzVi44dcAwBgbxTPAAAA0MaqVausDiFlkGsAAOyN4hkAAAAAAAAQBsUzAAAAAAAAIAyKZwAAAGijtLTU6hBSBrkGAMDeKJ4BAACgjWXLllkdQsog1wAA2JvL6gCSXV6XQMK97+jMrChGkjjvjeRGu0Z7aB/oiFV/Jyvbx7x583TddddZ9v6pxKpcp2K7RuRoH0hGtGucLYpnMfaFUQ1Wh9Bp8/oPtDoEIOpo12gP7QMdoY0gGdGu0R7aB5IR7Rpni8s2AQAAAAAAgDAongEAAKCNBQsWWB1CyiDXAADYG8UzAAAAtFFUVGR1CCmDXAMAYG8UzwAAANDGFVdcYXUIKYNcAwBgbxTPAAAAAAAAgDAongEAAAAAAABhUDwDAABAG+PHj7c6hJRBrgEAsDeKZwAAAGhj7dq1VoeQMsg1AAD2RvEMAAAAAAAACIPiGQAAAAAAABAGxTMAAAC0sXjxYqtDSBnkGgAAe6N4BgAAAAAAAIRB8QwAAABt3HjjjVaHkDLINQAA9kbxDAAAAAAAAAiD4hkAAAAAAAAQBsUzAAAAtHHHHXdYHULKINcAANgbxTOkPNO0OgLYXTCYuO0kUeNOJKYpBRK4jQDhzJ492+oQUga5RjLhuBg/5Dh+aNfxZ7dcu6wOAIg305Rq9rtUsd2j+qNONRx3tvzskxe6KKunX90HN6lLoV+GYWGgsExlvUMf7/FoT6VLB6td8vpDDSEnPaj8Ln4N6enXuEKv0t0WB3qKYEA6vsut47tD7brZut93U0ZuQFm9/Oo5rEmZPQMWRpkcmvzSpgMefXbYrQNVTlU1hvLtdprK6xJQv+5+ndfXq57ZQYsjBc7N9OnTtXLlSqvDSAnkGonuYLVTpXs92nfcpYPVTvmDob5Tt4yACroGNLyPT6PzmuRydvBCaJdpSnWHnarY5lHdUZcaKk4mdNOyLsrsEVD3gU3qNsAng2ky58Q0pZ3HXNq036P9VU4dqXUqaBoyDFM9s4Iq6OrX6PwmDe3FmPFcmUGpaq9blTvdqj96skS17nfdlN4toKxeAeUOaVJ2nrW5pniGlFK116U9qzPlrTrzkbux0qnGSqeOfZamtC4B9buwXl37+eMcJaxS3Wjo9U8yVXbILantnrmm0aGaRo+2HvbozS0ZunBgoy4Z2mhpR9A0paOferT/4wz5G9v2ksyAofojLtUfcenI5nRl9fFrwOfqlJFLYaezgkHpvZ1pemd7urz+trn2BQztqXRpT6VL7+1I19BePl01ul7dM8k1EtORI0esDiFlkGskqiO1Dr22KVO7Ks58RvF4g1PHG5zafNCjNzZn6NJhjZo0wEux4SzUHXGq/L3MVsWFU3mrnPJWOVW5wyN3ZlCFExqUO7SJXJ+FHUdd+usnmTpa17aTb5qGjtSGimnr96Wpe2ZAXyhq0PA+PgsiTXyVO93a+2GGmmrPkOugoYYKlxoqXDq6JU0Z3f3q97kG5eRZMz6neIaUYAal8vczdfTTtIif4612atuKHPUc4VW/i+rl4ExZUis76NZLGzJPFEU6niPsCxh6e3uGthzy6Kvn16qHBbOM/I2GdvwjSzX7I50CZ6rukEtlL3VR4YQG9R5D5zVSVQ2Gnvs4W/urIj1smtp2xK3H3+6iq0bXq6RvU0zjAwAg3j7YlaaVn2YoEDQU6ju136lo8Dn0+uZMlR106/rz6pSVZrNrsmzKNKUD69J1oDRdMiPLta/eoV2rslS5y61Bl9bJ6YlLqAkvGJReL8vQmt3pET+nst6pJWuzVdLXqy+Nrmd2ZYSCfmnX21mq3BF542yodGnrq9nqM9arwgkNcZ9dyWROJD0zKG3/W9aJwllnD9Kmjm5J046/Z8lk8kjS2rDPo+c+zjplNlHkFaXDtU4tWp2jo7Xx3Z36Gw1teS3nROEs0nYd+r3MoKG9H2Zq/9rIOwaprKrB0NPv53SicCY159oXMPTShix9uCvywj1gF0VFRVaHkDLINRLNW5+l6/XNmScKZ1LkfSdTuyvcenp1jmq9nMHriGlKe97P0IF1Gad09yLPdVW5R1tfz1GAc3gdCgal50uzThTOOj9mLN2bpmc/zlaAMWOHgn7psxXZJwpnnS+iH9qYrl1vZ8Z9TbSULJ699dZbuuuuuzRhwgQVFhaquLhYs2fP1u7du60ODTGw76MMVZU3fzDPfLCZdl/oX1uhsztV5R7tXZMRuyBhmf3HnfrLhky11xH5xU2hf+HUNzm0+KNsNcVpBrFpSjv+kaXGyuZTW21jD9+mW15FB9dn6Ng2TkW2JxCUlnyU3bKu2Zl01D4k6a+bM7UjzGUWgF0tWbLE6hBSBrlGItl8wK1/fpah9ga94Y+NoT7LsTqnln6cpSCTz9p1pCxNR8qaizlnM46R6o+4tOvtrFiFmDT++Vm6Nh9sf8zYfrsOXXWwoowxY0fK38tU7YHmCQCdHceEcl2xLU0HN8R3IkBKFs+efvppHThwQN/97nf13HPP6cEHH9TatWv1+c9/XuXl5VaHhyiqPeTUoU3NM87O9uxW6AN6eFOaag8xDzeZ+APSXzZkyTTP/cxnZb1Tb26Jz8Hy6BZPJ2ecnUnod97zfoZ89Zz5Deftbek6VBOdotfyjZnysoQiEshDDz1kdQgpg1wjUdR5Db36SeaJ786l/2CqvNLNzOx2eKsd2tdy8v7ccn18l0cVO2x0pyub2X/cqXd2tF+k7FhozPjh7nTtOsYJ03Cq9rh07LNojM+lAx+nq6EifiWtlCyePfroo3rxxRd16623avLkyfrqV7+qZcuWqaqqSosWLbI6PETR/rUZJ9YGONfiQOg19n3EmYRksmG/R0fOsDjl2TH10e40VTXEthAVDJxo15LOvV1LgSZH3M/aJIqGJkPv7ohebqoanFpbbu0gIRCUNu1365kPsrXgH130m7dz9Pa2dNVx6QzO4IUXXrA6hJSRKLmubzL07vY0PfFOjhb8o4t+vzpbG/Z55OdGzilj9a401TdFYwgZOu68tS1dPtrPGR0oTVfQH43jc+g19n2UEffL3BLF37dmnDiZHo0xY+j10JZp6pTx9Lm3bTNoaP+6+OU66YpnwWBQv/zlLzVhwgTl5+drypQpevfddzVp0iTdc889kqSePXu2eV7//v3Vo0cP7d+/P84RI1YajjtUc+BcZ+ecylTtQXdcq9uIHdOU1uyOZiHDkClDH8W4OHJ8l/uMd9U8F8c+8yjIjKg21u31yB+MblHpo/I0yzquXp/0+9XZer40W7uOuXS8walDNU79fWuGfvlWF+2tZGYtgPD2Vzn1P2910ZtbMnWwOnQHxd0VLr24PktPr85RQxNF+GTnD0gf74luP6fR59Cm/SwhcTp/o6GKTiykHommGqeq9zIj6nTH6hzafjS6Y8Y9lS4drKZfdbq6Q041VLgUzVwf3+1WU118jj9JVwX47ne/q0cffVS33nqrnnvuOc2aNUvf+ta3tGvXLpWUlIR93ubNm3X06FGNHDkyfsEipqp2N09NjtaHKfQ6x8s5wCeD6kZDB6ujufMO2XIotu3j+O7ov36gyaGag3SmTrflUPQvb6isd+pwjTWdqb9syNKe46G/s9myXwz93+Q39Mc12apn8AvgDBp9hv74YbYafa33Hc37kv1VTr2wnjWVkt3e464ozTprLRbH20RXvc8lMxD9YzLjmLa2HorNmPFT2nUbJ9tfFHNtGqraE59cJ9Vo6fnnn9eSJUv08ssva/LkyZKkKVOmaP369XrllVc0bty4Mz7P5/Pp+9//vnr06KHbbrstjhEjlupjdK15/THOIiSDAy13Toxmx8TU0VqHmvySJ0Z71/qjsWl/9Udd6tqX6WfNgqZOFFejb3+VU326xPcalYo6hz5tp7BrypDXb2jdXo8mD/bGMTLY2YoVK6wOIWXYPdfr93nU4GuvaGJo2xG3Dtc41DuHW80lq/1VsemD7I/R8TaR1cfoJkOx6kcmsgMxmiF2IEafl0QWy3GMFPtbyibVnuqxxx7T1KlTWwpnzQYPHiy3263Ro0e3eY5pmvre976ndevWacmSJcrNzY3ovSLdDtZ5/J6PNbTwvFaPtX/3wTP//M1HW3//8fufauL1xecY3bn73h9DM6YSqS3aKebzr/qBptzY+o/b0R0Tw/38nj81f2XIlDT6/Ck6tveTcw2xDcMwtOK/Ww9KzqZNS23b9e+eeFY/f/Yb5xBddNiljWR1y9ft/9P6Mv5zbx8hP/rpQr333H+cQ3Sdd/6X7tOUf/l5u9uYwaD++OpGzfjxhXGKqvPs0j6Swb333tvhNtu3b9eQIUPa3Wb+/PnRCilpJUOuv/LgP1Q44hIZjvZnHX39e4/qw7/Mi1NUqcFO+73P3/ZrjZs2u9Vj0Tg21jQ61KtPgQK+xnOM8NzYKdf/99YXNXnMrFaPRaPPd3hPrSbb4PezU65v+MkHyhsyqdVjZ9OuT+/vrV63Xd+5YtQ5Rnfu7JTrPz1Yrt7d+rV6rL12Hek4ZuWr7+r+L089x+g6ljSXbe7bt09lZWWaOXNmm5/t3btXI0eOVFpa22v077//fj333HN6/PHHdfnll8cjVMSJ0xGb2rDLwRTcZOBwxu7vGKvXjlWbDr027fpUidg+2uNJy+5wG8PhkCe9SxyiQaJYvny51SGkDLvn2pPRpcPCmRkMyh3BvgaJy+GK3fHLGcPXTkSuWPUlLeiD2J3DGZv+dSw/L4kq0cfnRkVFRVLcc2PNmjW68sor9eyzz2r69Oktjzc0NGj8+PGaOnWqfvnLX7Z6zty5c/X4449rwYIFuvnmm+MdMmLs05dzVHc4sg9oc1X79Cr2mWT28qvoyzXnEFl0/OS17pKkH19VaXEkkbNTzB/uStNfN2d2vKFOnl06/YxSON+9tEq5WdG/bMU0pXW/7xbRGhidadOS1Gtko/pPbjiH6KLDLm2kwWfov1d2i2jbzraPy4Y16NJh8T27XrrXo5c2tL8ekSFTQ3v5dOPEujhF1Xl2aR/JoLy8vMNtSkpKVFpa2u42/fv3j1JEySsZcr304yyVHXSfsl7imX1pdJ0mDIj9pTOpxE77vTc2Z2j1rsjuQh35sdGUYUgPXnlcTounddgp1zv+kaXKCG8Y0Jk+nysjqOIbq84hsuiwU65/tzpbuysiK750ps+X18WvOy5mzHiqTUu7yBvBZbKdHcd06evTsCtrOx3PjBkzJEkvv/xyRNsnzcyzHj16SJK2bdvW6vGFCxfq4MGDbW4WMG/ePP3617/WI488QuEsSWXkxmZNocwYvS7iK1ZrTnlcprpnxma9F8OIXbvO6EG7PlWG21SX9Nj8HfPivN6ZJI3Ka5LHaaq9G2SYMjS+P4NeAG2d38/bQeHMlMthakwB+5BkFpu+k6He2QHLC2d2wzgmfvrkxCYneTF63UQWs3bdIz7rNifNmmcDBw7U6NGjNX/+fHXv3l35+flavny5Vq5cKUkqLj65RtXChQv12GOP6ZprrlFJSYnWrFnT8rOcnBzuuJkksnr5dfTT6N5Ou/l1kfjyu/jlMEwFTSmaNw0o7OqXEcMbFmb18qv+SDR33aYkQ1m9OMCfrm83vzYfjOZdqUK5Luga/32IxyVNG1mv1z7Jaonj9NgG9/BrWG9f3GODfc2dO9fqEFKG3XM9uKdfw3o16bMjbp1p/yEZunx4vdK5SimpFXZrPn6d6ThyLq9LH+R0sRpvZPVmHHO6wm4BaXcsXpdcny6rt1/Hd0X/jq/xGsckTY3f4XDo6aef1siRI3Xfffdpzpw5ys3N1e233y6n09nqZgHNBbUXX3xRV155Zat/P/zhD636FRBl3Qc2yeGK7lXJDpep7oM4q5oMPC5pVJ5P0b3bplTcN7bto+ew5tePVts2lJHrj9mZoERW3Dfad500NLSXTznp1qyWMHFAk64aXae00/aLhkyNLWjSDeNr5Yhh4ReJ57rrrrM6hJRh91wbhnT9+XUqKWw6cWuckzxO6cqiel04iDv1Jrte2UEVdvUren2nUFsqifrxNvHl5PnlyY5m3yyU6x5DGcecbmSfpjZ9o3Plcpganc8JydPlDm6SjPavhOgsV3pQXfrGJ9dJM/NMkoYOHdpmwdU777xTI0aMUEZGRstjkV7TisTm9Eg9hnt1ZHNkazNEosdQr5zRL5bDIpMGNmrTAY+idQY1yxPU6LzYdkoyewaU1cevukPR2H2Hfu/eo7wxnS2XqIb28is3M6CK+mjcVjuU60kDrL2T2MQBTSrp26Sygx69uD60Btr3Pl+lrhlJsfwpoiySdbgQHYmQa7dTmllcr8+PaND8v3eTJM0qrlNRnyZ5kmpEgfZMGujVi+tdik7fyVB+F7/6MvOsDcMh9Rrl1b4PI1ufN4JXVNd+TUrrEpslKRKZxyWd18+r1TujN2YcW9ikDA99q9N5skx1H+SLeD2/SPQa6ZUjGl31CCTNzLNw1q1b1+qSTaSWgvMb5c6IzkHClRFUwQRrB76Irn7dAzqvr1fn3vkLHRyvGl0vVxx23v0/V3/irM25MpTVx68ewzgLeSYOQ7p6TP2J784134aK+jRpqA0u+3Y7pXGFJ//mFM4AdEaXU2bPFhdSOEs1YwuaNLBHNGbumzIMU1ePqecEXhi9R3mV0T06/QaHy1S/C62/MZRdXTq0IWpr3WZ6gpo6nFyH03dSvZye6OQ6rUtAecXxG58n9eGutrZW27dv1ze/+U2rQ4FFXGmmBlxSp20rsiUz/JG5wzt5GKYGTqmTK41BZrK5oqheuytc7c4uav+OOqEzr8WFXo2K0/TszNyACic0aN+aTIU78xvJ3WmcnqAGTqmTkfSnUc7eoJ5+XTiw8cTdxc6c60juuJSTFtRVDBAAAAnOMKSZY+v11Ls5qveF70BE0ne6dGiDCph1FpbDKQ28tF5bXslR0H+245hQrvtdWM+ss3aku6VZ4+r0xzXZCrYzZozk7rFfHluvLMaMYXmyTPX/XL12/jM77DaRjGMMh6mBl9TJEceKVlIPmbKzs3X06FF9+9vftjoUWKhrX78GXVp39jN1DFMDL6lX137WzxhB9KW7pa9fUKPumc2dt862k9CMohlj6zveNIr6jPUqr7hBnT/zG/r9nJ6ghl1Zq/SudKQ6ckVRw4n1WM4u1zlpQX39ghpl05FCgrnkkkusDiFlkGskkm6ZQd08qVYZ7uY+ROf7ThcObNQlQ7mioyOZPQIaMr32HNZxNlQ4sV49R3CVQUcG9fTr2pI6Oc5yzGjI1Mxx9RrRh7XOOpI7xKd+F5392MlwmBo8tU7ZfeJbfE/q4hnQLHeIT8OvqpWnk7cM9uQENPyLtSyumeS6Zpj65kU1GpXXpJMFkvYOnKGfOQxTlw9v0HXn1cX9FuuGIRVOaNTAS+oinPrc/PsYyurt18gv1yirN2d7I2EY0pfH1usLo+rlckTSoTqZ66G9fLp9crV6ZlOkROJZuHCh1SGkDHKNRJPfNaBvTa7RgNxTL+Hs+BiZ5jI1Y2ydrihqYDZ2hLoU+DVyRrUyenTuRL4rPajBU2uVN44bMkRqdL5Pt11Yo9zMzvWRu2YEdMukWpXE+MZhyaT3KK+GXlEjd2bn+sjp3QIacXWNuvWPf5EyqS/bBE6Vk+fXqGuqdfiTdB35NE2+uvDVDndWUL1GetV7dKOc3HY9JWSlmbr+/DptOdSk1TvTtKsi/B/e6ZDG5Hv1ucGN6p1jbVGkx7Am5RT4dGhjuo595lGgKVy7Dt1Vs/cor3oMa+JSzU4yDOmCgV4N6+XTuzvStXG/R75AuF6/ob7d/LpgYKNG5/sYHCBh3X333RR14oRcIxF1zwzq1gtqtX6fRx/sStPB6vBDyzSXqeLCUN+JtTY7LyM3qKIv1+jIp2k6sjlNjVXhlxtxpQfVY1iT8sY1ymXRHb4TWb/uAd05pVof7ErTR+VpqmoIn+uc9KDG9/PqwkGNSqOy0mld+/k16tpqHdqUpqNb0uRvCD9AScsJqFeRV71Gxe8GAafjT4yU4nRL+SWNyhvXqNrDLtUfdaqh0qljW9MkhRYwzOwZUHYfP8WFFDWij08j+vh0rM6hPZUuHah26sNdobvvTBnSoPyuAQ3I9SvTRnfQ8WSFFoEtGN+g2kMu1R91af/a0B2Ge470KjPXr6xeAWX0CFDIOUe5WUHNGFuv6SPrtbvCrQNVTr21LZTriQMaldcloH7d/OplcVEViIZVq1ZZHULKINdIVIYhlfRtUnFhkw7VOLXvuFMHq536qDzUd7psWIMKuvo1INfPzSXOkeEIzdbpVeRV/VGn6o641FDh1NEtoXFM4YST45h4rgOVjNxO6eIhXn1usFd7K53aX+XS4Rqn1u0N5Xr6yHoVdA2of3e/HIwZz4krzVTh+EbllzSeGMc4T6zrLPUc7lVGbkCZPf3K6m39OIaPFVKS4QjNRMvJC01/bi6e9RnLtGaE9MgKqkdWk0qkluLZ5SPsvTaH0x1a469rX39L8WzA5PiuxZYq0t0nC63NxbOrRnNnJQBAajIMKa9LQHldQpe7NRfPLh1m775TIjIMKatXQFm9QrluLp7lFTOOiTaHIfXPDah/bijXzcWzzw0m19HmcIYuUe5S4G8png2YYq9xDHVSAAAAAAAAIAyKZwAAAGijtLTU6hBSBrkGAMDeKJ4BAACgjWXLllkdQsog1wAA2BvFMwAAALQxb948q0NIGeQaAAB7o3gGAAAAAAAAhEHxDAAAAAAAAAiD4hkAAADaWLBggdUhpAxyDQCAvVE8AwAAQBtFRUVWh5AyyDUAAPZG8QwAAABtXHHFFVaHkDLINQAA9kbxDAAAAAAAAAiD4hkAAADaGD9+vNUhpAxyDQCAvVE8AwAAQBtr1661OoSUQa4BALA3imcAAAAAAABAGBTPAAAAAAAAgDAongEAAKCNxYsXWx1CyiDXAADYG8UzAAAAAAAAIAyKZwAAAGjjxhtvtDqElEGuAQCwN4pnAAAAAAAAQBgUzwAAAAAAAIAwKJ4BAACgjTvuuMPqEFIGuQYAwN4ongEAAKCN2bNnWx1CyiDXAADYG8UzAAAAtDF9+nSrQ0gZ5BoAAHujeAYAAIA2jhw5YnUIKYNcAwBgbxTPAAAAAAAAgDAongEAAKCNoqIiq0NIGeQaAAB7o3gGAACANpYsWWJ1CCmDXAMAYG8UzwAAANDGQw89ZHUIKYNcAwBgbwlXPHvnnXd08803a8yYMcrPz9fo0aN19913q7q6umWbyy+/XLfffruWL1+uadOmqbCwUCUlJXrhhRcsjBxAojFNae9xZ8v3VQ2GhdEAQHzRb4ofcg0A9uUPSJ8edLd87wtYGAws47I6gM7atGmTLrjgAn39619Xdna2tm7dqp/+9KeSpIULF8rv96usrEyVlZU6dOiQ5syZo5ycHD388MOaM2eOpk2bpi5dulj8WwCwu73HnXplY6YO1ZzcTS74R1eN6OPTjLH1yvSYFkYHAAAAIJZMU/pgV5re2pauRt/JeUf/72/dNHlwoy4e0iiDc+spI+GKZ3feeWfL18FgUJMmTdKmTZu0atUqSdKWLVvk9Xo1ePBgLV26VA5HqJE3NTXppptu0t69ezVq1ChLYgeQGPZXOfX71TkKBFs/bsrQp4fcOlaXrW9eVKM095mfDwAAACCxvb09Xf/YmiGp9Ulzr9/Q37dmqNFvaPrIBmuCQ9wl1GWbgUBAS5cu1Re/+EWNGDFCPXv2VO/evbVo0SJlZWVJkjZs2CBJeuCBB1oKZ5JUWVkpScrLy2t5rLy8XFdffbUmTpyoyZMn6/3334/jbwPArt7YnKFAMFQsa8vQkVqX1pSnxT0uAIinFStWWB1CyiDXAGAvNY2G/rk1/cR3Z55e9t6OdFXUJVRJBecgof7Sd911l+655x5NmjRJCxYs0Ouvv64333xT2dnZLbPJNmzYoG7dumnChAmtnrtx40YVFBQoNze35bF7771X11xzjdasWaPHHntMt99+u5qamuL6OwGwl6O1DpVXusMUzpqZWrOb4hmA5FZWVmZ1CCmDXAOAvazbm6aOF2kxtXYPY4JUkTCXbW7evFlLly7VggULdMstt7Q8vnr1atXW1qq4uFhSqEjW/PWp1q9f3+rxY8eO6YMPPtDixYslSRdccIHy8vL09ttva+rUqR3Gc2oRDolv5c9Du8ZE+rt+74/EHAuDzp+hL39/eQdbGapudKpnrz4KBnxxiauzErFNS4nRRk6XiDFLiRl3IsZsV/fee2+H28yfP7/D7ebPnx+tkJJWMuaaz2L8JGquEzHuRIxZSsw+XyLk+oo7n9GIi/5FDmf4kkkwENAflr6hG+bPil9gnZQIuT6dXdt0wsw827dvnyRp2LBhLY/5fD7NnTtXklRcXCzTNLVx40aVlJS0em4wGNSmTZs0duzYlsf27t2rXr16KS3tZKW4f//+2rNnTwx/CwB2F2iKbN2CYMCvYNAf42gAAAAAxJu/qUGR3A3AH+HYAYkvYWaejR49Wunp6XrooYd03333qbKyUr/+9a91/PhxOZ1OjR07Vrt27VJNTU2b4tm2bdtazU6LhoqKiqi9Fqy39reh/xPp7/qT10L/E3N0+QLS//ubKa8//MHSkKmi/KAqjh2LY2Sdk4htWkqMNnK6RIxZSsy4EzFmuyovL+9wm/nz5+vWW29td5sf/ehH0QopaSVjrvksxk+i5joR407EmKXE7PMlQq63HHLrz2ud7W7jcLr073fO0OL/tO/vkQi5Pl282vSMGTM6tX3CzDwrKCjQk08+qcOHD+vmm2/Wr371K82ZM0eTJk3S0KFDlZmZ2XKzgNOLZ+vXr5ckjRs3ruWxvn376siRI/J6vS2PlZeXq1+/frH/ZQDYltspTRzQ2M4WpkwZunCQt51tACDxNc/uR+yRawCwl2G9feqeGZARZuUzQ6Zy0oIalcea6akiYWaeSdLVV1+tq6++utVj11xzTcvXM2fOPGN18vrrr9f111/f6rEePXpo0qRJ+uMf/6hvfvOb+uCDD3TgwAFNmTIlNsEDSBiXDWvUkVqnthzyyDhRLJPU8vUXRtVrYA8u2QSQ3K677jqrQ0gZ5BoA7MVhSDdNqNXvPshRrdeQZCp0183Q/+luUzdNrJWr/clpSCIJVTyLtscee0zf+c539Jvf/EZut1tPPfWUPB6P1WEBsJjTIX31/DqVHWzSmt1pOljtlNOQhvb2adIArwq7BawOEQBirqSkRKWlpVaHkRLINQDYT4/soGZPqdba8jSV7vWo1utQlieo4r5NGt/fq+y0ju/HieSR0sWzgQMH6tVXX7U6DAA25DCk0fk+jc635900AQAAAMRWpsfUlKGNmjK0vWVdkAoSZs0zAAAAAAAAIN4ongEAAKCNSy65xOoQUga5BgDA3iieAQAAoI2FCxdaHULKINcAANgbxTMAAAC0cffdd1sdQsog1wAA2BvFMwAAALSxatUqq0NIGeQaAAB7o3gGAAAAAAAAhEHxDAAAAAAAAAiD4hkAAADaKC0ttTqElEGuAQCwN4pnAAAAaGPZsmVWh5AyyDUAAPZG8QwAAABtzJs3z+oQUga5BgDA3iieAQAAAAAAAGFQPAMAAAAAAADCoHgGAACANhYsWGB1CCmDXAMAYG8UzwAAANBGUVGR1SGkDHINAIC9UTwDAABAG1dccYXVIaQMcg0AgL1RPAMAAAAAAADCoHgGAACANsaPH291CCmDXAMAYG8UzwAAANDG2rVrrQ4hZZBrAADsjeIZAAAAAAAAEAbFMwAAAAAAACAMimcAAABoY/HixVaHkDLINQAA9kbxDAAAAAAAAAiD4hkAAADauPHGG60OIWWQawAA7M1ldQDJbm75Ln1SXxf39x2dmaV5/QfG/X0BAEhGHM8Be7DqsyjxeQSAVEbxLMY+qa/Te7U1VocBAADOAcdzwB74LAIArMBlmwAAAGjjjjvusDqElEGuAQCwN4pnAAAAaGP27NlWh5AyyDUAAPZG8QxIQL7Aya/9gfDbAUCyCARPft3kty6OVDJ9+nSrQ0gZ5BpnEjRPft3osy4OAImJMWN0seYZkCAOVju1ttyj3RVuHa09Wfd+eEU39coOaFAPvyb096pHdrCdVwGAxHG83qG1ezzafsStwzXOlscfXtFNPbOC6pfr1/h+XhV2o0cYC0eOHLE6hJRBrtGszmto3V6Pth726GD1yf3ez1Z2V9eMgPp2C6ikr1dDevplGBYGCsCWDlU7tHZPmnYfc+tI3ckx43+t6Kbe2QENPDFm7MmYsdMongE2d7zBoVc2Zmr7Ufcpj548FRk0pUM1Lh2qcWn1rnQV5TXpqtH1yk4z274YACSAhiZDb5RlaP0+j6Qzjw6P1jl1tM6pdXvS1L+7TzPG1tMRBJCw/AHpn5+la/XOdAXMM+33TFU1OFXV4NQnBzzqmR3QjDF16p/LyQMAUlWDoVc3ZemzI2ceM5qnjBk/2JWukX1CY8acdMaMkaJ4BtjYJwfcWr4hS00BQ6GdX3Nn6tRO1alfmyo76NGuYy59paROQ3pxbROAxLKn0qnnPs5WrdehUzt9rbXe75VXuvXEO130xdH1Or9fUxyiTA1FRUVWh5AyyHVqq6hz6M9rs3Wk1qnI9nvS0Vqnnl6doylDGvX54Y3MQgNSWNlBt/6yvnNjxk8PebSrwqVri+s0rDdjxkiw5hlgUxv2ebRsXfNOUAo3+6K10DYNPocWf5Stzw5THweQOMornPrDhzknCmdSZ/Z7/qChlzdm6cNdaTGLL9UsWbLE6hBSBrlOXRV1Dj29OudE4UyKbL930tvbM/TXzRkymTwCpKRPDri19OOzGzM2+kKF+y2H3B1sD4niGWBLh6qdemlDptrb+f3iptC/cIKmoWXrsnW8no85APur8xp6dm22fIGz3+9J0l83Z2h3BScOouGhhx6yOoSUQa5TUyAoPftx1iknDNpqf78XmmWyZne6Svd6YhEiABs7XOPQi6VZMs9xzPh8aZYq6hgzdiQhM/TOO+/o5ptv1pgxY5Sfn6/Ro0fr7rvvVnV1dcs2l19+uW6//XYtX75c06ZNU2FhoUpKSvTCCy9YGDnQsUBQ+suGTAXPuN5F5zQFDC3fmMnZSAC299onmar3RaNbYmj5hsxWd5jC2aHPFD/kOjWt2pauwzXnWuwP9RffKMtUdQPXbgKpImhKL23ICrNGYuf4GDNGJCGLZ5s2bdIFF1ygxx57TM8//7x++MMf6q9//avmzp0rSfL7/SorK9PatWv11FNPac6cOXrmmWfUs2dPzZkzp1WRzW5Mn0++O+co8MRTrR4PvPgX+W6+VWZtrUWRIV4+PeTWwepozZowtfOYW+WVzMIAom1/lVN/WZ/Z8v2T7+To4z0ebgV+Fg5VO7T5oEfh1/rpnIp6pzbss3YWBsdzwB7s+lls9Bl6f2d61F7P6zf0XhRf72z4AtKa3Wl6/O2clsde3pipwzUJOeQEbG3rYbf2V0VvzLi7wq2dxxgztichs3PnnXe2fB0MBjVp0iRt2rRJq1atkiRt2bJFXq9XgwcP1tKlS+VwhHbYTU1Nuummm7R3716NGjXKktg7Yrjdcv37D+X/7j0yJk2U47wSmTt3Krjo93L+9CEZ2dlWh4gY+6g8muv1hM5ErNmdpgG5LAQJRMu6PR69vDGz1WMHq516eWOW1u/16KaJtfIk5BHWGif3e9GbNbFmd5rG97fu5gEczwF7sOtncf0+T7uXqZ/Va+71aOqIBrmdHW8bbQ0+Q898kK2D1S4Zp5wIWbfHo9K9Hn2lpE6j8n3xDwxIUh/tjs2YcXBPxozhJNxpgEAgoKVLl+qLX/yiRowYoZ49e6p3795atGiRsrKyJEkbNmyQJD3wwAMthTNJqqyslCTl5eW1PPZf//Vfmjhxonr06KGXXnopjr9JeMbAAXL8660KPDpfZkWF/I/8XI6ZM+QYN9bq0BBjTX5p9zGXojX7otm2I26m4QJRsr/KqZc3ZsqUWq0x0fx1eaVLf92cGebZOJPWt1WPBlOHalyqabT2EqZEP56vWLHC6hBSBrmOLTt+Fj87HP0Fuhv9Du216GqD5RsydbA6VLU7/dgYNKXnS7N0rDbhhp6ALfkDOjFLLLoDvO1H3QoyZgwr4fZgd911l+655x5NmjRJCxYs0Ouvv64333xT2dnZLbPJNmzYoG7dumnChAmtnrtx40YVFBQoNze35bGpU6dq6dKl+tznPhfX36MjjlkzZfTvJ/8d35GcTjluvcXqkBAHB6udJzoc0R3wef2GKrhxABAVJ+/mGO5zamjDPo/qvKw9E4n6JkNVDU5FtwMYyn30Lmc4e4l8PC8rK7M6hJRBrmPPTp9F05QOVMdmetj+GL1ueyrrHfr0kFvtHReDprQmqldXAKnrUI3zxPrY0e1r+gIGRe52WN+r7ITNmzdr6dKlWrBggW655eQBb/Xq1aqtrVVxcbGkUJGs+etTrV+/vs3jF1xwwVnFcmoBrj3Onz8iR/G4Tr++YRgyxo2VufZjOb72VRnuzp2deufdd5X7uSmdft9UtfLnoUFbpH/XWBk5+WZdOfsPrR7r6M5y4X5+z59afz/1qq+qfKO1Z7a/90d75DkV2KVNd1YitJE7fnNM6dntDwCCpqGp19ypraufjVNUnWeXXPcZPEFfe2iNTu0ARmu/N/ueB1X6xsJzC/CEZDue33vvvR1uM3/+/A63mz9/frRCSlrJmGsr9x9WfRal6H0e3enZuut/a1o9Fq393mP/83vN+N1d5xBd542deqcu/8bjHWxlaMWH+3XjxUPiEtPZsMtxsbMSsc+XqLm2i+EX3qAvzvlzq8eitQ/5wsybtLP01XOI7tzZtU0nVFlx3759kqRhw4a1PObz+VpuFFBcXCzTNLVx40aVlJS0em4wGNSmTZs0dmxiXCph7typ4OI/y3HD9Qr+cbHMw4etDglxYDhid7bQ4UyoWjlgW053ZGfOnW5rF25OFIYRu/2e4bB+v8fxHLAHO30Wk62/54rweBfpdgDaF8t9iMGYMayEyszo0aOVnp6uhx56SPfdd58qKyv161//WsePH5fT6dTYsWO1a9cu1dTUtCmebdu2rdXstHNVUVER0XbXfPqJ3qut6XjDU5hNvtBaDNfOkvMbt8qsrFTg54/J+bP/kuGIrN558eTJejHCGCGt/W3o/0j/rrHy6SG3nl3b+rHTzwY0az57EO7np3vp+SXq193a2wD+5LXQ/1bnORXYpU13ViK0kSfeSdOharPVmi5n8uzvFqpvd/vMFDmdXXJ9tNahX61q/Vi09nuPPvKQzvvT3LMP7hTJdjwvLy/vcJv58+fr1ltvbXebH/3oR9EKKWklY66t3H9Y9VmUovd5DASl/3rDPLG2UOhYEq393re/eYuW/vy6c46xMz477NLij9rfxpCpMUN7WX7MaY9djoudlYh9vkTNtV2c6TMXrX3I88/+wfIbzcWrTc+YMaNT2yfUzLOCggI9+eSTOnz4sG6++Wb96le/0pw5czRp0iQNHTpUmZmZLTcLOL14tn79eknSuHGdn+Ydb8FFT8twueS4JdTSnXfdKfPgIQWff9HiyBBr+V1isaMyZchUnxxrC2dAspg4wNtu4cyQqd45fhV24zMXidysoNzO2KxOm9/V2r9Boh/Pm2f2I/bIdWzZ7bPodEi9cwKK9npFUqz6ku0b0suvLukBtbd2pSlDE/p74xcUkMTyusSifxP6/PbJ4W6b4SRU8UySrr76an300Ufav3+//v73v+uaa67R448/rvfff1+SNHPmTFVUVGjAgAGtnnf99deroqJChYWFVoQdseC6UgVfe13Of79fhis0MdDIzJTz3+5T8Jk/yty50+IIEUtd0k3lpAej/KqG+nQJyJNQ80wB+xpX0KR+3X060yDBkCmHIV01ukEG9wuIiMOQ+naLfkfN4zTVK9u64lkyHM+vuy6+s1dSGbmOHbt+FqO/3zNPvG7893sOQ7p6TP2JUuCZC2jDejVpZB9fPMMCklZOuqmuGdH+rBvqlR1QevRvBJw0Eq54Fm0//elPNXr0aK1Zs0bf//73NXr0aO20sEPrOK9E7uUvyOjXt/XjY0bL/fKLMgYNsigyxINhSOf1jf5ZufP6NkX9NYFU5XJKN0+sVUnfJjmM1oOEntkB3XJBreXT3RNNLPZ7xX29clrYy0mG4/nps/gRO+Q6duz6WTy/X3PfLFozbw0N6uFTt8xon4SNzLDeft04sVa5p72/02Fq4oBGffX8OnXiClkAHYjF+O68fswObU/Kz0V58MEH9eCDD1odBtBifD+v3tuRLn8wOtNW0t1BjStkRwhEk8clzRxXr2kjGrTjmEv+QOhsXWG3ADPOzkJRnk85nwZV443GyMqUYYQurwUAu8rvGlD/7j6VV0ZjmocpydAFA63d7w3t5decS6tVXulSRZ1DbqepIb38ynDH5tJ8IJWN7+/VuzvS5QtEp+OZ5gqqhAkX7aL+D9hMlwxTlw9vOPHduXQ2Qs/94qgGpt8CMZKVZmpsgU/n9WtS3+4Uzs6WyynNGFt34rtzHWQZunhwo3plWzP7AgAidfWYejmNaBSWDBXlNWl4b+svizQMaUCuX+f1a9KYAh+FMyBGstNMTR0RvTHjlUUNfF47kPIzzwA7umCQV9uPurX9qFvNZxNP1/4dU0LPGVvg1dgCziAAsL9hvf2aOKBRa3an6+z2eyF9u/l1ydDGqMeXii655BKrQ0gZ5Do19coJ6oqiBv11c6bOZb/XNSOgL42u5wQOkGImDfBq+xGXPjvi0bmMGUflNTHrLALMPANsyGFIXz2/VoN6+HR2d2IKnYGcOY6OFIDE8YVRDSfWP+vsjit0prSwW2jNHZcz6qGlpIULF1odQsog16lr0kCvpo6o19nu97plBPT1SbXKSmPGCJBqDEO6/vw6Del59mPGEX2adE1xHWPGCFA8A2zK45JumlirS4c2tFmUvD0uh6kriup13Xl1li6WDQCd5TCkGWPrdfWYOnmckez3Tm5z0aBG3XpBDZccRNHdd99tdQgpg1yntouHePUvE2qUkxbJ5ebN+zhDY/Kb9K3JNcrN4jJ1IFW5ndK/TKjVZcM6N2Z0OkxNG1mvr55fx0nHCHHZJmBjTod02fBGjcpv0uqd6dq43xP2RgJpLlPFhV5dMNBLJwpAwjIMaXz/Jg3t5dMHu9JVutejBt+ZzwQ4DWl0vlcXDPKqoGu0b9mOVatWWR1CyiDXGN7br9mXVOuj3Wn6qDxN1Y3hz4AO69WkCwZ6NaQXd3YGEBozXjqs9Zgx3I0EPE5TxX1DY8YejBk7heIZkAB65wT15XH1unJUvfZXuXSgyqlar0OGIeWkBZXfNaCCrn65OWsAIEl0zTB1RVGDLh/eoAPVTu2vcqm60SHTlDI9QeV3Cd3dNJ2ZZgCSRIbb1JShjZo8uFGHapw6UO1URZ1TQTN0kjSvS0CF3fzK5hJNAGfQKzuoGWPrdUVRvQ5UubT/lDFjdlpQBV0Dyu/il4cq0FkhbUACSXNJg3r4NagHZxoBpAaXU+rXPaB+3ZlZBiA1OBxSfteA8plRC+AspLmkgT38GsiYMapYEQkAAABtlJaWWh1CyiDXAADYG8UzAAAAtLFs2TKrQ0gZ5BoAAHvjss0YG52ZlVLvCwBAMkrF4/m8efN03XXXWfb+qYRcR87KzwT9awBIXRTPYmxe/4FWhwAAAM4Rx3PAHvgsAgCswGWbAAAAAAAAQBgUzwAAANDGggULrA4hZZBrAADsjeIZAAAA2igqKrI6hJRBrgEAsDeKZwAAAGjjiiuusDqElEGuAQCwN4pnAAAAAAAAQBgUzwAAAAAAAIAwKJ4BAACgjfHjx1sdQsog1wAA2BvFMwAAALSxdu1aq0NIGeQaAAB7o3gGAAAAAAAAhEHxDAAAAAAAAAiD4hkAAADaWLx4sdUhpAxyDQCAvVE8AwAAAAAAAMKgeAYAAIA2brzxRqtDSBnkGgAAe6N4BgAAAAAAAIRB8QwAAAAAAAAIg+IZAAAA2rjjjjusDiFlkGsAAOyN4hkAAADamD17ttUhpAxyDQCAvVE8AxBT/oC07YhLq7altzz2l/WZem9HmnZXuBQ0LQwOljNN6UCVU2t2p2n5xsyWx/+2JV1lB91q8BkWRgektunTp1sdQsog14A16psMfXLArTc/zWh57JWNmfqo3KPDNQyVo6mm0dDGfW69UXYy1699kqF1ezw6VkeuYX8uqwMAkJzqmwy9uyNd6/Z41OBrfUBcvy+t5etuGQFNGODVBQO8cjnjHSWsYprS+n0efbArTQer2x6K3tke6li5HKbGFjTp4iGNys0KxjtMIKUdOXLE6hBSBrkG4utwjUPvbk/XJwc8CpitT9St3ZMm7Qn1VQu7+XXhwEaNzvfJ4HzeWdl33Kl3tqdry2G3zNNyvWb3yZPrA3v49LlBjRrW2x/vEIGIUDwDEHVlB916dVOm6po6Pot0vMGpNz/NVOneNM0aV6fCboE4RAgrHa936KUNmdpV4T7xiCnpzD1Sf9DQur1p2rjfo6kjGnTBQC+dVwAAcFaCpvTu9nS99Vn6iaJZe5dAmNp33KXnS7O1YZ9PM8bWKSedSyYi5Q9If9+aodU702Sq41zvOubWrmNujS3w6oujGpThIdewF+ZHAoiq93ak6bmPsyMqnJ3qaK1TT6/O0dZD7o43RsI6WO3UU+/lnCicNXeKOq6G+YOG3ijL1MsbM2XSlwLioqioyOoQUga5BmIvEJSeX5elv2/NOGW2WXt9kOafmfrsiFv/+14XLi+MUJNfWvxRtt7fmX5KySyyXG/cn6ZFq3NU08jZUtgLn34AUVO616OVn2Z2vGEYgaCh5z7O0p5Krt9MRtUNhv74YbbqWwqrne0UmVq3N00rT1mXBEDsLFmyxOoQUga5BmLv1U2Z2nzQo/ZnQJ1JqL9S3ejQHz7IVn0TRZ32mKa0bF2Wdh5rPlHamXyFtj1a69Qf12TLxwUpsBGKZwCi4ni9Q3/9pP3C2S9uCv1rT8A09Jf1WRwsk4xpSss3ZnU4I7H9NhKa8v/+znTtOsaqA0CsPfTQQ1aHkDLINRBbnx5ya93eNLVXzImkn1rV6Oywv5vq1pZ79NmR5iLl2ef6cI1Lf9/CCVPYR0IWz9555x3dfPPNGjNmjPLz8zV69Gjdfffdqq6ubtnm8ssv1+23367ly5dr2rRpKiwsVElJiV544QULI4fd+BoMHd6c1up7nJ03yjLUFIhO/irqnXp3R3rHG+KMGqtP7tqPfeZRwGdhMCeUHXRr+9FTL9U8W6E29somLt8EYo0+U/yQayB2AkHptU3NBa9z7aua2nTAox1HrT+JF2iSjm7xtHzfVGv9OKbBZ5xyFcq553r1rjTueppimupOtpsjn3rk91rfrptZ/6k/C5s2bdIFF1ygr3/968rOztbWrVv105/+VJK0cOFC+f1+lZWVqbKyUocOHdKcOXOUk5Ojhx9+WHPmzNG0adPUpUsXi38LWMkMSvvWZOjw5jSZwZMfyA1Luqr3KK/6TmqQwX46YscbHNpy6GymZoe3tjxNU4Y0ysnfIWKBJmnXqiwd332yI7VrVZYc72eq78R69Spqsiy2D3c3F6mj0z6O1Tm146hLQ3pxRyYAABBe2UG3arzR6lCG+jFrdqdpcE9r+iCmKR3amKb9H2fIPOXE9cZnuyp3SJMGXFwvh0Wj/PV7PVE7mX5qrr80piFKrwm7CgakPe9n6ujWk+OY8neztGe1qbxxjco/r9Hym4YlZPHszjvvbPk6GAxq0qRJ2rRpk1atWiVJ2rJli7xerwYPHqylS5fK4QjtLJuamnTTTTdp7969GjVqlCWxwx72vJ+hI5+eYWaTaejwJ+kK+g0NuLg+/oElqI37PCfuohM9tV6Hth9xa3gfG0ybSgDBgPTZGzmqO9x2vbigTyp/L0uSLCmgVdY7tLsiusVVSSrdm0bxDAAAtGv9vrSON+oUU1sOudXQZFhyR8hDG9K076NMtZ3Nb6hiu0f+RoeGXllrSaFh/T5Pxxt10oZ9afriqAY5OKGetExT2vnPLB3f1bb9mAFDB9ZlyAxKhRMaLYjupIRrgoFAQEuXLtUXv/hFjRgxQj179lTv3r21aNEiZWWFBocbNmyQJD3wwAMthTNJqqyslCTl5eVJko4fP66vfe1rmjhxoqZMmaJrr71WO3bsiPNvhHhrPO44c+HsFEe3pKnheMJ9PCyz73hsFvjfV8WNAyJ1fLdbdYddOnNxKrRW2L41GQpaUGs62T6i24ujfQCxtWLFCqtDSBnkGogN04xFP9WQKUP7LeiH+L2G9n/cvA7Ymft81fvcqtkX/zkyvoB0qNqpc1+io7WmgKGj3OU0qdUddp6xcHaqgxvSW13SaYWEa4V33XWX7rnnHk2aNEkLFizQ66+/rjfffFPZ2dkts8k2bNigbt26acKECa2eu3HjRhUUFCg3N1eSZBiG7rzzTq1Zs0Zvv/22rrzySs2ZMyfuvxPi6+jW5sVC22Pq2JZon6VKXodqYtN5iNXrJqOjW9Iko712bSjgc+j4bnfcYmoWm7+jqcp6p5qYeAbETFlZmdUhpAxyDcRGTaOhBl9shrxW9FMrtntaLTlzRoZ5YrwTX4drnCeuRIl+geNgdUJeMIcIRTQ+Nw1VbIv+zMbOSKhWuHnzZi1dulQLFizQLbfc0vL46tWrVVtbq+LiYkmhIlnz16dav359q8e7du2qyy67rOX7SZMmaeHChRHF0lyAQ+J58KY/a8q4r8jZzmIAgWBASxe/ov/84vVxjCxxffvxI8rI6dnqsfbuoBPuZ/f8qfX3b/79Hd01feo5Rpcanvn37crvMbjD7X70b/+lJX9/OA4RnXTpLQtUcuXdrR7r6A5LHbeRUMds8LAi1VcdOrcAU9T3/hjqpHA8S0333ntvh9vMnz+/w+3mz58frZCSVjLmmv0HEkX3gpH6+n+3Lk6fex8kZN7Dj2r18z8+h+g671tf+m9dd8kPWl1d1YZpaPWqDZpw3cT4BSap76jP6yv/8fdWj0Ur19+95z5t/NtvziE62Nl/f/tNlQy9vN1LjQNBv5745TNa8MLs+AV2moSaebZv3z5J0rBhw1oe8/l8mjt3riSpuLhYpmlq48aNKikpafXcYDCoTZs2aezYsWFf/ze/+Y2+9KUvRT9w2EqDt0aRnBEJbYdIBGN0O8dAwLoF7hNNvbdaphmMYLv4t+tYtY9YvzYAAEhssewnBPzx76c2eGtkdLCYWTAYUH1jVZwiOuV9Y9nfsyDXiJ96b42CHYxjDDksGce0iqGioiL+qxyepf3792vChAkqKSnRfffdp8rKSv3617/W8ePHVV5ert27d+vQoUMaP368nn76ac2cObPluVu3btWFF16oP/3pT/riF7/Y5rV/9rOf6c0339RLL72kzMzMNj9H8qja49K2FTkdbjd0eo269ueasEg8/X62yis7vhyw+ezS6WeTwpk4oFFXjebuOpE4UJqu/WszOtjK1NgbquTJju9u/6PdHr36SVZE23amjWS4g/rhtCrL77yTqH7yWndJ0o+vqrQ4ElihvLy8w21KSkpUWlra7jb9+/ePUkTJKxlzzf4DiSIQlB5+o5sCZsedhc72U687r1aj8+N7Eq+h0qHNL3TtcLv+n6uL+02iar2G/t/fukW0bWdzfduFNRqQy7gsWR37zKNdqzoeK4ycUa2s3oGove+MGTMkSS+//HJE2yfUzLOCggI9+eSTOnz4sG6++Wb96le/0pw5czRp0iQNHTpUmZmZLTcLOH3m2fr16yVJ48aNa/O6jz76qFasWKFly5ZROEsBXfr6ld7dr/DXVZtK7xZQl77soCOV3zV6O7FTFcTodZNRzxFeOd3BdtY9M5U7pCnuhTMpdu0jv2uAwhkQQ80z+xF75BqIDadD6tMlRv2QGL1uezK6B9Wlb5PCjmMMU670oHKHxH+mVnaaqZz0jq+C6BxTkqm8LozLkln3QU1yZwXaHcdk9/Eps5e1Y8OEKp5J0tVXX62PPvpI+/fv19///nddc801evzxx/X+++9LkmbOnKmKigoNGDCg1fOuv/56VVRUqLCwsNXjP/vZz/T666/rhRdeUNeuHVfxkfgMQxp2Ra3SupzYubd8SEP/p+UENfSKWhkJ9+mwzsg+0T7rZsphmBrai0vyIuXOMDX0ylo5Xa3bc3P7zinwq//kektiy+8SUJeod6Zi0e4AnOq6666zOoSUQa6B2BkRg/5C7+yAumdGv28TiUGX1SuruYhw2jjGlWZq2Bdq5bRoXfWRfaJdtDM0uKdfaQm1Ujs6y+GShn+hVu6MM49jMnIDGjy1zvKT5indDMvKyvSzn/1MgwYNapmyJ0mrVq2yMCrEgyfb1KhrqlWxw6Njn3nka3DInRFUj6FN6j64Sc7435AwoQ3I9atXdkBHaqN11yFDo/O9yk5LmKvKbSG7T0Cjr6/Wsa0eVe7yKOgz5OkSUK8RTeraz2dZQdjhkCb09+rvWzu6rDRyHqepcYXeqL0egLYiuZQQ0UGugdg5v59Xb32WrqApRetOkBMHNFo2kHelmRpxdY2O73br6JY0eWsdcnlMdR/cpB7DmuSysP88sb9Xa3anK1T8ONcEhV5j4gD6e6kgvVtQo79SpWPb0lSx3SO/15AnM6gew5vUfVCTHPG/uW0bKV08KyoqUkVFhdVhwCIOl9RzeJN6DmcBynNlGNK0kfVa8lGOonGwdDlMXTq0MSqxpRp3hqm8Yq/yiu3V0Zg4oFEflaepuvFcK3ih9jVlaANnIQEAQIey00xdNKhR7+6Izkm8HlkBFfe1dvxgOKTug3zqPshes/B75QRVXOjV+n1pUXg1Q/26+zW8t71+R8SO0yP1HuVV71H2Gsc048I0AFExvLdfxYVehQpnZ3vGK/S8y0c0qEe2NVPhERvpbunLY+ui8EqGCrv59blB9jyoAgAA+7lsWKN6ZZ/7ekmGTM0aVye3DWbB2NWVoxqUk3bu/XiXw9TMsXVysL4tbILz9gCi5ktj6lXV6NCuY26daQZa+3fUCW0/vp9XFw6kMJKMhvTy6wuj6vX65vA3ZgnfRkLtIzczoBvOr5WDUz9AzF1yySVWh5AyyDUQWy6n9C8TavW71TlhZ8F3fOdHUzPH1atvd25o1Z4Mt6kbJ9bq9x9kq9F3drl2Gqa+en4tJ9NhKww/AESN2yndOKFWYwuaZ6BFIjTbzJB0ydAGfWlMveWLQSJ2Lhjo1cxxdXI7T1sQtEOG+nf36RsX1SgnnbXwgHhYuHCh1SGkDHINxF73zKC+cVGNCrp25s6NJ24o5grq+vPqLL9cM1HkdQnoGxfWqGdWZwqNoVxnpwV148RaDevNHTZhLxTPAESV2yldW1Kvr55fq+6ZkRwwDRV09eubn6vR54dbt/gq4qekb5NmT6nWsF5NiqTImukJ6guj6nXbhbXcRAKIo7vvvtvqEFIGuQbio1tGUN+8qEZTR9Qr3RXZrKZReU2665Jqjcpn7a3O6J0T1LcvrtbkwQ2nnDQNz2FIJX29umtKtQb3pHAG++GyTQAxUZTn08g+Pm0/6tInBzw6UOXU0TqnAkFDbqepPjkBFXT1a1xhkwq7Mf091XTPDOrGiXU6Vteg0r0e7T3u0sEqpxr9DjkMU90zg8rvEtCw3j6NymuSi7VFgLjj7uPxQ66B+HE4pIuHeHXBQK827fdo+1G39lc5dbzeIVOGMtyhPkjf7n6d17dJ3TK5dPBsuZ3StJGNunhIozbsS9POYy7tr3KpptGQKSnLYyq/a0ADcn0q6dvESVLYGsUzADFjGNLQXn4N7XXy7JFpitllaNEjK6ipI07eWZX2AQAA4sHtlM7r16Tz+p28FJN+SGyku6VJA72adGJdY/NEjYxcI5FQPAMQVxwk0R7aBwAAsAr9kPggz0hErHkGAACANkpLS60OIWWQawAA7I3iGQAAANpYtmyZ1SGkDHINAIC9UTwDAABAG/PmzbM6hJRBrgEAsDeKZwAAAAAAAEAYFM8AAAAAAACAMCieAQAAoI0FCxZYHULKINcAANgbxTMAAAC0UVRUZHUIKYNcAwBgbxTPAAAA0MYVV1xhdQgpg1wDAGBvFM8AAAAAAACAMCieAQAAoI3x48dbHULKINcAANgbxTMAAAC0sXbtWqtDSBnkGgAAe6N4BgAAAAAAAIRB8QwAAAAAAAAIg+IZAAAA2li8eLHVIaQMcg0AgL1RPAMAAAAAAADCoHgGAACANm688UarQ0gZ5BoAAHujeAYAAAAAAACEQfEMAAAAAAAACIPiGQAAANq44447rA4hZZBrAADsjeIZAAAA2pg9e7bVIaQMcg0AgL1RPAMAIIUEgtKRmpOHf6/PwmBga9OnT7c6hJSRSLn2+k9+fbjGoUDQulgAAIgXl9UBAACA2GrySxv3e7R+X5r2VzkVCBotP3tkZXflZgY0Ms+nCf296p7JSBghR44csTqElGH3XB9vcGhtuUdlBz06Vney+P74213lNEzldQ2ouLBJ4wq8SnNbGCgAADFC8QwAgCRlmtKG/R69vjlDjb5wk81NVdQ79d4Op97bkaYJ/b2aPrJBHnoIQMrzBaS/bcnQh7vSZKq56G622iZgGtp33KV9x13625Z0XVHUoPP6Nskw2r4eAACJiq4xAABJyB+QXlyfpc0HPR1s2XqE+1F5urYdcevGibXqlc0stFRWVFRkdQgpw465rqhz6E9rslVR71Trgln4qpjX79DLG7O09ZBbXzmvTm5nzMMEACAuWPMMAIAkEwhKz36cfaJwZna4/UmhQfHxBqd+tzpHx2rpJqSyJUuWWB1CyrBbrivrHXp6dc6JwpnUXsGsLVNbDnu05KNs+QOxiA4AgPhL+V7xnDlzlJubq1tvvdXqUAAAiIq3PgvNHgsVzs486P3FTaF/4dQ3OfTcuiwWA09hDz30kNUhpAw75ToQlJZ+nKVab/hhQvv7D0OSqZ3H3Pr71oxYhAgAQNyldPHsrbfe0ksvvaScnByrQwEAICoOVDn1zo50tVc4i9ThGpfe3pYelbiQeF544QWrQ+iUoBm6E6TZmcmWNmGnXL+/M00Hqs91ZZdQAe39nWnaW8m1mwCAxJeya541NDTo+9//vn74wx/qt7/9rdXhAAAQFe9sT5dpRm+l7vd3putzgxu5gQBs63CNQ+/vTNfG/R4FgoZcDlPFfZv0uUGNys1i6mRn+ALSezuiVTAP7Yfe3p6uf5lQF6XXBADAGkk18ywYDOqXv/ylJkyYoPz8fE2ZMkXvvvuuJk2apHvuuafVtg8//LAyMzN11113WRMsAABRVtNo6NNDzZdrRkdTwNCG/R3ddACwxs6jLj31bhet3xsqnEmSP2hobblHT7zTRfuOM+upMzYf8Kgh7J15z4aprYfdOt6QVEMOAEAKSqoj2Xe/+109+uijuvXWW/Xcc89p1qxZ+ta3vqVdu3appKSkZbvS0lI98cQTeuyxx+RycSodAJAcdlW4FDQNnevlmqfbcdQd1ddDYlixYoXVIbTL65P+vDZbgaBktmnzhnwBaclH2Qmxbp9dcr39aLT7xaH90c6ovy4AAPGVNEey559/XkuWLNHLL7+syZMnS5KmTJmi9evX65VXXtG4ceMkSX6/X9/73vd0yy23aOLEiVaGDABAVB2ois1hfX8Vs3dSUVlZmXr37m11GGFt2J+mpkD4QrEpQ3VNhsoOujWmwBfHyDrPLrmO1T7kQLVT58XklQEAiI+kKZ499thjmjp1akvhrNngwYPldrs1evRoSdL//M//6MiRI/o//+f/nNP75ebmntPzAQCIti98Z4lGXPS1Vo+1d0fN9n5+z59Ofl1Za3LcSzL33ntvh9vMnz+/w+3mz58frZA67Ut3L9Pg8TPlcIbvzgYDfv3kF3/W3/73W3GMrLVEyvXs/62RJz275fto7D8k6c/Pv66bp1xzjtEBAGCdpLhsc9++fSorK9PMmTPb/Gzv3r0aOXKk0tLStHfvXv33f/+3/u3f/k2maaqqqkpVVVUKBoPy+/2qqqqSz2fvM5MAAIRjOGJzWDeMpOguIMkYTpdkdHyJssORNOeKYy5Wn3XDwexVAEBiMyoqKhLwht6trVmzRldeeaWeffZZTZ8+veXxhoYGjR8/XlOnTtUvf/lLvfPOO/ryl7/c7mv95je/0Ve/+tVYhwwAQNS9uilDH5VHdqe85hkjp88QOZNMT1A/nFZ1DpHBbsrLyzvcpqSkRKWlpe1u079//yhF1Hn/2JquVdvS1dEaf1cW1evCQd74BHUGiZTr+X/vqurGjgtondl/SFJJoVczi+vPITIAAKJrxowZkqSXX345ou2T4lRcjx49JEnbtm1rVTxbuHChDh482HKzgLFjx2r58uVtnn/77bdr6NCh+vd//3cNHz48LjEDABBteV0CMXnd/Bi9Luxt7ty5VofQrvH9vHp7W7pMmTpzAc2U0yEV922Kd2idZpdc53f1q7ox+nfXzevKPgQAkNiSong2cOBAjR49WvPnz1f37t2Vn5+v5cuXa+XKlZKk4uJiSVLXrl118cUXt3l+WlqaevToccafAQCQKPrn+k98Fa6YcK6vi1Ry3XXXWR1Cu7pkmJpe1KAVZZk6vc0bMmXK0JdG1ynDbf+LLOyS6/7d/dpyKJrFs9DfpX939iEAgMSWFIuYOBwOPf300xo5cqTuu+8+zZkzR7m5ubr99tvldDpbbhYAAEAy65UdVP/uPkWvcGbKMEyV9LXukjdYp3nmvp1dNMira4rr1D0z2OrxntkBffX8Wp3Xz/6zziT75Lq4sElOw1So6BUNhgq6+pXPzDMAQIJLiplnkjR06NA2l2TeeeedGjFihDIyMtp97vr162MZGgAAcXPRIK/KK92KzuwzQ2PyveqSbv+ZO0hd4wqbNLagSQ/9tbsk6duTq5XXJRDJvQRwmqw0U+P6NmndnrQovFpoH3ThoMYovBYAANZKipln4axbt67lkk0AAFLBiD4+FfVpUjRmn2W6g7qyqOHcgwJi7NRCWX5XCmfnYvqIBmWnBTvesEOGhvVq0ph87mQPAEh8STPz7HS1tbXavn27vvnNb1odCgAAcWMY0pfG1OtgjVOV9c6w23V0lzyHYWpWcZ2y0ph1lqouueQSq0NIGXbKdYbH1DXFdfrTmmwFzTNXISO5y2bXjIBmjK2nkAkASApJWzzLzs7W0aNHrQ4DAIC4y0ozdesFNfrDhzk6VudUZy/hdBqmvnJenYb1ZpHvVLZw4UKrQ0gZdsv14J5+3XB+nZauy5I/aCjyfUhou+6ZAd0yqVY5XPINAEgSSX3ZJgAAqaprhqlvfa5a5/fz6uSgN9xA9uTj+V38+tbkahXlcalVqrv77rutDiFl2DHXw/v49O2Lq1XY1a/I9yGGSvp69e3JNW1u4gAAQCJL2plnAACkujS3NGNsvc7v59Wa3WnadMCjwBnHs4b6dfdrYv9Gjcr3ycmpNUhatWqV1SGkDLvmuld2UP96UY02H3Tro/I07a5wn3E7hyGNzvdq4gCv+nXnzpoAgORD8QwAgCRX2C2gwm71+tKYeh2qdupQjVNevyGnQ+qRFVB+lwBrmwE4I4dDGlPg05gCn+qbDO2vcupYnVOBoORxmeqTE1Bel4Dc4ZdYBAAg4VE8AwAgRbidUt/uAfVlZgiAs5DpMTW0l19De7EeIgAgtXBhBgAAANooLS21OoSUQa4BALA3imcAAABoY9myZVaHkDLINQAA9kbxDAAAAG3MmzfP6hBSBrkGAMDeKJ4BAAAAAAAAYVA8AwAAAAAAAMKgeAYAAIA2FixYYHUIKYNcAwBgbxTPAAAA0EZRUZHVIaQMcg0AgL1RPAMAAEAbV1xxhdUhpAxyDQCAvVE8AwAAAAAAAMKgeAYAAAAAAACEQfEMAAAAbYwfP97qEFIGuQYAwN4ongEAAKCNtWvXWh1CyiDXAADYG8UzAAAAAAAAIAyKZwAAAAAAAEAYFM8AAADQxuLFi60OIWWQawAA7I3iGQAAAAAAABAGxTMAAAC0ceONN1odQsog1wAA2JvL6gCS3dzyXfqkvi7u7zs6M0vz+g+M+/sCAAAAAAAkE4pnMfZJfZ3eq62xOgwAAAAAAACcBS7bBAAAQBt33HGH1SGkDHINAIC9UTwDAABAG7Nnz7Y6hJRBrgEAsDeKZwAAAGhj+vTpVoeQMsg1AAD2RvEMAAAAbRw5csTqEFIGuQYAwN4ongEAAAAAAABhUDwDAABAG0VFRVaHkDLINQAA9pbyxbP6+no98sgj2rBhg9WhAAAA2MaSJUusDiFlkGsAAOwt5YtnGzdu1H//93+rtrbW6lAAAABs46GHHrI6hJRBrgEAsDeKZxs3yjAMjRkzxupQJEmmzyffnXMUeOKpVo8HXvyLfDffKpMiHwAAiIMXXnjB6hAidrjmZJf2H1vTdawusbq4iZRrAABSkW16Fn6/X4899pjOP/985efn68orr9TmzZs1fvx43XfffS3bmaapp59+WpdddpkKCws1duxYPfDAA61mjgUCAfXt21c/+9nP2rxHfn6+FixYIEmaPHmy7r//fpmmqYEDByo3N1fTpk2Lzy8chuF2y/XvP1TwldcUXFcqSTJ37lRw0e/lvP8+GdnZlsYHAABgF01+6dm1WXr87a4tj63alq7/eaurXlyfKX/AwuAAAEDScFkdQLNvf/vbWrFihe69916NHz9eH3zwgW666Sbt2bNHxcXFkqRgMKg77rhDr7/+ur7//e9rwoQJ2rZtm37yk5/o0KFDWrRokSRp69atqq+v17hx41q9x6effiqv16uxY8dKkv7nf/5Hd955pwoKCvQf//EfkqTc3Nw4/tZnZgwcIMe/3qrAo/Nl/HK+/I/8XI6ZM+QYN9bq0AAAAGzBNKVl67L02RH3aT8xJEkb9qVJpnRNSX38gwMAAEnFFsWzP//5z1q+fLleeuklTZ48WZJ02WWXaf369dq9e7dKSkokSf/7v/+rF198US+//LIuuugiSdKUKVN09OhRPfzww5o/f766du3asvh/c5GsWfPjzUW1cePGad++fbr++us1ceLEePyqEXPMminzw4/kv+M7Uq+ectx6i9UhAQCAFLJixQqrQ2jX3uNOfXbE0+42G/an6eKhjeqVHYxTVGfH7rkGACDV2eKyzQULFuiqq65qKZw1Gzx4sNLS0jRy5EhJ0q9+9StdeeWVLYWzU7eTpH379kmS1q9fr9zcXPXt27fVdhs2bFB+fr569uwp6eQMteaZbXZiGIaMcWOlqio5pl4uw336WVUAAIDYKSsrszqEdpXuTZMhs4OtTJXuTYtLPOfC7rkGACDVWT7zbO/evdqyZYu+853vtPnZ/v37NXr0aLndbu3cuVN79uzRvffe22a75qJZfn6+pFCR7PRZZ82Pn3op5/r16yW1naEWiUgv73T+/BE5isd1vOFpzJ07FVz8ZzluuF7BPy6WY8pkGb17R/z8d959V7mfm9Lp9wUAAMnvTP2p082fP7/D7ebPnx+tkDptxveXa2DJVXI4nGG3CQYC+u0zS3XDb74ex8haS4ZcAwCQ6iyfeXbgwAFJUp8+fVo97vV69cEHH7TMCjt69KikkwWyU/3jH//Q2LFj1b17d0nSpk2b2qx3Vl1drXXr1rUqlK1fv159+vRRXl5e9H6hKDCbfKF1zq6dJec3vyFj8kUK/PwxmUF7X3IAAAAQL411FVIHM88MwzixHQAAwNmzfOZZ8yWUn332maZPn97y+BNPPKEDBw60FM/69+8vSdqxY0er569cuVJvvfWWHn/8cUlSTU2NqqurVVBQ0Gq73/zmN/J6va2Kalu2bNGIESPOKu6Kisg6Ytd8+oneq63p1GsHFz0tw+WS45abJEnOu+6U/47vKPj8i3Je/5WIXuPiyZP1YoQxAgCA1FJeXt7hNvPnz9ett97a7jY/+tGPohVSp2074tKf1rTflTUcTv3PT76hvgusm3mWDLkGACDZzJgxo1PbW148GzhwoEaPHq3HHntMXbp00YABA/TGG29o6dKlkqTzzjtPUmhm2pVXXqn58+crJydHAwcO1HvvvaeFCxfqtttu0w033CBJysnJUX5+vpYvX64vfelLMk1TixcvbrkT56nFs27duun999/XypUr1a1bN/Xv37/NDLh4C64rVfC11+X61UIZrtCfx8jMlPPf7lPggblyTDhfxqBBlsYIAACS39y5c60OoV1DevqV38Wvg9VOmSfusNmaqUE9/CrsGoh7bJ1l91wDAJDqjIqKio5WWo25bdu26Qc/+IHWrl2rrl27atasWcrJydFvf/tbbdmyRQ5H6OrS48eP68c//rFWrlyp6upqjRgxQv/6r/+qm266qdXrrVq1Svfff792796tfv366cYbb1RNTY0WLVqknTt3tmy3detWffe739XGjRvV2NioP/zhD/rSl74U1d/tbGaeRcPnsnP04sjRcX9fAABgf5HMhopE85UBVqn1GvrTmmwdrHbJkClTkiHJlKF+3X26cUKd0t3WdnWTJdcAACST5plnL7/8ckTb26J4drojR45o6tSpuv766xN+ijrFMwAAYDeRFHRKSkpUWlra7jZ2KOgETemzw25t3O9RfZOh7LSgigubNLinX8aZJqTFWTLlGgCAZNHZ4pnll22uXbtWf/3rXzVx4kRlZGRo8+bNevzxx9W9e/eI7k4EAACA1OUwpBF9fBrRx2d1KAAAIElZXjyrr6/XypUr9eSTT8rr9ap///667rrrdO+99yo7O9vq8AAAAAAAAJDCLC+eTZkyRW+99ZbVYQAAAOAUl1xyidUhpAxyDQCAvTmsDgAAAAD2s3DhQqtDSBnkGgAAe6N4BgAAgDbuvvtuq0NIGeQaAAB7o3gGAACANlatWmV1CCmDXAMAYG8UzwAAAAAAAIAwKJ4BAAAAAAAAYVA8AwAAQBulpaVWh5AyyDUAAPbmsjqAZDc6Myul3hcAACSHZcuW6brrrrM6jJRArgEAsDeKZzE2r/9Aq0MAAADotHnz5lHQiRNyDQCAvXHZJgAAAAAAABAGxTMAAAAAAAAgDIpnAAAAaGPBggVWh5AyyDUAAPZG8QwAAABtFBUVWR1CyiDXAADYG8UzAAAAtHHFFVdYHULKINcAANgbxTMAAAAAAAAgDJfVAQAAACC++vfv3+E2999/f0TboX3kGgCAxGdUVFSYVgcBAAAAAAAAxMOMGTMkSS+//HJE23PZJgAAAAAAABAGxTMAAAAAAAAgDIpnAAAAAAAAQBgUzwAAAAAAAIAwKJ4BAAAAAAAAYVA8AwAAAAAAAMKgeAYAAAAAAACEQfEsyvbu3atZs2bpggsu0EUXXaSHHnrI6pAAAAAAAABwliieRZnL5dKPf/xjffDBB3rrrbe0evVqvfbaa1aHBQAAAAAAgLPgsjqAZJOXl6e8vDxJksfj0bhx47R3716LowIAAAAAAMDZYOZZDFVWVurVV1/VZZddZnUoAAAAAAAAOAsUz2KkqalJt912m+644w4NHz7c6nAAAAAAAABwFiiexUAgENC3vvUtjRkzRnPmzLE6HAAAAAAAAJwlimcxcM899yg7O1vz5s2zOhQAAAAAAACcg5Qtnh04cEC33367Bg0apH79+ukrX/mKysrKzvl1V69erT/96U8qLS3VpZdeqksuuURPPPFEFCIGAAAAAABAvKXk3TYbGho0a9YsBYNB/eIXv1BmZqYee+wxzZgxQ2+99ZYKCwvP+rUvvPBCVVRUhP351l37NLCwjzzulEw9AAAAAABAQknJmWd/+MMftG3bNj3zzDOaOXOmpk+friVLlsjv92v+/Pkxe98jFVV6ccV7+nD9lpi9BwAAAAAAAKInaYtnmzdv1te//nUNHTpU+fn5mjBhQssaZH/96181btw4FRUVtWzfrVs3feELX9Crr74as5je+7hMHrdL548ZGrP3AAAAAAAAQPQk5bWDpaWluvrqq1VYWKif/OQn6tevn3bv3q0PP/xQklRWVqbLL7+8zfOKior03HPPqaKiQrm5uVGN6UhFlT7dsUcXloxUZnpaVF8bAAAAAAAAsZGUxbMf/ehHyszM1MqVK9WlS5eWx2+55RZJUmVlpbp27drmed26dWv5eUfFs589ufSsYltd+qlWl356Vs8FAAAAAADAuSnff6RT2yfdZZv19fV6//33de2117YqnAEAAAAAAACdlXQzz44fP65gMKiCgoKw23Tv3l1VVVVnfG7zzwEAAAAAAJB8ehf07dT2SVc869atmxwOhw4cOBB2m5EjR+rTT9teOvnpp5+qT58+UV/vDAAAAAAAAPYwbcYNndreqKioMGMUi2W+/OUva8uWLVqzZs0ZL9184okn9B//8R967733NGLECElSVVWVSkpKdO211+r//b//F7VYXnpztXbsOaA7/uUqbhQAAAAAAACQYJKyeNZ8t82+ffvq7rvvVr9+/bR37169//77Wrhwoerr63XppZfKMAzNnTtXGRkZmj9/vrZs2aK33npLfft2bvpeOEcqqrRo2QpdWDJSl04aG5XXBAAAAAAAQPwk3Q0DJKmkpERvvPGGhgwZogcffFA33HCDHnvsMeXl5UmSMjMz9dJLL2nMmDG6++679Y1vfEPp6el6+eWXo1Y4k0J31vS4XZo4bnjUXhMAAAAAAADxk5Qzz+yiurZeh48d19AB4W9eAAAAAAAAAPuieAYAAAAAAACEkZSXbQIAAAAAAADRQPEMAAAAAAAACIPiGQAAAAAAABAGxTMAAAAAAAAgDIpnAAAAAAAAQBgUzwAAAAAAAIAwKJ4BAAAAAAAAYVA8AwAAAAAAAMKgeAYAAAAAAACEQfEMAAAAAAAACIPiGQAAAAAAABAGxTMAAAAAAAAgDIpnAAAAAAAAQBgUzwAAAAAAAIAwKJ4BAAAAAAAAYVA8AwAAAAAAAMKgeAYAAAAAAACEQfEMAAAAAAAACIPiGQAAAAAAABAGxTMAAAAAAAAgDIpnAAAAAAAAQBgUzwAAAAAAAIAwKJ4BAAAAAAAAYVA8AwAAAAAAAMKgeAYAAAAAAACEQfEMAAAAAAAACIPiGQAAAAAAABAGxTMAAAAAAAAgDIpnAAAAAAAAQBgUzwAAAAAAAIAwKJ4BAAAAAAAAYVA8AwAAAAAAAMKgeAYAAAAAAACEQfEMAAAAAAAACIPiGQAAAAAAABAGxTMAAAAAAAAgDIpnAAAAAAAAQBgUzwAAAAAAAIAwKJ4BAAAAAAAAYVA8AwAAAAAAAMKgeAYAAAAAAACEQfEMAAAAAAAACIPiGQAAAAAAABAGxTMAAAAAAAAgDIpnAAAAAAAAQBgUzwAAAAAAAIAwKJ4BAAAAAAAAYVA8AwAAAAAAAMKgeAYAAAAAAACEQfEMAAAAAAAACIPiGQAAAAAAABAGxTMAAAAAAAAgDIpnAAAAAAAAQBgUzwAAAAAAAIAwKJ4BAAAAAAAAYVA8AwAAAAAAAMKgeAYAAAAAAACEQfEMAAAAAAAACIPiGQAAAAAAABAGxTMAAAAAAAAgDIpnAAAAAAAAQBguqwNAYikvL+9wm8cff1yzZ89ud5v+/ftHKyQAwBmwvwYAAACig5lniLonnnjC6hAAABFgfw0AAAB0jOIZAAAAAAAAEAbFMwAAAAAAACAMimeIusWLF1sdAgAgAuyvAQAAgI5RPAMAAAAAAADCoHiGqLvxxhutDgEAEAH21wAAAEDHXFYHAETL65szdLDaGff3zesS0BdGNZzVc+eW79In9XVRjigyozOzNK//wLN6biLmOlElYq4TsV1blWcp9dr1Aw88oE2bNlny3mPGjNHDDz9syXsDAAAgcVE8Q9I4WO3U7gq31WF0yif1dXqvtsbqMDotEXOdqBIx14nYrhMxz4lq06ZNevfdd60OAwAAAIgYl20i6u644w6rQwAARID9NQAAANAximeIutmzZ1sdAgAgAuyvAQAAgI5RPEPUTZ8+3eoQgHNmmlJFnUOfHHBrzW6P1pZ79Nlhl+q8htWhAVHD/hoAAADoGGueIeqOHDlidQjAWatuMPRReZo+3pOmuqYzn1/I6+LXhP5ejStsktuaNeaBqGB/DQAAAHSM4hkAKDTT7INdafrblgz5g+3NLjN1sNqlVza59M72dM0cV6+BPfxxixMAAAAAEF9ctomoKyoqsjoEoFN8AenPa7P0RllmB4UzSTr58+MNTv3+gxy9tyMttgECMcL+GgAAAOgYxTNE3ZIlS6wOAYhY0JSWfpylrYc9ksyzeo2Vn2Zq9U4KaEg8yby/pjAIAACAaEno4tnmzZuVm5urlStXRvyct956S3fddZcmTJigwsJCFRcXa/bs2dq9e3cMI00tDz30kNUhABFbvTNNnx1pLpydedbZL24K/WvPyk8zdKCKBdCQWOy+v87IyNA3vvENLVmyRFu3blVjY6O8Xq/27dunl19+Wffff7/69OnT5nk/+MEPtGnTJn3961+3IGoAAAAkm4QunuXl5emNN97QpZdeGvFznn76aR04cEDf/e539dxzz+nBBx/U2rVr9fnPf17l5eUxjDZ1vPDCC1aHAESkst6hv2/NOPHdud1FM2gaWr4xU+bZTV4DLGHX/bXL5dLcuXO1f/9+LVq0SF/72tc0bNgwpaWlyePxqKCgQFdffbV+9rOfac+ePVq0aJFyc3MlhQpnjz76qBwOh9xut8W/CQAAAJJBQhfPcnNzNXHiRHk8noif8+ijj+rFF1/UrbfeqsmTJ+urX/2qli1bpqqqKi1atCiG0cJuls27TB/+ZV7Ej9uB6fPJd+ccBZ54qtXjgRf/It/Nt8qsrbUosvbZNdcf7k5ToMM1ziJ3sNqlncesvQ+LXXPdHto1TjVkyBB9+OGH+s///E9169ZN7733nu644w4VFxcrMzNTaWlpGjp0qL72ta/phRdekMPh0De+8Q198skneuKJJ/Too49Kkr75zW/qt7/9rcW/DQAAAJJBQhfPpk2bpttuu63l+8svv1y33367li9frmnTpqmwsFAlJSWtzqz37Nmzzev0799fPXr00P79++MRNnDWDLdbrn//oYKvvKbgulJJkrlzp4KLfi/n/ffJyM62NsAEEghKpXsjL7xHam05a591Fu0azYYPH6533nlH5513nnbs2KHPf/7zmjx5sp588klt2LBBDQ0Nampq0vbt2/Xss8/qK1/5ikaMGKG33npLeXl5+va3vy0pVDjjhBgAAACiJWGLZ4FAQGVlZSouLpYk+f1+lZWVae3atXrqqac0Z84cPfPMM+rZs6fmzJmj6urqsK+1efNmHT16VCNHjoxX+EltxYoVVoeQ1IyBA+T411sVeHS+zIoK+R/5uRwzZ8gxbqzVoSWUwzVONfqivwvcXeHi0s2zQLu2hp3215mZmXr11VeVl5env/3tbyouLtY///nPDp+3fft2vfLKKy3f+3w+vf/++zGMFAAAAKkmYYtnW7duVUNDg8aODQ2stmzZIq/Xq8GDB+ull17SrFmzNHXqVP3gBz9QY2Oj9u7d+//bu/s4q+s6b/yvGcAbxEQ0BVQys3QcbkZRrEg0E6xW8ybIVjFs11YwRPEy0yu89orFVauLuWA3S9qHbuZiBeIKVt70202SUovkLtHSNMRbFLxBDYGZ3x9cTIwwnoMe+M4Zns/Ho8fjzHe+3/N9ndOZ9+BrPt9ztng/a9euzcUXX5y99tqr1So23rmlS5cWHaHDqz31lNT0OSDrzvty0qlTakedXXSkqvPsK9vmzf1fe7M2r66p3KWgOxKv6+2vPc3rq6++OgcffHAWLlyYz3zmM1ld5uW6/+N//I9885vfTJLMmzcvXbp0yb//+7+nUycf4AEAQGUU++Y878KiRYuSJP3792/19eWXX57a2r92gqtWrUqy4cMF3qq5uTkXXnhhHnzwwdx8880tbzZcjq3ZtyMZP358yX0aGxtL7tfY2FipSC0++7X/zv51x23VMQ/cdmXm//Rbrbat/cvq9Ol7Qtn3MW/evFz86Y9v1Xk36vTNq1M7oP9WH1dTU5Oa/v3SPP93qf3851LzDt4U+95589Ljo8ds9XFJdT7Xb3XkyV/N4DOubrWt1CdqtvX9i/7jLff94ePywrKF7yLdX1Xjc12Nr+t38jwnxT/Xb6c9z+u3et/73pcvf/nLWbt2bUaNGpXXX3+9rOM2fjhAsuFSzRkzZmTJkiUZNGhQhg8fnh/96Eet9p83b94O+/sbAIDNrVy5sqz9qro869WrV/bZZ5+Wr7t3754jjzyy1X6LFy9O7969t/iP5UsvvTQ//vGPM23atBx//PHbJTfty6BTvpZBp05otW3mpOOKCbMVmh9/PE3Tf5jaM0ak6abpqT1mcGr+389Ce9XenuvmbXltZXPTtrvvMrS357pcXtc7rtGjR6e2tjb/8R//kYULyyue31qcbXyPs6uuuirf+c53cv75529WngEAwDtRteXZ4sWLWy7Z3Pj1xvc/29TChQu3uH3ChAm5/vrrM2XKlJx++ulbff7BgwcnSebMmbPVx1azZcuWldynsbExo0aNett9rrjiikpFavHv93XLn8srjStq8ODB+V6ZbfVbnfbw7/Or1a9u1THNb67d8H5Qp5+aTl8cleZVq7L+m5PT6Zp/Tk1t+Vdif2zw4Nz6DnNX43P9Voue2im3vuW/0d+6gmyjjSvO2vr+XzUnqcmC38xN150qU85V43Ndja/rop7npLKv602113l98sknZ968ea22nXbaaUmSadOmlXUfbRVnSXLTTTfl//yf/5MhQ4Zkr732yosvvtjyvcGDB+9wv7cBAHj3qvY9zzYtz5qbm7N48eI0NDS02qepqSlLlixpVbIlyaRJk3Lttdfm6quvzsiRI7dX5B3GhAkTSu/EO9Z0/Q2p6dw5tWdvaHQ6nT86zc8+l6Zbbi04WXXptce6bXCvNdlj1/UVK852JF7XxWgP83r33XfPIYcckjVr1uT+++8vuf/bFWdJsnr16vzud79LkhxxxBGVDwwAwA6nKsuzJ554Ii+//HLLirInnngir7766mbl2aOPPprVq1e3Wnk2derUTJ48OaeddloaGhrym9/8puV/Dz/88PZ8GB3W8OHDi47QYTU9uCBNP70jnS67NDWdNywcrenaNZ2+ekmabrwpzY8/XnDC6rHXbk15zy6Vv7zy/Xtti1KuY/O6Lk57mNcHH3xwkg0f/LN27dq33bdUcbbRkiVLkiSHHHJIBZMCALCjqsrLNtv6sIC3lmcb3zdl435JcvfddydJbr311tx6a+sVDS7nqIyGhoYsWLCg6BglDZ/wi63a3h7UHt6Q2tmzNt/etz61c9rvCp32+FzX1iQD+6zJf/9h1wrd44ZLNo9635oK3d870x6f61K8rovTHub1n//853z+85/Pq6++/aW+++67b8slpG9XnCXJ9ddfn7lz5+a3v/1tRbMCALBjqsry7DOf+UyrT0Q45ZRTtvgJCSNGjMiIESNabVOOARsNPGBN7nt857yxthKLcGvygb3Xpvce6ytwX7DjWLlyZVlv7P/cc89l2LBhqaury/e///233XfjinIAAKiEqrxsE6ASdtu5OZ+qf/3/ffXu3qdsp07NObnfa+8+FNCmBx54oGRxBgAAlVaVK89o34YMGVJ0BChb315rs3zVX/LAn3fJxksv36rUp2zW1DTntIbXsseuPiiA6mJeAwBAacozKm7q1KlFR4Cy1dQknzzsjXSqTX79+C5bceSGoq1Lp+acPuC1HLrv27/RObRH5jUAAJTmsk0qbty4cUVHgK1SU5MMq3sjI496dSs+gbMmB/ZYmzHHvJJDeyrOqE7mNQAAlGblGRU3d+7coiPAO/KB967LBce+nIee3Sm/e3KnPPVS56xran0Z565dmvKBvdflyPetSZ8916Vm86s8oWqY1wAAUJryDGATnTsl/fd7M/33ezPrm5IXX6vNd365R5LkwuNezh67NinMAAAAdiDKM4A2dKpN9tn9r5dxdu9a7iWdAAAAdBTe84yKW7BgQdERACiDeQ0AAKUpz6i4mTNnFh0BgDKY1wAAUJrLNqm4SZMmZfjw4dv9vD3fs367n/Pdnre+624VTLL9zl2Nz3W1qsbnuhpf10W+too8dxHzum/fvu/ouGVPr0iS9On93la3t8e5AQDYsSnP6DA+edgbRUfYapP6HFh0hHekGp/ralWNz3U1vq6r8XmuVlddddU7Ou6aaTOSJF/9hxGtbgMAwLbmsk0AAAAAaIPyjIqbMmVK0REAKIN5DQAApSnPqLi6urqiIwBQBvMaAABKU55RccOGDSs6AgBlMK8BAKA05RkAAAAAtEF5BgAAAABtUJ5RcQMHDiw6AgBlMK8BAKA05RkVN3/+/KIjAFAG8xoAAEpTngEAAABAG5RnAAAAANAG5RkVN3369KIjAFAG8xoAAEpTngEAAABAG5RnVNyZZ55ZdAQAymBeAwBAaZ2LDtDRTVj2RH7/+mvb/bz1XXfLpD4HbvfzFumOh3bNs6902u7n7fme9fnkYW+8o2OLen0kO+ZrpBpV4wwp6mcxeXc/j/B2Lr/88ixZsmS7n7dv37656qqrtvt5AQD4K+XZNvb711/Lr1a/WnSMHcKzr3TKn1d2KTrGVvH6oJRqfI1U488ilLJkyZLMmzev6BgAABTAZZtU3HnnnVd0BADKYF4DAEBpyjMqbsyYMUVHAKAM5jUAAJSmPKPihg4dWnQEAMpgXgMAQGnKMypuxYoVRUcAoAzmNQAAlKY8AwAAAIA2KM+ouLq6uqIjAFAG8xoAAErrXHQAOp6bb7656AgAlMG83nb222+/HHXUUenbt2922223/OUvf8nDDz+c3/72t3nssce2eMzVV1+d3//+9/nBD36wndMCAPB2qnrl2UMPPZQePXrk7rvvLvuYX/ziFzn11FNTV1eXnj17pq6uLp///OfzwAMPbMOkO5aJEycWHQGAMpjXlVVTU5MRI0bkF7/4RZYvX55bb701//RP/5TLLrss//t//+/88Ic/zKOPPpr58+fn7//+79OlS5eWY7/xjW/kq1/9aqZNm5bevXsX+CgAAHirqi7PevbsmTvvvDPHHnts2cesXLky9fX1ueqqq3LLLbfkyiuvzIsvvpiTTjop999//zZMW57mtWuzdvTYrL/ue622r7/1P7N25Kg0r15dULLyzZo1q+gIZZk56bg88J+Tyt7eHnSE10c1efrlTpm9qGvL17/4wy555S81BSZ6e9X6+qjGn8WOolrmdTU48MAD8/Of/zw//vGPc+yxx2b16tW566678o1vfCOXX355rrzyysyZMycvvvhijjjiiPzbv/1bHnjggQwYMCDf+MY38pWvfCVr167NGWeckaeffrrohwMAwCaq+rLNHj16pEePHlt1zOmnn57TTz+91bYTTjghH/rQhzJ9+vQcffTRlYy41Wq6dEnny76SdRdclJpBR6X28IY0P/54mq7/fjpdOTE13boVmo9ieX1sH83NyZ1Ld839T+ySmjS3bL/n0V1y72O7ZPjhr+XQnmsLTLhlXh9QjKOPPjo/+9nPsueee+b555/P17/+9dx4441ZvYXCeuedd86IESPy9a9/PQ0NDfntb3+bzp07Z+3atRk+fHhmz55dwCMAAODtVPXKsxNOOCHnnHNOy9fHH398zj333MyePTsnnHBC9ttvvzQ0NJT8y3q3bt2y0047tbp8okg1B74vtX83Kuu/1ZjmlSuz7upvpvaUk1Pbv1/R0WgHvD62vfue2Dn3P7FLkqQ5m640q8n65mTGg7vl2Vc6FROuBK8P2L4OO+yw3HHHHdlzzz1z++23p76+Ptdee+0Wi7MkWbNmTW666ab0798/CxcuTOfOndPc3JyJEycqzgAA2qmqLc/Wr1+fpUuXZsCAAUmSdevWZenSpZk/f36+973vZezYsbnxxhuz9957Z+zYsXnllVc2O37t2rV58skn85WvfCVJ8sUvfnG7P4621J56Smr6HJB153056dQptaPOLjpS2e66666iI3R41fz6aO/WNyXzHtsl2WTFWWs1aW5O7nt85+0Za6t4fVAu8/rd6dy5c37wgx+ke/fumTlzZk499dS88MILZR37j//4jxkwYEDWr1+fmpqajB49Onvsscc2TgwAwDtRtZdt/uEPf8gbb7yRfv02rKZ45JFHsmbNmhx00EGZMWNGams39IJvvvlmzjrrrCxfvjyHHXZYy/GbvsfZPvvskx/96Eepr68v+/zz5s1LkpKXjXb65tWpHdB/qx5bsuFNh2v690vz/N+l9vOfS81Wroq7d9689PjoMVt93lLGjx9fcp/HHnssH/jAB952n8bGxkpFavHZr/139q87bquOeeC2KzP/p99qtW3tX1anT98Tyr6PefPm5eJPf3yrzrtRUa+PZNu9RqrdfnXHZvjXfvG2+zSnJvOfaM7ffXzrLht/J97Ja6To10dRP4vJu/t57Gja87x+J7569XVJNvze3fR2kS688MIcccQRefzxx3POOedk/fr1ZR236Xucfe5zn8ull16aj3zkI7nyyiszduzYVvvOmzev8McJANBRrVy5sqz9qnbl2aJFi5Ik/fv3b/X15Zdf3lKcJcmqVauSbPhwgU1NnTo1d999d2688cYMGDAgZ5xxRu69997tEb0szY8/nqbpP0ztGSPSdNP0ND//fNGRylZNl50MOuVrGTPtpVb/6/2hjxUdq6Rqfn20dzvvWt7Kj85ddkmnzjtt4zTvTDW+Pqr1Z7HaVdO8bm9qa2szbty4JMkFF1yQ1157razjNi3Ohg8fnv/8z//Ml770pSQbVsBbfQYA0P5U7cqzRYsWpVevXtlnn31avu7evXuOPPLIVvstXrw4vXv33uyvth/84Adbbn/605/OsGHDcvnll+eXv/xlWecfPHhwkmTOnDlvu99pD/8+v1r9aln3uVHzm2s3vE/R6aem0xdHpXnVqqz/5uR0uuafU1NbXt/5scGDc2uZDerWWLZsWcl9GhsbM2rUqLfd54orrqhUpBb/fl+3/LnyD7mkwYMH53vv8Lku6vWRbLvXSLV75uVOmTav1F7N2bVLc1Y8/+w2z7O1r5H28Poo6mcxeXc/jx1Ne57X78Q102Yk2fDXwU1vby8nn3xyy6rzJBk6dGj69OmTP/7xj/npT39a1n28tTjbWF7+/ve/z913352hQ4fmb//2b/Pd73635ZjBgweX/LcGAADbVtWuPFu8eHHLJZsbv974/mebWrhw4Ra3b6q2tjaHH354HnvssYrnfCearr8hNZ07p/bss5Iknc4fneZnn0vTLbcWnIz2wOtj2+r5nvXZd/d1rT5lc0uOOGDNdkq0dbw+YPv46Ec/miSZNWtWmpvffl4kbRdnG91yyy2t7hcAgPajQ5Rnzc3NWbx4cRoaGlrt09TUlCVLlrQq2bZk7dq1ue+++/L+979/W8UtW9ODC9L00zvS6bJLU9N5w8LAmq5d0+mrl6TpxpvS/PjjBScsbcKECUVH6LA6wuujvaupSU449I3/99Xm/0Fck+Z027k5H35/+yvPvD7YWub1O3f44YcnSX73u9+V3LdUcZYk8+fPb3W/AAC0H1V52eYTTzyRl19+uWVF2RNPPJFXX311s/Ls0UcfzerVq1utPBs5cmT69u2bvn37Zs8998zy5cvz/e9/P0uXLs2NN964PR/GFtUe3pDa2bM23963PrVzqmPlyPDhw4uOUJbhE36xVdvbg47w+qgGB793XUYc8VpmL+6av6ytyV9LtJq8d/f1+dwRr6XbzqVXmmxv1fr6qMafxY6iWuZ1e/T0009nwYIFJVetf/3rXy9ZnCXJk08+mYULF+bJJ5/cFnEBAHgXqrI8a+vDAt5ani1cuLDVfkkyaNCg3HbbbZk2bVpWr16dPffcM0cddVRuv/32fPjDH94O6Tu+hoaGLFiwoOgY8K7U9VybD7735Sx9tkuee7Vzamub84G916XPnutSU1N0OqgM8/qdGz16dFn7/eQnP8mYMWNy7rnnvu0HNDz33HOb/TsGAID2oSrLs8985jOt3iT4lFNO2eKbBo8YMSIjRoxotW3cuHEtn44F8HY6d0r67bc2/bK26ChAlXrggQdy0EEHZfXq1UVHAQDgHara9zwDAKgGijMAgOqmPKPihgwZUnQEAMpgXgMAQGnKMypu6tSpRUcAoAzmNQAAlKY8o+K8pxxAdTCvAQCgNOUZFTd37tyiIwBQBvMaAABKU54BAAAAQBuUZwAAAADQBuUZFbdgwYKiIwBQBvMaAABK61x0gI6uvutuO9R5k2TmzJkZPnz4dj9vz/es3+7nfLfnLfL/pyLPTfmqcYYU9bNY9LmrUVHzuhr17dt3q49Z9vSKJEmf3u9tdXtbnxcAgMpSnm1jk/ocWHSE7W7SpEmF/MfYJw97Y7uf893aEV8fbJ1qfI1U48/ijqqoeV2Nrrrqqq0+5pppM5IkX/2HEa1uAwBQXVy2CQAAAABtUJ4BAAAAQBuUZ1TclClTio4AQBnMawAAKE15RsXV1dUVHQGAMpjXAABQmvKMihs2bFjREQAog3kNAAClKc8AAAAAoA3KMypu4MCBRUcAoAzmNQAAlKY8o+Lmz59fdAQAymBeAwBAacozAAAAAGiD8gwAAAAA2qA8o+KmT59edAQAymBeAwBAacozAAAAAGiD8oyKO/PMM4uOAEAZzGsAAChNeQYAAAAAbVCeAQAAAEAblGdU3HnnnVd0BADKYF4DAEBpyjMqbsyYMUVHAKAM5jUAAJSmPKPihg4dWnQEAMpgXgMAQGnKMypuxYoVRUcAoAzmNQAAlKY8AwAAAIA2KM+ouLq6uqIjAFAG8xoAAErbIcuze+65J+eff36OPPLI7LfffhkwYEDGjBmTP//5z0VH6xBuvvnmoiMAUAbzGgAAStshy7MbbrghzzzzTC644IL8+Mc/zte+9rXMnz8/H//4x7Ns2bKi41W9iRMnFh0BgDKY1wAAUNoOWZ5961vfyq233ppRo0Zl8ODB+dznPpeZM2fm5ZdfzvXXX190vKo3a9asoiMAUAbzGgAASutw5VlTU1P+5V/+JUceeWR69eqVY445JvPmzcugQYNy0UUXJUn23nvvzY7r06dP9tprrzz99NPbOTEAAAAA7VXnogNU2gUXXJDbb789l1xySRoaGvLAAw/kS1/6Ul544YWcf/75bR730EMP5YUXXsihhx66HdMCAAAA0J51qPLslltuyc0335w5c+Zk8ODBSZJjjjkmCxcuzO23357+/ftv8bi1a9fm4osvzl577ZVzzjlnOybumO66666iIwBQBvMaAABK61CXbU6ePDmf+MQnWoqzjQ466KB06dIl9fX1mx3T3NycCy+8MA8++GCuu+669OjRY3vF7bCWLl1adAQAymBeAwBAaR1m5dlTTz2VpUuXZsyYMZt9b/ny5Tn00EOz8847b/a9Sy+9ND/+8Y8zbdq0HH/88WWfb968eUmyw5Vt48ePL7lPY2Njyf0aGxsrFQmALeho8/qrV1+XZMPv3U1vt2fVmBkAYEeycuXKsvbrMCvPNr7R/7777ttq+xtvvJFf/epXW7xkc8KECbn++uvzf//v/83pp5++XXICAAAAUD06zMqzvfbaK0ny6KOPZujQoS3bp06dmmeffTYNDQ2t9p80aVKuvfbaXHPNNRk5cuRWn2/jpaFz5sx556Gr0LJly0ru09jYmFGjRr3tPldccUWlIgGwBR1tXl8zbUaSDX8d3PR2e1aNmQEA2FyHKc8OPPDA1NfXp7GxMXvuuWd69eqV2bNn5+67706SDBgwoGXfqVOnZvLkyTnttNPS0NCQ3/zmNy3f23333X3i5rs0YcKEoiMAUAbzGgAASusw5VltbW1uuOGGXHzxxbnkkkuy55575vOf/3zOPffc/NM//VOrDwvYWKjdeuutufXWW1vdz+DBg3e41WSVNnz48KIjAFAG8xoAAErrMOVZkhx88MGZPXt2q22jR4/OIYcckl133bVlm3Js22poaMiCBQuKjgFACeY1AACU1mE+MKAtDz74YKtLNgEAAACgXB26PFu9enUee+wx5RkAAAAA70iHumzzrbp165YXXnih6Bg7nCFDhhQdAYAymNcAAFBah155RjGmTp1adAQAymBeAwBAacozKm7cuHFFRwCgDOY1AACUpjyj4ubOnVt0BADKYF4DAEBpyjMAAAAAaIPyDAAAAADaoDyj4hYsWFB0BADKYF4DAEBpyjMqbubMmUVHAKAM5jUAAJSmPKPiJk2aVHQEAMpgXgMAQGnKMwAAAABog/IMAAAAANqgPKPipkyZUnQEAMpgXgMAQGnKMyqurq6u6AgAlMG8BgCA0pRnVNywYcOKjgBAGcxrAAAoTXkGAAAAAG1QngEAAABAG5RnVNzAgQOLjgBAGcxrAAAoTXlGxc2fP7/oCACUwbwGAIDSlGcAAAAA0AblGQAAAAC0QXlGxU2fPr3oCACUwbwGAIDSlGcAAAAA0AblGRV35plnFh0BgDKY1wAAUJryDAAAAADaoDwDAAAAgDYoz6i48847r+gIAJTBvAYAgNKUZ1TcmDFjio4AQBnMawAAKE15RsUNHTq06AgAlMG8BgCA0pRnVNyKFSuKjgBAGcxrAAAoTXkGAAAAAG1QnlFxdXV1RUcAoAzmNQAAlKY8o+JuvvnmoiMAUAbzGgAASqu68uzee+/NyJEj07dv3/Tq1Sv19fUZN25cXnnllZZ9jj/++Jx77rmZPXt2TjjhhOy3335paGjIrFmzCky+45g4cWLREQAog3kNAAClVV15tmTJkhx99NGZPHlybrnllnzlK1/Jz372s0yYMCFJsm7duixdujTz58/P9773vYwdOzY33nhj9t5774wdO7ZVyca2oaQEqA7mNQAAlNa56ABba/To0S23m5qaMmjQoCxZsiRz585NkjzyyCNZs2ZNDjrooMyYMSO1tRv6wTfffDNnnXVWli9fnsMOO6yQ7AAAAABUl6oqz9avX59Zs2bl+uuvz5/+9KesWLGi5XsDBgxIkixatChJcvnll7cUZ0myatWqJEnPnj1bti1btiznn39+nnvuuey000751re+lY985CPb46EAAAAAUAWqqjw7//zzc/vtt+fcc8/NhRdemB49eqRz58459dRTW1aTLVq0KN27d8+RRx7Z6tjFixend+/e6dGjR8u28ePH57TTTsvf//3f5/7778/f/d3f5cEHH8xOO+20XR9XR3PXXXcVHQGAMpjXAABQWtWUZw899FBmzJiRKVOm5Oyzz27Zft9992X16tUtK88WL17ccntTCxcubLX9xRdfzP3335/p06cnSY4++uj07Nkzv/zlL/OJT3yiZJ558+YlSasybkcwfvz4kvs89thj+cAHPvC2+zQ2NlYqEgBb0NHm9Vevvi7Jht+7m95uz6oxMwDAjmTlypVl7Vc1Hxjw1FNPJUk++MEPtmxbu3ZtywcFDBgwIM3NzVm8eHEaGhpaHdvU1JQlS5akX79+LduWL1+e9773vdl5551btvXp0ydPPvnkNnwUO4bZs2cXHQGAMpjXAABQWtWsPKuvr88uu+ySiRMn5pJLLsmqVaty7bXX5qWXXkqnTp3Sr1+/PPHEE3n11Vc3K88effTRVqvTKmHw4MFJkjlz5lTsPqvBsmXLSu7T2NiYUaNGve0+V1xxRaUiAbAFHW1eXzNtRpINfx3c9HZ7Vo2ZAQDYXNWsPOvdu3emTZuW559/PiNHjsy3v/3tjB07NoMGDcrBBx+crl27tnxYwFvLs4ULFyZJ+vfv37Jt//33z4oVK7JmzZqWbcuWLcsBBxyw7R8MAAAAAFWhalaeJclJJ52Uk046qdW20047reX2KaecssW/6I4YMSIjRoxotW2vvfbKoEGDctNNN7V8YMAzzzyTY445ZtuE34FsvJQWgPbNvAYAgNKqqjyrtMmTJ+fLX/5yvvvd76ZLly753ve+55M2K2D48OFFRwCgDOY1AACUtkOXZwceeGB+8pOfFB2jw2loaMiCBQuKjgFACeY1AACUVjXveQYAAAAA25vyDAAAAADaoDyj4oYMGVJ0BADKYF4DAEBpyjMqburUqUVHAKAM5jUAAJSmPKPixo0bV3QEAMpgXgMAQGnKMypu7ty5RUcAoAzmNQAAlKY8AwAAAIA2KM8AAAAAoA3KMypuwYIFRUcAoAzmNQAAlKY8o+JmzpxZdAQAymBeAwBAacozKm7SpElFRwCgDOY1AACUpjwDAAAAgDYozwAAAACgDcozKm7KlClFRwCgDOY1AACUpjyj4urq6oqOAEAZzGsAAChNeUbFDRs2rOgIAJTBvAYAgNKUZwAAAADQBuUZFTdw4MCiIwBQBvMaAABKU55RcfPnzy86AgBlMK8BAKA05RkAAAAAtEF5BgAAAABtUJ5RcdOnTy86AgBlMK8BAKA05RkAAAAAtEF5RsWdeeaZRUcAoAzmNQAAlNa56AAd3YRlT+T3r7+23c9b33W3TOpz4HY/LwAAAEBHojzbxn7/+mv51epXi44BAAAAwDvgsk0q7rzzzis6AgBlMK8BAKA05RkVN2bMmKIjAFAG8xoAAEpTnlFxQ4cOLToCAGUwrwEAoDTlGRW3YsWKoiMAUAbzGgAASlOeAQAAAEAblGdUXF1dXdERACiDeQ0AAKUpz6i4m2++uegIAJTBvAYAgNKUZ1TcxIkTi44AQBnMawAAKK0qy7N77703I0eOTN++fdOrV6/U19dn3LhxeeWVV1r2Of7443Puuedm9uzZOeGEE7LffvuloaEhs2bNKjB5ac1r12bt6LFZf933Wm1ff+t/Zu3IUWlevbqgZOVr788xABuY1wAAUFrnogO8E0uWLMnRRx+dL3zhC+nWrVv+8Ic/5Morr0ySTJ06NevWrcvSpUuzatWqPPfccxk7dmx23333XHXVVRk7dmxOOOGEvOc97yn4UWxZTZcu6XzZV7LugotSM+io1B7ekObHH0/T9d9PpysnpqZbt6IjAgAAAOwwqrI8Gz16dMvtpqamDBo0KEuWLMncuXOTJI888kjWrFmTgw46KDNmzEht7YYFdm+++WbOOuusLF++PIcddlgh2ctRc+D7Uvt3o7L+W42p+ZfGrLv6m6k95eTU9u9XdDQAAACAHUrVXba5fv36zJgxI5/61KdyyCGHZO+9984+++yT66+/PrvttluSZNGiRUmSyy+/vKU4S5JVq1YlSXr27Nmy7Z//+Z9z1FFHZa+99sptt922HR/J26s99ZTU9Dkg6877ctKpU2pHnV10pLLdddddRUcAoAzmNQAAlFZ15dn555+fiy66KIMGDcqUKVNyxx135Oc//3m6devWspps0aJF6d69e4488shWxy5evDi9e/dOjx49WrZ94hOfyIwZM/LRj350uz6OUmpqalLTv1/y8sup/cTxqenSpehIZVu6dGnREQAog3kNAAClVdVlmw899FBmzJiRKVOm5Oyz/7oS67777svq1aszYMCAJBtKso23N7Vw4cLNth999NHvKMu8efOSpFURtyWdvnl1agf03+r7b3788TRN/2FqzxiRppump/aYwanZZ5+yj7933rz0+OgxW33eUsaPH19yn8bGxpL7NTY2VioSAFvQ0eb1V6++LsmG37ub3m7PqjEzAMCOZOXKlWXtV1Urz5566qkkyQc/+MGWbWvXrs2ECROSJAMGDEhzc3MWL16choaGVsc2NTVlyZIl6dev/b9vWPObaze8z9npp6bT338xNYM/kvXfnJzmpqaiowEAAADsUKpq5Vl9fX122WWXTJw4MZdccklWrVqVa6+9Ni+99FI6deqUfv365Yknnsirr766WXn26KOPtlqd9m4NHjw4STJnzpy33e+0h3+fX61+davuu+n6G1LTuXNqzz4rSdLp/NFZd96X03TLrek04rNl3cfHBg/OrWU2qFtj2bJlJfdpbGzMqFGj3nafK664olKRANiCjjavr5k2I8mGvw5uers9q8bMAABsrqpWnvXu3TvTpk3L888/n5EjR+bb3/52xo4dm0GDBuXggw9O165dWz4s4K3l2cKFC5Mk/ftv/SWU21PTgwvS9NM70umyS1PTeUO3WdO1azp99ZI03XhTmh9/vOCEpW1cCQhA+2ZeAwBAaVW18ixJTjrppJx00kmttp122mktt0855ZQt/lV3xIgRGTFixDbP927VHt6Q2tmzNt/etz61c24tINHWGz58eNERACiDeQ0AAKVV1cqzbeHKK69MfX19fvOb3+Tiiy9OfX19Hq+C1V3t2VtX/QHQPpnXAABQWtWtPKu0r33ta/na175WdAwAAAAA2qEdfuUZAAAAALRFeUbFDRkypOgIAJTBvAYAgNKUZ1Tc1KlTi44AQBnMawAAKE15RsWNGzeu6AgAlMG8BgCA0pRnVNzcuXOLjgBAGcxrAAAoTXkGAAAAAG1QngEAAABAG5RnVNyCBQuKjgBAGcxrAAAorXPRATq6+q677VDnTZKZM2dm+PDhhZ0fgPKY1wAAUJrybBub1OfAoiNsd5MmTfIfYwBVwLwGAIDSXLYJAAAAAG1QngEAAABAG5RnVNyUKVOKjgBAGcxrAAAoTXlGxdXV1RUdAYAymNcAAFCa8oyKGzZsWNERACiDeQ0AAKUpzwAAAACgDcozKm7gwIFFRwCgDOY1AACUpjyj4ubPn190BADKYF4DAEBpyjMAAAAAaIPyDAAAAADaoDyj4qZPn150BADKYF4DAEBpyjMAAAAAaIPyjIo788wzi44AQBnMawAAKE15BgAAAABtUJ4BAAAAQBuUZ1TceeedV3QEAMpgXgMAQGnKMypuzJgxRUcAoAzmNQAAlKY8o+KGDh1adAQAymBeAwBAacozKm7FihVFRwCgDOY1AACUpjwDAAAAgDYoz6i4urq6oiMAUAbzGgAASlOeUXE333xz0REAKIN5DQAApSnPqLiJEycWHQGAMpjXAABQWlWWZ/fee29GjhyZvn37plevXqmvr8+4cePyyiuvtOxz/PHH59xzz83s2bNzwgknZL/99ktDQ0NmzZpVYPIdg+cYoDqY1wAAUFrnogO8E0uWLMnRRx+dL3zhC+nWrVv+8Ic/5Morr0ySTJ06NevWrcvSpUuzatWqPPfccxk7dmx23333XHXVVRk7dmxOOOGEvOc97yn4UQAAAADQ3lVleTZ69OiW201NTRk0aFCWLFmSuXPnJkkeeeSRrFmzJgcddFBmzJiR2toNC+zefPPNnHXWWVm+fHkOO+ywQrIDAAAAUD2qrjxbv359Zs2aleuvvz5/+tOfsmLFipbvDRgwIEmyaNGiJMnll1/eUpwlyapVq5IkPXv2TJK89NJLGT16dB577LHssssuee9735tvfetbOeigg7bXw+mQ7rrrrqIjAFAG8xoAAEqruvc8O//883PRRRdl0KBBmTJlSu644478/Oc/T7du3VpWky1atCjdu3fPkUce2erYxYsXp3fv3unRo0eSpKamJqNHj85vfvOb/PKXv8yJJ56YsWPHbvfH1NEsXbq06AgAlMG8BgCA0qpq5dlDDz2UGTNmZMqUKTn77LNbtt93331ZvXp1y8qzxYsXt9ze1MKFC1tt32OPPXLccce1fD1o0KBMnTq1rCzz5s1LkpYibkcxfvz4kvs0NjaW3K+xsbFSkQDYgo42r7969XVJNvze3fR2e1aNmQEAdiQrV64sa7+qWnn21FNPJUk++MEPtmxbu3ZtJkyYkGTDZZvNzc1ZvHhxGhoaWh3b1NSUJUuWpF+/fm3e/3e/+938zd/8TeWDAwAAAFCVqmrlWX19fXbZZZdMnDgxl1xySVatWpVrr702L730Ujp16pR+/frliSeeyKuvvrpZefboo4+2Wp32Vtdcc03+9Kc/5bbbbisry+DBg5Mkc+bMeVePqdosW7as5D6NjY0ZNWrU2+5zxRVXVCoSAFvQ0eb1NdNmJNnw18FNb7dn1ZgZAIDNVdXKs969e2fatGl5/vnnM3LkyHz729/O2LFjM2jQoBx88MHp2rVry4cFvLU8W7hwYZKkf//+m93vt771rdx1112ZOXNmunbtus0fR0e3cSUgAO2beQ0AAKVV1cqzJDnppJNy0kkntdp22mmntdw+5ZRTtvhX3REjRmTEiBGbbb/mmmty9913Z9asWdljjz0qH3gHNHz48KIjAFAG8xoAAEqrqpVnlbZ06dJcc801WbVqVU4++eQMGTIkQ4YMKTpW1Xvrqj8A2ifzGgAASqu6lWeVVFdX571HAAAAAGjTDr3yDAAAAADejvKMinPpK0B1MK8BAKA05RkVN3Xq1KIjAFAG8xoAAEpTnlFx48aNKzoCAGUwrwEAoDTlGRU3d+7coiMAUAbzGgAASlOeAQAAAEAblGcAAAAA0AblGRW3YMGCoiMAUAbzGgAASlOeUXEzZ84sOgIAZTCvAQCgNOUZFTdp0qSiIwBQBvMaAABKU54BAAAAQBuUZwAAAADQBuUZFTdlypSiIwBQBvMaAABKU55RcXV1dUVHAKAM5jUAAJSmPKPihg0bVnQEAMpgXgMAQGnKMwAAAABog/IMAAAAANqgPKPiBg4cWHQEAMpgXgMAQGnKMypu/vz5RUcAoAzmNQAAlKY8AwAAAIA2KM8AAAAAoA3KMypu+vTpRUcAoAzmNQAAlKY8AwAAAIA2KM+ouDPPPLPoCACUwbwGAIDSlGcAAAAA0AblGQAAAAC0QXlGxZ133nlFRwCgDOY1AACUpjyj4saMGVN0BADKYF4DAEBpyjMqbujQoUVHAKAM5jUAAJSmPKPiVqxYUXQEAMpgXgMAQGnKMwAAAABog/KMiqurqys6AgBlMK8BAKC0Hb48Gzt2bHr06JFRo0YVHaXDuPnmm4uOAEAZzGsAAChthy7P7rnnntx2223Zfffdi47SoUycOLHoCACUwbwGAIDSdtjy7I033sjFF1+cr3zlK+nevXvRcTqUWbNmFR0BgDKY1wAAUFqHKs+ampryL//yLznyyCPTq1evHHPMMZk3b14GDRqUiy66qNW+V111Vbp27Zrzzz+/mLAAAAAAtHudiw5QSRdccEFuv/32XHLJJWloaMgDDzyQL33pS3nhhRdalWQLFizIddddl9tvvz2dO3eopwAAAACACuowzdEtt9ySm2++OXPmzMngwYOTJMccc0wWLlyY22+/Pf3790+SrFu3LhdeeGHOPvvsHHXUUUVG7rDuuuuuoiMAUAbzGgAASuswl21Onjw5n/jEJ1qKs40OOuigdOnSJfX19UmSf/3Xf82KFSvyv/7X/yoi5g5h6dKlRUcAoAzmNQAAlNYhVp499dRTWbp0acaMGbPZ95YvX55DDz00O++8c5YvX55vfOMbueqqq9Lc3JyXX345yYb3Slu3bl1efvnldO3aNV26dCl5znnz5iVJevToUdkH086NHz++5D6NjY0l92tsbKxUJAC2oKPN669efV2SDb93N73dnlVjZgCAHcnKlSvL2q9DrDx7+umnkyT77rtvq+1vvPFGfvWrX7VcsvnEE0/kL3/5S8aPH5/3v//9Lf976qmn8rOf/Szvf//7c+utt273/AAAAAC0Tx1i5dlee+2VJHn00UczdOjQlu1Tp07Ns88+m4aGhiRJv379Mnv27M2OP/fcc3PwwQfnsssuy4c+9KGyzrnx8tA5c+a8y/TVZdmyZSX3aWxszKhRo952nyuuuKJSkQDYgo42r6+ZNiPJhr8Obnq7PavGzAAAbK5DlGcHHnhg6uvr09jYmD333DO9evXK7Nmzc/fddydJBgwYkCTZY4898rGPfWyz43feeefstddeW/weW2/ChAlFRwCgDOY1AACU1iEu26ytrc0NN9yQQw89NJdccknGjh2bHj165Nxzz02nTp1aPiyA7WP48OFFRwCgDOY1AACU1iFWniXJwQcfvNklmaNHj84hhxySXXfd9W2PXbhw4baMtsNpaGjIggULio4BQAnmNQAAlNYhVp615cEHH2y5ZBMAAAAAtlaHLc9Wr16dxx57THkGAAAAwDvWYS7bfKtu3brlhRdeKDrGDmnIkCFFRwCgDOY1AACU1mFXnlGcqVOnFh0BgDKY1wAAUJryjIobN25c0REAKIN5DQAApSnPqLi5c+cWHQGAMpjXAABQmvIMAAAAANqgPAMAAACANijPqLgFCxYUHQGAMpjXAABQmvKMips5c2bREQAog3kNAAClKc+ouEmTJhUdAYAymNcAAFCa8gwAAAAA2qA8AwAAAIA2KM+ouClTphQdAYAymNcAAFCa8oyKq6urKzoCAGUwrwEAoDTlGRU3bNiwoiMAUAbzGgAASlOeAQAAAEAblGdU3MCBA4uOAEAZzGsAAChNeUbFzZ8/v+gIAJTBvAYAgNKUZwAAAADQBuUZAAAAALRBeUbFTZ8+vegIAJTBvAYAgNKUZwAAAADQBuUZFXfmmWcWHQGAMpjXAABQmvIMAAAAANqgPAMAAACANijPqLjzzjuv6AgAlMG8BgCA0pRnVNyYMWOKjgBAGcxrAAAoTXlGxQ0dOrToCACUwbwGAIDSlGdU3IoVK4qOAEAZzGsAAChNeQYAAAAAbVCeUXF1dXVFRwCgDOY1AACUpjyj4m6++eaiIwBQBvMaAABK2+HLs9dffz1XX311Fi1aVHSUDmPixIlFRwCgDOY1AACUtsOXZ4sXL843vvGNrF69uugoHcasWbOKjgBAGcxrAAAoTXm2eHFqamrSt2/foqMAAAAA0M60m/Js3bp1mTx5co444oj06tUrJ554Yh566KEMHDgwl1xySct+zc3NueGGG3Lcccdlv/32S79+/XL55Ze3Wjm2fv367L///rnmmms2O0evXr0yZcqUJMngwYNz6aWXprm5OQceeGB69OiRE044Yfs8YAAAAADavc5FB9joH/7hH3LXXXdl/PjxGThwYO6///6cddZZefLJJzNgwIAkSVNTU84777zccccdufjii3PkkUfm0Ucfzde//vU899xzuf7665Mkf/jDH/L666+nf//+rc7x8MMPZ82aNenXr1+S5F//9V8zevTo9O7dO//zf/7PJEmPHj2246PumO66666iIwBQBvMaAABKaxfl2Q9/+MPMnj07t912WwYPHpwkOe6447Jw4cL8+c9/TkNDQ5Lk3/7t33Lrrbdmzpw5+chHPpIkOeaYY/LCCy/kqquuSmNjY/bYY4+WN//fWJJttHH7xlKtf//+eeqppzJixIgcddRR2+Oh7hCWLl2affbZp+gYAJRgXgMAQGntojybMmVKPv3pT7cUZxsddNBB2XnnnXPooYcmSb797W/nxBNPbCnONt0vSZ566qnsscceWbhwYXr06JH999+/1X6LFi1Kr169svfeeyf56wq1jSvbtsa8efOS7Hgr1caPH19yn8bGxpL7NTY2VioSAFvQ0eb1V6++LsmG37ub3m7PqjEzAMCOZOXKlWXtV/h7ni1fvjyPPPJIhg0bttn3nn766dTX16dLly55/PHH8+STT2bo0KGb7ffUU08lSXr16pVkQ0n21lVnG7dveinnwoULk2y+Qg0AAAAAknaw8uyZZ55Jkuy7776ttq9Zsyb3339/PvnJTyZJXnjhhSR/Lcg29d///d/p169f9txzzyTJkiVLMmrUqFb7vPLKK3nwwQdzwQUXtGxbuHBh9t133/Ts2XOrc29cJTdnzpytPraaLVu2rOQ+jY2Nmz3/b3XFFVdUKhIAW9DR5vU102Yk2fDXwU1vt2fVmBkAgM0VvvJs4yWUf/zjH1ttv+666/LMM8+0XFLZp0+fJMmf/vSnVvvdfffdueeee3L++ecnSV599dW88sor6d27d6v9vvvd72bNmjWtVp498sgjOeSQQyr7gMiECROKjgBAGcxrAAAorfCVZwceeGDq6+szefLkvOc978n73ve+3HnnnZkxY8NfaA8//PAkG1amnXjiiWlsbMzuu++eAw88ML/61a8yderUnHPOOTnjjDOSJLvvvnt69eqV2bNn52/+5m/S3Nyc6dOnt3wS56blWffu3fPrX/86d999d7p3754+ffpstgKOrTd8+PCiIwBQBvMaAABKK3zlWU1NTW644YbU19fnsssuy+jRo9Pc3JwvfvGL2WuvvVJfX9+y73e+85186lOfyj//8z/n85//fO68885cc801mTx5cqv7/M53vpMXX3wxRx11VD772c9m5513zsiRI7PHHnu0rGBLkssuuyx9+vTJqFGjcuKJJ+a3v/3tdnvcHdnGT0cFoH0zrwEAoLTCV54lycEHH5zbbrut5esVK1bkE5/4RL7whS+ktvav/V737t0zZcqUkvc3ZMiQ3HfffZttf+v7tnzoQx/KnXfe+S6SAwAAANCRFV6ezZ8/Pz/72c9y1FFHZdddd81DDz2U73znO9lzzz0zfvz4ouMBAAAAsAMrvDx7/fXXc/fdd2fatGlZs2ZN+vTpk+HDh2f8+PHp1q1b0fF4B4YMGVJ0BADKYF4DAEBphZdnxxxzTO65556iY1BBU6dOLToCAGUwrwEAoLTCPzCAjmfcuHFFRwCgDOY1AACUpjyj4ubOnVt0BADKYF4DAEBpyjMAAAAAaIPyDAAAAADaoDyj4hYsWFB0BADKYF4DAEBpyjMqbubMmUVHAKAM5jUAAJSmPKPiJk2aVHQEAMpgXgMAQGnKMwAAAABog/IMAAAAANqgPKPipkyZUnQEAMpgXgMAQGnKMyqurq6u6AgAlMG8BgCA0pRnVNywYcOKjgBAGcxrAAAoTXkGAAAAAG1QngEAAABAGzoXHYDq0qdPn5L7XHrppWXtB8C2Y14DAEBlWHlGxV122WVFRwCgDOY1AACUpjwDAAAAgDYozwAAAACgDcozAAAAAGiD8gwAAAAA2qA8AwAAAIA2KM8AAAAAoA3Kswpbvnx5Tj311Bx99NH5yEc+kokTJxYdCQB2aPfee28+8pGPZODAgRk3blzWr19fdKSSLrvsstTX1+e9731v0VEAAHZ4yrMK69y5c/7xH/8x999/f+65557cd999+elPf1p0LADYITU1NeXCCy/MDTfckPnz5+fVV1/Nj3/846JjlXTqqafmv/7rv4qOAQBAlGcV17Nnzxx++OFJkp122in9+/fP8uXLC04FADum3/3ud+nZs2cOPfTQJMnIkSMzZ86cglOV9uEPfzj77rtv0TEAAIjybJtatWpVfvKTn+S4444rOgoA7JCefvrp7Lfffi1f77///nnqqacKTAQAQLXpXHSAjurNN9/MOeeck/POOy8f+tCHio4DAFXld79/NAsf/tNm22+45a7Nbvd6b4+ceMzA1NTUbLZ/c3Pztgv5Fq+98Zfccue8zd5TbUuZk5qcfPzR2XvP92y3fAAAvDNWnm0D69evz5e+9KX07ds3Y8eOLToOAFSdfoccmHXr1uf5F1/O8y++3LL9rbdXvrQ6R/U/ZIvFWZLst99+rVaaLV++PL17994mmXfbdZfUfeCAkpmff/HlHNBrb8UZAECVUJ5tAxdddFG6deuWSZMmFR0FAKpSl86d8zcfP7rNUmyjj3+4f/bqvnub3z/88MPz9NNP5+GHH06S3HTTTTnppJMqmnVTR/b9YN7Xe5+33adH991z7KD+2ywDAACVtcOWZ88880zOPffcvP/9788BBxyQz372s1m6dOm7vt/77rsv//Ef/5EFCxbk2GOPzZAhQ3LddddVIDEA7Fh679MjHz28rs3vH7j/vjn8sA+87X106tQpU6ZMyTnnnJMjjjgiu+22W84444xKR21RU1OTTx93VHbeqcsWv19bU5OTP350unTu9Lb3M378+NTX12f9+vWpr6/PV77ylW0RFwCAMtSsXLly+70ZSDvxxhtv5LjjjktTU1MmTJiQrl27ZvLkyfnjH/+Ye+65p9UbC7fl5JNPTpKt/sSu5ubmkn9FBwA2WN/UlJtu+688u2JVq+277Nwlfzf8xOy+264FJXt7v//jn3P7fz+w2fZjjqzPR484rIBEAAC8UzvkyrMf/OAHefTRR3PjjTfmlFNOydChQ3PzzTdn3bp1aWxs3GbnXb++KTf+5/+Xhx5dts3OAQAdSafa2pz08aPTuVPrlVrDPjaw3RZnSXLYwX1yyEH7t9rWe58e+XDDoQUlAgDgneqw5dlDDz2UL3zhCzn44IPTq1evHHnkkS3vQfazn/0s/fv3T13dXy8F6d69ez75yU/mJz/5yTbLtPiRx/PsilXZZeedttk5AKCj2av77jnu6L++R9hhB/dJ3QcOKDBRaTU1NTnxY0ekW9ddkiRdOnfK33x8UGprO+w/vQAAOqwO+S+4BQsWZNiwYXnkkUfy9a9/PT/60Y9y4YUX5rnnnkuSLF26tFVxtlFdXV2ee+65rFy5suKZ1q9vyq8eXJre+/TI+/fft+L3DwAd2RH1H8iB++2bbrvtmqGDDy86Tll23WXnfOrYo5IkH//wgPTYo+0PNgAAoP3qXHSAbeGKK65I165dc/fdd+c97/nrx8CfffbZSZJVq1Zljz322Oy47t27t3y/R48eb3uOZU+vSJJcM23GVmV79bU38o3vzdyqYwCAv5ry/duKjrDV7rr3d7nr3t8VHQMAgE189R9GlLVfh1t59vrrr+fXv/51Tj/99FbFGQAAAABsrQ638uyll15KU1NTevfu3eY+e+65Z15++eUtHrvx+6X06f3eJOW1lAseeix33vu7jPjUMTnogJ4l9wcAAACgfehw5Vn37t1TW1ubZ555ps19Dj300Dz88MObbX/44Yez7777lrxkM0n+kp2TbN1lmzN+9suy9wUAAABg29lhL9vs2rVrPvrRj2bWrFl55ZVXtrjPpz71qSxcuDCPPPJIy7aXX345d9xxRz796U+XdZ4TTj4jJ5x8RkUyAwAAANA+1axcubK56BCVtmDBgpx00knZf//9M27cuBxwwAFZvnx5fv3rX2fq1Kl5/fXXc+yxx6ampiYTJkzIrrvumsbGxjzyyCO55557sv/++1ckx/r1Tbnuhz/N7rvtmpGnHJ+ampqK3C8AAAAA20eHW3mWJA0NDbnzzjvzgQ98IF/72tdyxhlnZPLkyenZc8P7jXXt2jW33XZb+vbtm3HjxuWLX/xidtlll8yZM6dixVmSLH7k8bz62hsZPLBecQYAAABQhTrkyrP2Yt78h/Lnp5/P3550rPIMAAAAoAopz7axpqbm1NYqzgAAAACqUYe8bLM9UZwBAAAAVC/lGQAAAAC0QXkGAAAAAG1QngEAAABAG5RnAAAAANAG5RkAAAAAtEF5BgAAAABtUJ4BAAAAQBuUZwAAAADQBuUZAAAAALRBeQYAAAAAbVCeAQAAAEAblGcAAAAA0AblGQAAAAC0QXkGAAAAAG1QngEAAABAG5RnAAAAANAG5RkAAAAAtEF5BgAAAABtUJ4BAAAAQBuUZwAAAADQBuUZAAAAALRBeQYAAAAAbVCeAQAAAEAblGcAAAAA0AblGQAAAAC0QXkGAAAAAG1QngEAAABAG/5/2nK3u9VXMUYAAAAASUVORK5CYII=\n", "text/plain": ["<Figure size 1612.22x1167.88 with 1 Axes>"]}, "execution_count": 106, "metadata": {}, "output_type": "execute_result"}], "source": ["circuit.draw(output='mpl', style={'backgroundcolor': '#EEEEEE'})"]}, {"cell_type": "code", "execution_count": 107, "id": "challenging-emphasis", "metadata": {}, "outputs": [], "source": ["from qiskit import Aer\n", "sim = Aer.get_backend('aer_simulator')"]}, {"cell_type": "code", "execution_count": 108, "id": "boring-brisbane", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAc0AAAE6CAYAAAB00gm8AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjMuNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8QVMy6AAAACXBIWXMAAAsTAAALEwEAmpwYAAAZZ0lEQVR4nO3df7BcZZ3n8feXRERMUAgScnNBDKF0JxFBGwfwAtEyw4BbqGCJrJrJoGSJIxEpd0ZrhZm4irM6srDOskrGEoLuLCOuuo4BwqAxFIQbbzITRd0kJZgxIdzAEI0KJBC/+8fp<PERSON><PERSON><PERSON>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\n", "text/plain": ["<Figure size 504x360 with 1 Axes>"]}, "execution_count": 108, "metadata": {}, "output_type": "execute_result"}], "source": ["from qiskit.visualization import plot_histogram\n", "qobj = assemble(circuit)\n", "result = sim.run(qobj).result()\n", "counts = result.get_counts()\n", "plot_histogram(counts)"]}, {"cell_type": "code", "execution_count": null, "id": "timely-addiction", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}}, "nbformat": 4, "nbformat_minor": 5}