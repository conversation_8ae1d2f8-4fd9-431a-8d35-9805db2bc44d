architecture,file,logic_variables,time
32symbolic,memory-access-fail-1-35,0,0.08
32symbolic,nested-if-else-1-35,247258,6.52
32symbolic,nested-if-else-reverse-1-35,232693,6.43
32symbolic,recursive-ackermann-1-35,12748,1.8
32symbolic,recursive-factorial-fail-1-35,133282,4.4
32symbolic,recursive-fibonacci-1-10,196354,6.63
32symbolic,return-from-loop-1-35,37206,2.03
32symbolic,simple-assignment-1-35,44021,1.85
32symbolic,simple-if-else-1-35,65430,3.16
32symbolic,simple-if-else-reverse-1-35,70842,2.87
32symbolic,simple-if-without-else-1-35,116626,4.91
32symbolic,u,620,0.5
64symbolic,memory-access-fail-1-35,0,0.13
64symbolic,nested-if-else-1-35,365548,9.94
64symbolic,nested-if-else-reverse-1-35,350618,9.85
64symbolic,recursive-ackermann-1-35,11290,2.98
64symbolic,recursive-factorial-fail-1-35,33780,2.81
64symbolic,recursive-fibonacci-1-10,35411,3.14
64symbolic,return-from-loop-1-35,61740,3.03
64symbolic,simple-assignment-1-35,70369,3.07
64symbolic,simple-if-else-1-35,103691,4.76
64symbolic,simple-if-else-reverse-1-35,112047,4.77
64symbolic,simple-if-without-else-1-35,184418,6.07
64symbolic,u,718,0.92
64symbolic,invalid-memory-access-fail-2-35,14853,1.47
64symbolic,nested-recursion-fail-1-35,16366,2.54
