architecture,file,logic_variables,time
32fsm,12bad_const,69699,2.14
32fsm,baseline,91691,5.19
32fsm,base_mr,99899,5.93
32fsm,const_mr,88686,3.57
32fsm,base_lin,90775,5.5
32fsm,12bad_c_lin,68966,2.41
32fsm,12bad_lin,90346,5.31
32fsm,const_prop,70128,2.55
32fsm,const_lin,69395,2.28
32fsm,base_mmu,106134,6.51
32fsm,base_ram,114855,6.6
32fsm,const_mmu,97558,3.3
32fsm,12bad_c_mr,88257,2.81
32fsm,const_ram,104637,3.55
32fsm,12bad,91262,5.3
32fsm,12bad_mmu,105705,7.74
32fsm,12bad_c_ram,104208,3.43
32fsm,12bad_ram,114426,7.23
32fsm,12bad_mr,99470,5.72
32fsm,12bad_c_mmu,97129,3.25
64fsm,12bad_const,138267,4.45
64fsm,baseline,180447,10.06
64fsm,base_mr,196619,10.53
64fsm,const_mr,173139,5.47
64fsm,base_lin,163691,10.03
64fsm,12bad_c_lin,124862,4.59
64fsm,12bad_lin,162837,9.43
64fsm,const_prop,139121,4.53
64fsm,const_lin,125716,4.27
64fsm,base_mmu,204359,12.78
64fsm,base_ram,221044,12.76
64fsm,const_mmu,187644,6.59
64fsm,12bad_c_mr,172285,5.75
64fsm,const_ram,201631,7.21
64fsm,12bad,179593,10.55
64fsm,12bad_mmu,203505,11.18
64fsm,12bad_c_ram,200777,7.01
64fsm,12bad_ram,220190,12.42
64fsm,12bad_mr,195765,11.94
64fsm,12bad_c_mmu,186790,7.64
