{"cells": [{"cell_type": "code", "execution_count": 2, "id": "recognized-income", "metadata": {}, "outputs": [], "source": ["from dwave.system import DWaveSampler, EmbeddingComposite\n", "from greedy import SteepestDescentComposite\n", "import sys, os\n", "sys.path.append(\"../\")\n", "from bqm_input_checker import InputChecker\n", "from btor2bqm import BTor2BQM"]}, {"cell_type": "markdown", "id": "grateful-liabilities", "metadata": {}, "source": ["# 32 bit"]}, {"cell_type": "code", "execution_count": 3, "id": "posted-bermuda", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["started building ../../../qa_examples/32_u.btor2 for 15 timesteps\n", "{'begin_datasegment': 17408, 'begin_heap': 18432, 'size_datasegment': 2, 'size_heap': 1, 'size_stack': 12, 'word_size': 32, 'address_step_size': 1, 'address_word_size': 30, 'begin_stack': 1073741824}\n", "output dir:  ./32_u/\n", "sort memory modified to be bitvector of size:  480\n", "{1: ['1', 'sort', 'bitvec', '1'], 2: ['2', 'sort', 'bitvec', '32'], 4: ['4', 'sort', 'bitvec', '30'], 5: ['5', 'sort', 'bitvec', '480'], 10: ['10', 'zero', '1'], 11: ['11', 'one', '1'], 20: ['20', 'zero', '2'], 21: ['21', 'one', '2'], 22: ['22', 'constd', '2', '2'], 23: ['23', 'constd', '2', '3'], 24: ['24', 'constd', '2', '4'], 30: ['30', 'constd', '2', '69632'], 31: ['31', 'constd', '2', '69640'], 32: ['32', 'constd', '2', '73728'], 33: ['33', 'constd', '2', '73732'], 40: ['40', 'constd', '4', '17408'], 41: ['41', 'constd', '4', '17410'], 42: ['42', 'constd', '4', '18432'], 43: ['43', 'constd', '4', '18433'], 50: ['50', 'constd', '2', '4294967292'], 60: ['60', 'state', '1', 'kernel-mode'], 61: ['61', 'init', '1', '60', '10', 'kernel-mode'], 62: ['62', 'not', '1', '60'], 71: ['71', 'sort', 'bitvec', '8'], 72: ['72', 'sort', 'bitvec', '16'], 73: ['73', 'sort', 'bitvec', '24'], 81: ['81', 'input', '71', '1-byte-input'], 82: ['82', 'input', '72', '2-byte-input'], 83: ['83', 'input', '73', '3-byte-input'], 91: ['91', 'uext', '2', '81', '24'], 92: ['92', 'uext', '2', '82', '16'], 93: ['93', 'uext', '2', '83', '8'], 94: ['94', 'input', '2', '4-byte-input'], 101: ['101', 'constd', '2', '65952'], 102: ['102', 'constd', '2', '4294967248'], 103: ['103', 'constd', '2', '69640'], 105: ['105', 'constd', '2', '1'], 108: ['108', 'constd', '2', '4294967252'], 111: ['111', 'constd', '2', '73728'], 112: ['112', 'constd', '2', '1'], 116: ['116', 'constd', '2', '1074145840'], 117: ['117', 'constd', '2', '63'], 200: ['200', 'zero', '2', 'zero'], 201: ['201', 'state', '2', 'ra'], 202: ['202', 'state', '2', 'sp'], 203: ['203', 'state', '2', 'gp'], 204: ['204', 'state', '2', 'tp'], 205: ['205', 'state', '2', 't0'], 206: ['206', 'state', '2', 't1'], 207: ['207', 'state', '2', 't2'], 208: ['208', 'state', '2', 's0'], 209: ['209', 'state', '2', 's1'], 210: ['210', 'state', '2', 'a0'], 211: ['211', 'state', '2', 'a1'], 212: ['212', 'state', '2', 'a2'], 213: ['213', 'state', '2', 'a3'], 214: ['214', 'state', '2', 'a4'], 215: ['215', 'state', '2', 'a5'], 216: ['216', 'state', '2', 'a6'], 217: ['217', 'state', '2', 'a7'], 218: ['218', 'state', '2', 's2'], 219: ['219', 'state', '2', 's3'], 220: ['220', 'state', '2', 's4'], 221: ['221', 'state', '2', 's5'], 222: ['222', 'state', '2', 's6'], 223: ['223', 'state', '2', 's7'], 224: ['224', 'state', '2', 's8'], 225: ['225', 'state', '2', 's9'], 226: ['226', 'state', '2', 's10'], 227: ['227', 'state', '2', 's11'], 228: ['228', 'state', '2', 't3'], 229: ['229', 'state', '2', 't4'], 230: ['230', 'state', '2', 't5'], 231: ['231', 'state', '2', 't6'], 301: ['301', 'init', '2', '201', '101', 'ra'], 302: ['302', 'init', '2', '202', '102', 'sp'], 303: ['303', 'init', '2', '203', '103', 'gp'], 304: ['304', 'init', '2', '204', '20', 'tp'], 305: ['305', 'init', '2', '205', '105', 't0'], 306: ['306', 'init', '2', '206', '20', 't1'], 307: ['307', 'init', '2', '207', '20', 't2'], 308: ['308', 'init', '2', '208', '108', 's0'], 309: ['309', 'init', '2', '209', '20', 's1'], 310: ['310', 'init', '2', '210', '20', 'a0'], 311: ['311', 'init', '2', '211', '111', 'a1'], 312: ['312', 'init', '2', '212', '112', 'a2'], 313: ['313', 'init', '2', '213', '20', 'a3'], 314: ['314', 'init', '2', '214', '20', 'a4'], 315: ['315', 'init', '2', '215', '20', 'a5'], 316: ['316', 'init', '2', '216', '116', 'a6'], 317: ['317', 'init', '2', '217', '117', 'a7'], 318: ['318', 'init', '2', '218', '20', 's2'], 319: ['319', 'init', '2', '219', '20', 's3'], 320: ['320', 'init', '2', '220', '20', 's4'], 321: ['321', 'init', '2', '221', '20', 's5'], 322: ['322', 'init', '2', '222', '20', 's6'], 323: ['323', 'init', '2', '223', '20', 's7'], 324: ['324', 'init', '2', '224', '20', 's8'], 325: ['325', 'init', '2', '225', '20', 's9'], 326: ['326', 'init', '2', '226', '20', 's10'], 327: ['327', 'init', '2', '227', '20', 's11'], 328: ['328', 'init', '2', '228', '20', 't3'], 329: ['329', 'init', '2', '229', '20', 't4'], 330: ['330', 'init', '2', '230', '20', 't5'], 331: ['331', 'init', '2', '231', '20', 't6'], 16561600: ['16561600', 'state', '1', 'pc=0x10050'], 16561601: ['16561601', 'init', '1', '16561600', '10'], 16562000: ['16562000', 'state', '1', 'pc=0x10054'], 16562001: ['16562001', 'init', '1', '16562000', '10'], 16562400: ['16562400', 'state', '1', 'pc=0x10058'], 16562401: ['16562401', 'init', '1', '16562400', '10'], 16562800: ['16562800', 'state', '1', 'pc=0x1005c'], 16562801: ['16562801', 'init', '1', '16562800', '10'], 16563200: ['16563200', 'state', '1', 'pc=0x10060'], 16563201: ['16563201', 'init', '1', '16563200', '10'], 16563600: ['16563600', 'state', '1', 'pc=0x10064'], 16563601: ['16563601', 'init', '1', '16563600', '10'], 16566800: ['16566800', 'state', '1', 'pc=0x10084'], 16566801: ['16566801', 'init', '1', '16566800', '11'], 16567200: ['16567200', 'state', '1', 'pc=0x10088'], 16567201: ['16567201', 'init', '1', '16567200', '10'], 16595200: ['16595200', 'state', '1', 'pc=0x101a0'], 16595201: ['16595201', 'init', '1', '16595200', '10'], 16595600: ['16595600', 'state', '1', 'pc=0x101a4'], 16595601: ['16595601', 'init', '1', '16595600', '10'], 16596000: ['16596000', 'state', '1', 'pc=0x101a8'], 16596001: ['16596001', 'init', '1', '16596000', '10'], 16596400: ['16596400', 'state', '1', 'pc=0x101ac'], 16596401: ['16596401', 'init', '1', '16596400', '10'], 16596800: ['16596800', 'state', '1', 'pc=0x101b0'], 16596801: ['16596801', 'init', '1', '16596800', '10'], 16597200: ['16597200', 'state', '1', 'pc=0x101b4'], 16597201: ['16597201', 'init', '1', '16597200', '10'], 16597600: ['16597600', 'state', '1', 'pc=0x101b8'], 16597601: ['16597601', 'init', '1', '16597600', '10'], 16598000: ['16598000', 'state', '1', 'pc=0x101bc'], 16598001: ['16598001', 'init', '1', '16598000', '10'], 16598400: ['16598400', 'state', '1', 'pc=0x101c0'], 16598401: ['16598401', 'init', '1', '16598400', '10'], 16598800: ['16598800', 'state', '1', 'pc=0x101c4'], 16598801: ['16598801', 'init', '1', '16598800', '10'], 16599200: ['16599200', 'state', '1', 'pc=0x101c8'], 16599201: ['16599201', 'init', '1', '16599200', '10'], 16599600: ['16599600', 'state', '1', 'pc=0x101cc'], 16599601: ['16599601', 'init', '1', '16599600', '10'], 16600000: ['16600000', 'state', '1', 'pc=0x101d0'], 16600001: ['16600001', 'init', '1', '16600000', '10'], 16600400: ['16600400', 'state', '1', 'pc=0x101d4'], 16600401: ['16600401', 'init', '1', '16600400', '10'], 16600800: ['16600800', 'state', '1', 'pc=0x101d8'], 16600801: ['16600801', 'init', '1', '16600800', '10'], 16601200: ['16601200', 'state', '1', 'pc=0x101dc'], 16601201: ['16601201', 'init', '1', '16601200', '10'], 16601600: ['16601600', 'state', '1', 'pc=0x101e0'], 16601601: ['16601601', 'init', '1', '16601600', '10'], 16963200: ['16963200', 'state', '5', 'memory-dump'], 16963201: ['16963201', 'constd', '4', '17408'], 16963202: ['16963202', 'constd', '2', '73728'], 16963203: ['16963203', 'write', '5', '16963200', '16963201', '16963202'], 16963600: ['16963600', 'constd', '4', '17409'], 16963601: ['16963601', 'constd', '2', '73732'], 16963602: ['16963602', 'write', '5', '16963203', '16963600', '16963601'], 17372800: ['17372800', 'constd', '4', '18432'], 17372801: ['17372801', 'write', '5', '16963602', '17372800', '20'], 17373200: ['17373200', 'constd', '4', '1073741812'], 17373201: ['17373201', 'write', '5', '17372801', '17373200', '20'], 17373600: ['17373600', 'constd', '4', '1073741813'], 17373601: ['17373601', 'write', '5', '17373201', '17373600', '20'], 17374000: ['17374000', 'constd', '4', '1073741814'], 17374001: ['17374001', 'constd', '2', '65616'], 17374002: ['17374002', 'write', '5', '17373601', '17374000', '17374001'], 17374400: ['17374400', 'constd', '4', '1073741815'], 17374401: ['17374401', 'constd', '2', '1'], 17374402: ['17374402', 'write', '5', '17374002', '17374400', '17374401'], 17374800: ['17374800', 'constd', '4', '1073741816'], 17374801: ['17374801', 'constd', '2', '4294967268'], 17374802: ['17374802', 'write', '5', '17374402', '17374800', '17374801'], 17375200: ['17375200', 'constd', '4', '1073741817'], 17375201: ['17375201', 'constd', '2', '4294967280'], 17375202: ['17375202', 'write', '5', '17374802', '17375200', '17375201'], 17375600: ['17375600', 'constd', '4', '1073741818'], 17375601: ['17375601', 'write', '5', '17375202', '17375600', '20'], 17376000: ['17376000', 'constd', '4', '1073741819'], 17376001: ['17376001', 'write', '5', '17375601', '17376000', '20'], 17376400: ['17376400', 'constd', '4', '1073741820'], 17376401: ['17376401', 'constd', '2', '1700749681'], 17376402: ['17376402', 'write', '5', '17376001', '17376400', '17376401'], 17376800: ['17376800', 'constd', '4', '1073741821'], 17376801: ['17376801', 'constd', '2', '1886216568'], 17376802: ['17376802', 'write', '5', '17376402', '17376800', '17376801'], 17377200: ['17377200', 'constd', '4', '1073741822'], 17377201: ['17377201', 'constd', '2', '796091756'], 17377202: ['17377202', 'write', '5', '17376802', '17377200', '17377201'], 17377600: ['17377600', 'constd', '4', '1073741823'], 17377601: ['17377601', 'constd', '2', '6499957'], 17377602: ['17377602', 'write', '5', '17377202', '17377600', '17377601'], 20000000: ['20000000', 'state', '5', 'physical-memory'], 20000001: ['20000001', 'init', '5', '20000000', '17377602'], 36561200: ['36561200', 'constd', '2', '65616'], 36561600: ['36561600', 'constd', '2', '-4'], 36561601: ['36561601', 'add', '2', '202', '36561600'], 36561602: ['36561602', 'ite', '2', '16561600', '36561601', '202'], 36562000: ['36562000', 'slice', '4', '202', '31', '2'], 36562001: ['36562001', 'ite', '2', '16562000', '202', '30'], 36562002: ['36562002', 'write', '5', '20000000', '36562000', '210'], 36562003: ['36562003', 'ite', '5', '16562000', '36562002', '20000000'], 36562400: ['36562400', 'slice', '4', '202', '31', '2'], 36562401: ['36562401', 'ite', '2', '16562400', '202', '36562001'], 36562402: ['36562402', 'read', '2', '20000000', '36562400'], 36562403: ['36562403', 'ite', '2', '16562400', '36562402', '210'], 36562800: ['36562800', 'constd', '2', '4'], 36562801: ['36562801', 'add', '2', '202', '36562800'], 36562802: ['36562802', 'ite', '2', '16562800', '36562801', '36561602'], 36563200: ['36563200', 'constd', '2', '93'], 36563201: ['36563201', 'ite', '2', '16563200', '36563200', '217'], 36563600: ['36563600', 'ite', '1', '16563600', '11', '10'], 36566800: ['36566800', 'ite', '1', '16566800', '11', '36563600'], 36589200: ['36589200', 'constd', '2', '65896'], 36594800: ['36594800', 'constd', '2', '65952'], 36595200: ['36595200', 'ite', '2', '16595200', '200', '36562403'], 36595600: ['36595600', 'constd', '2', '-8'], 36595601: ['36595601', 'add', '2', '203', '36595600'], 36595602: ['36595602', 'slice', '4', '36595601', '31', '2'], 36595603: ['36595603', 'ite', '2', '16595600', '36595601', '36562401'], 36595604: ['36595604', 'read', '2', '20000000', '36595602'], 36595605: ['36595605', 'ite', '2', '16595600', '36595604', '205'], 36596000: ['36596000', 'slice', '4', '205', '31', '2'], 36596001: ['36596001', 'ite', '2', '16596000', '205', '36595603'], 36596002: ['36596002', 'read', '2', '20000000', '36596000'], 36596003: ['36596003', 'ite', '2', '16596000', '36596002', '36595605'], 36596400: ['36596400', 'constd', '2', '-4'], 36596401: ['36596401', 'add', '2', '208', '36596400'], 36596402: ['36596402', 'slice', '4', '36596401', '31', '2'], 36596403: ['36596403', 'ite', '2', '16596400', '36596401', '36596001'], 36596404: ['36596404', 'write', '5', '20000000', '36596402', '205'], 36596405: ['36596405', 'ite', '5', '16596400', '36596404', '36562003'], 36596800: ['36596800', 'constd', '2', '-8'], 36596801: ['36596801', 'add', '2', '203', '36596800'], 36596802: ['36596802', 'slice', '4', '36596801', '31', '2'], 36596803: ['36596803', 'ite', '2', '16596800', '36596801', '36596403'], 36596804: ['36596804', 'read', '2', '20000000', '36596802'], 36596805: ['36596805', 'ite', '2', '16596800', '36596804', '36596003'], 36597200: ['36597200', 'constd', '2', '-4'], 36597201: ['36597201', 'add', '2', '208', '36597200'], 36597202: ['36597202', 'slice', '4', '36597201', '31', '2'], 36597203: ['36597203', 'ite', '2', '16597200', '36597201', '36596803'], 36597204: ['36597204', 'read', '2', '20000000', '36597202'], 36597205: ['36597205', 'ite', '2', '16597200', '36597204', '206'], 36597600: ['36597600', 'constd', '2', '4'], 36597601: ['36597601', 'ite', '2', '16597600', '36597600', '207'], 36598000: ['36598000', 'mul', '2', '206', '207'], 36598001: ['36598001', 'ite', '2', '16598000', '36598000', '36597205'], 36598400: ['36598400', 'add', '2', '205', '206'], 36598401: ['36598401', 'ite', '2', '16598400', '36598400', '36596805'], 36598800: ['36598800', 'slice', '4', '205', '31', '2'], 36598801: ['36598801', 'ite', '2', '16598800', '205', '36597203'], 36598802: ['36598802', 'read', '2', '20000000', '36598800'], 36598803: ['36598803', 'ite', '2', '16598800', '36598802', '36598401'], 36599200: ['36599200', 'constd', '2', '-4'], 36599201: ['36599201', 'add', '2', '208', '36599200'], 36599202: ['36599202', 'slice', '4', '36599201', '31', '2'], 36599203: ['36599203', 'ite', '2', '16599200', '36599201', '36598801'], 36599204: ['36599204', 'write', '5', '20000000', '36599202', '205'], 36599205: ['36599205', 'ite', '5', '16599200', '36599204', '36596405'], 36599600: ['36599600', 'ite', '2', '16599600', '208', '36562802'], 36600000: ['36600000', 'slice', '4', '202', '31', '2'], 36600001: ['36600001', 'ite', '2', '16600000', '202', '36599203'], 36600002: ['36600002', 'read', '2', '20000000', '36600000'], 36600003: ['36600003', 'ite', '2', '16600000', '36600002', '208'], 36600400: ['36600400', 'constd', '2', '4'], 36600401: ['36600401', 'add', '2', '202', '36600400'], 36600402: ['36600402', 'ite', '2', '16600400', '36600401', '36599600'], 36600800: ['36600800', 'slice', '4', '202', '31', '2'], 36600801: ['36600801', 'ite', '2', '16600800', '202', '36600001'], 36600802: ['36600802', 'read', '2', '20000000', '36600800'], 36600803: ['36600803', 'ite', '2', '16600800', '36600802', '201'], 36601200: ['36601200', 'constd', '2', '4'], 36601201: ['36601201', 'add', '2', '202', '36601200'], 36601202: ['36601202', 'ite', '2', '16601200', '36601201', '36600402'], 40000000: ['40000000', 'constd', '2', '93'], 40000001: ['40000001', 'constd', '2', '63'], 40000002: ['40000002', 'constd', '2', '64'], 40000003: ['40000003', 'constd', '2', '56'], 40000004: ['40000004', 'constd', '2', '214'], 40000010: ['40000010', 'eq', '1', '217', '40000000'], 40000011: ['40000011', 'eq', '1', '217', '40000001'], 40000012: ['40000012', 'eq', '1', '217', '40000002'], 40000013: ['40000013', 'eq', '1', '217', '40000003'], 40000014: ['40000014', 'eq', '1', '217', '40000004'], 41000000: ['41000000', 'and', '1', '36566800', '40000010'], 41000001: ['41000001', 'ite', '1', '60', '40000010', '41000000'], 42000000: ['42000000', 'and', '1', '36566800', '40000011'], 42000001: ['42000001', 'ite', '2', '42000000', '211', '36600801'], 42000002: ['42000002', 'ite', '1', '42000000', '11', '41000001'], 42000003: ['42000003', 'ite', '2', '42000000', '20', '36595200'], 42000004: ['42000004', 'sub', '2', '212', '210'], 42000005: ['42000005', 'ugte', '1', '42000004', '24'], 42000006: ['42000006', 'ite', '2', '42000005', '24', '42000004'], 42000007: ['42000007', 'eq', '1', '42000006', '22'], 42000008: ['42000008', 'ite', '2', '42000007', '92', '91'], 42000009: ['42000009', 'eq', '1', '42000006', '23'], 42000010: ['42000010', 'ite', '2', '42000009', '93', '42000008'], 42000011: ['42000011', 'eq', '1', '42000006', '24'], 42000012: ['42000012', 'ite', '2', '42000011', '94', '42000010'], 42000013: ['42000013', 'add', '2', '211', '210'], 42000014: ['42000014', 'slice', '4', '42000013', '31', '2'], 42000015: ['42000015', 'write', '5', '20000000', '42000014', '42000012'], 42000016: ['42000016', 'ult', '1', '210', '212'], 42000017: ['42000017', 'and', '1', '40000011', '42000016'], 42000018: ['42000018', 'and', '1', '60', '42000017'], 42000019: ['42000019', 'ugt', '1', '42000006', '20'], 42000020: ['42000020', 'and', '1', '42000018', '42000019'], 42000021: ['42000021', 'ite', '5', '42000020', '42000015', '36599205'], 42000022: ['42000022', 'add', '2', '210', '42000006'], 42000023: ['42000023', 'ite', '2', '42000018', '42000022', '42000003'], 42000024: ['42000024', 'ite', '1', '42000018', '11', '42000002'], 43000000: ['43000000', 'and', '1', '36566800', '40000012'], 43000001: ['43000001', 'ite', '2', '43000000', '211', '42000001'], 43000002: ['43000002', 'ite', '2', '43000000', '212', '42000023'], 44000000: ['44000000', 'and', '1', '36566800', '40000013'], 44000001: ['44000001', 'ite', '2', '44000000', '211', '43000001'], 44000002: ['44000002', 'state', '2', 'fd-bump-pointer'], 44000003: ['44000003', 'init', '2', '44000002', '21'], 44000004: ['44000004', 'inc', '2', '44000002'], 44000005: ['44000005', 'ite', '2', '44000000', '44000004', '44000002'], 44000006: ['44000006', 'next', '2', '44000002', '44000005'], 44000007: ['44000007', 'ite', '2', '44000000', '44000004', '43000002'], 45000000: ['45000000', 'and', '1', '36566800', '40000014'], 45000001: ['45000001', 'state', '2', 'brk-bump-pointer'], 45000002: ['45000002', 'init', '2', '45000001', '33'], 45000003: ['45000003', 'ulte', '1', '45000001', '210'], 45000004: ['45000004', 'ult', '1', '210', '202'], 45000005: ['45000005', 'and', '1', '45000003', '45000004'], 45000006: ['45000006', 'and', '2', '210', '23'], 45000007: ['45000007', 'eq', '1', '45000006', '20'], 45000008: ['45000008', 'and', '1', '45000005', '45000007'], 45000009: ['45000009', 'and', '1', '45000000', '45000008'], 45000010: ['45000010', 'ite', '2', '45000009', '210', '45000001'], 45000011: ['45000011', 'next', '2', '45000001', '45000010'], 45000012: ['45000012', 'not', '1', '45000008'], 45000013: ['45000013', 'and', '1', '45000000', '45000012'], 45000014: ['45000014', 'ite', '2', '45000013', '45000001', '44000007'], 46000000: ['46000000', 'next', '1', '60', '42000024'], 56561600: ['56561600', 'not', '2', '21'], 56561601: ['56561601', 'and', '2', '201', '56561600'], 56561602: ['56561602', 'eq', '1', '56561601', '36561200'], 56561603: ['56561603', 'and', '1', '16601600', '56561602'], 56561604: ['56561604', 'next', '1', '16561600', '56561603'], 56562000: ['56562000', 'next', '1', '16562000', '16561600'], 56562400: ['56562400', 'next', '1', '16562400', '16562000'], 56562800: ['56562800', 'next', '1', '16562800', '16562400'], 56563200: ['56563200', 'next', '1', '16563200', '16562800'], 56563600: ['56563600', 'next', '1', '16563600', '16563200'], 56564000: ['56564000', 'state', '1', 'kernel-mode-pc-flag-65636[0x10064](~1)'], 56564001: ['56564001', 'init', '1', '56564000', '10'], 56564002: ['56564002', 'ite', '1', '56564000', '60', '16563600'], 56564003: ['56564003', 'next', '1', '56564000', '56564002'], 56564004: ['56564004', 'and', '1', '56564000', '62'], 56566800: ['56566800', 'next', '1', '16566800', '10'], 56567200: ['56567200', 'state', '1', 'kernel-mode-pc-flag-65668[0x10084](~1)'], 56567201: ['56567201', 'init', '1', '56567200', '10'], 56567202: ['56567202', 'ite', '1', '56567200', '60', '16566800'], 56567203: ['56567203', 'next', '1', '56567200', '56567202'], 56567204: ['56567204', 'and', '1', '56567200', '62'], 56567205: ['56567205', 'next', '1', '16567200', '56567204'], 56595200: ['56595200', 'not', '2', '21'], 56595201: ['56595201', 'and', '2', '201', '56595200'], 56595202: ['56595202', 'eq', '1', '56595201', '36594800'], 56595203: ['56595203', 'and', '1', '16567200', '56595202'], 56595204: ['56595204', 'next', '1', '16595200', '56595203'], 56595600: ['56595600', 'next', '1', '16595600', '16595200'], 56596000: ['56596000', 'next', '1', '16596000', '16595600'], 56596400: ['56596400', 'next', '1', '16596400', '16596000'], 56596800: ['56596800', 'next', '1', '16596800', '16596400'], 56597200: ['56597200', 'next', '1', '16597200', '16596800'], 56597600: ['56597600', 'next', '1', '16597600', '16597200'], 56598000: ['56598000', 'next', '1', '16598000', '16597600'], 56598400: ['56598400', 'next', '1', '16598400', '16598000'], 56598800: ['56598800', 'next', '1', '16598800', '16598400'], 56599200: ['56599200', 'next', '1', '16599200', '16598800'], 56599600: ['56599600', 'next', '1', '16599600', '16599200'], 56600000: ['56600000', 'next', '1', '16600000', '16599600'], 56600400: ['56600400', 'next', '1', '16600400', '16600000'], 56600800: ['56600800', 'next', '1', '16600800', '16600400'], 56601200: ['56601200', 'next', '1', '16601200', '16600800'], 56601600: ['56601600', 'next', '1', '16601600', '16601200'], 60000001: ['60000001', 'next', '2', '201', '36600803', 'ra'], 60000002: ['60000002', 'next', '2', '202', '36601202', 'sp'], 60000003: ['60000003', 'next', '2', '203', '203', 'gp'], 60000004: ['60000004', 'next', '2', '204', '204', 'tp'], 60000005: ['60000005', 'next', '2', '205', '36598803', 't0'], 60000006: ['60000006', 'next', '2', '206', '36598001', 't1'], 60000007: ['60000007', 'next', '2', '207', '36597601', 't2'], 60000008: ['60000008', 'next', '2', '208', '36600003', 's0'], 60000009: ['60000009', 'next', '2', '209', '209', 's1'], 60000010: ['60000010', 'next', '2', '210', '45000014', 'a0'], 60000011: ['60000011', 'next', '2', '211', '211', 'a1'], 60000012: ['60000012', 'next', '2', '212', '212', 'a2'], 60000013: ['60000013', 'next', '2', '213', '213', 'a3'], 60000014: ['60000014', 'next', '2', '214', '214', 'a4'], 60000015: ['60000015', 'next', '2', '215', '215', 'a5'], 60000016: ['60000016', 'next', '2', '216', '216', 'a6'], 60000017: ['60000017', 'next', '2', '217', '36563201', 'a7'], 60000018: ['60000018', 'next', '2', '218', '218', 's2'], 60000019: ['60000019', 'next', '2', '219', '219', 's3'], 60000020: ['60000020', 'next', '2', '220', '220', 's4'], 60000021: ['60000021', 'next', '2', '221', '221', 's5'], 60000022: ['60000022', 'next', '2', '222', '222', 's6'], 60000023: ['60000023', 'next', '2', '223', '223', 's7'], 60000024: ['60000024', 'next', '2', '224', '224', 's8'], 60000025: ['60000025', 'next', '2', '225', '225', 's9'], 60000026: ['60000026', 'next', '2', '226', '226', 's10'], 60000027: ['60000027', 'next', '2', '227', '227', 's11'], 60000028: ['60000028', 'next', '2', '228', '228', 't3'], 60000029: ['60000029', 'next', '2', '229', '229', 't4'], 60000030: ['60000030', 'next', '2', '230', '230', 't5'], 60000031: ['60000031', 'next', '2', '231', '231', 't6'], 70000000: ['70000000', 'next', '5', '20000000', '42000021', 'physical-memory'], 80000000: ['80000000', 'ult', '1', '44000001', '30'], 80000001: ['80000001', 'bad', '80000000', 'b0'], 80000002: ['80000002', 'ugte', '1', '44000001', '31'], 80000003: ['80000003', 'ult', '1', '44000001', '32'], 80000004: ['80000004', 'and', '1', '80000002', '80000003'], 80000005: ['80000005', 'bad', '80000004', 'b1'], 80000006: ['80000006', 'ugte', '1', '44000001', '45000001'], 80000007: ['80000007', 'ult', '1', '44000001', '202'], 80000008: ['80000008', 'and', '1', '80000006', '80000007'], 80000009: ['80000009', 'bad', '80000008', 'b2'], 80000010: ['80000010', 'ugt', '1', '44000001', '50'], 80000011: ['80000011', 'bad', '80000010', 'b3']}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["0.35 348\n"]}], "source": ["parser = BTor2BQM(15)\n", "bqm_32, total_time_32, num_variables_32 = parser.parse_file(f\"../../../qa_examples/32_u.btor2\",\n", "                          f\"./32_u/\",\n", "                          with_init=True, modify_memory_sort=True)\n", "print(total_time_32, num_variables_32)"]}, {"cell_type": "markdown", "id": "announced-cancer", "metadata": {}, "source": ["# 64 bit"]}, {"cell_type": "code", "execution_count": 4, "id": "shaped-lottery", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["started building ../../../qa_examples/64_u.btor2 for 15 timesteps\n", "{'begin_datasegment': 8704, 'begin_heap': 9216, 'size_datasegment': 2, 'size_heap': 1, 'size_stack': 10, 'word_size': 64, 'address_step_size': 1, 'address_word_size': 29, 'begin_stack': 536870912}\n", "output dir:  ./64_u/\n", "sort memory modified to be bitvector of size:  832\n", "{1: ['1', 'sort', 'bitvec', '1'], 2: ['2', 'sort', 'bitvec', '64'], 4: ['4', 'sort', 'bitvec', '29'], 5: ['5', 'sort', 'bitvec', '832'], 10: ['10', 'zero', '1'], 11: ['11', 'one', '1'], 20: ['20', 'zero', '2'], 21: ['21', 'one', '2'], 22: ['22', 'constd', '2', '2'], 23: ['23', 'constd', '2', '3'], 24: ['24', 'constd', '2', '4'], 25: ['25', 'constd', '2', '5'], 26: ['26', 'constd', '2', '6'], 27: ['27', 'constd', '2', '7'], 28: ['28', 'constd', '2', '8'], 30: ['30', 'constd', '2', '69632'], 31: ['31', 'constd', '2', '69648'], 32: ['32', 'constd', '2', '73728'], 33: ['33', 'constd', '2', '73736'], 40: ['40', 'constd', '4', '8704'], 41: ['41', 'constd', '4', '8706'], 42: ['42', 'constd', '4', '9216'], 43: ['43', 'constd', '4', '9217'], 50: ['50', 'constd', '2', '4294967288'], 60: ['60', 'state', '1', 'kernel-mode'], 61: ['61', 'init', '1', '60', '10', 'kernel-mode'], 62: ['62', 'not', '1', '60'], 71: ['71', 'sort', 'bitvec', '8'], 72: ['72', 'sort', 'bitvec', '16'], 73: ['73', 'sort', 'bitvec', '24'], 74: ['74', 'sort', 'bitvec', '32'], 75: ['75', 'sort', 'bitvec', '40'], 76: ['76', 'sort', 'bitvec', '48'], 77: ['77', 'sort', 'bitvec', '56'], 81: ['81', 'input', '71', '1-byte-input'], 82: ['82', 'input', '72', '2-byte-input'], 83: ['83', 'input', '73', '3-byte-input'], 84: ['84', 'input', '74', '4-byte-input'], 85: ['85', 'input', '75', '5-byte-input'], 86: ['86', 'input', '76', '6-byte-input'], 87: ['87', 'input', '77', '7-byte-input'], 91: ['91', 'uext', '2', '81', '56'], 92: ['92', 'uext', '2', '82', '48'], 93: ['93', 'uext', '2', '83', '40'], 94: ['94', 'uext', '2', '84', '32'], 95: ['95', 'uext', '2', '85', '24'], 96: ['96', 'uext', '2', '86', '16'], 97: ['97', 'uext', '2', '87', '8'], 98: ['98', 'input', '2', '8-byte-input'], 101: ['101', 'constd', '2', '65952'], 102: ['102', 'constd', '2', '4294967216'], 103: ['103', 'constd', '2', '69648'], 105: ['105', 'constd', '2', '1'], 108: ['108', 'constd', '2', '4294967224'], 111: ['111', 'constd', '2', '73728'], 112: ['112', 'constd', '2', '1'], 116: ['116', 'constd', '2', '105553163124736'], 117: ['117', 'constd', '2', '63'], 200: ['200', 'zero', '2', 'zero'], 201: ['201', 'state', '2', 'ra'], 202: ['202', 'state', '2', 'sp'], 203: ['203', 'state', '2', 'gp'], 204: ['204', 'state', '2', 'tp'], 205: ['205', 'state', '2', 't0'], 206: ['206', 'state', '2', 't1'], 207: ['207', 'state', '2', 't2'], 208: ['208', 'state', '2', 's0'], 209: ['209', 'state', '2', 's1'], 210: ['210', 'state', '2', 'a0'], 211: ['211', 'state', '2', 'a1'], 212: ['212', 'state', '2', 'a2'], 213: ['213', 'state', '2', 'a3'], 214: ['214', 'state', '2', 'a4'], 215: ['215', 'state', '2', 'a5'], 216: ['216', 'state', '2', 'a6'], 217: ['217', 'state', '2', 'a7'], 218: ['218', 'state', '2', 's2'], 219: ['219', 'state', '2', 's3'], 220: ['220', 'state', '2', 's4'], 221: ['221', 'state', '2', 's5'], 222: ['222', 'state', '2', 's6'], 223: ['223', 'state', '2', 's7'], 224: ['224', 'state', '2', 's8'], 225: ['225', 'state', '2', 's9'], 226: ['226', 'state', '2', 's10'], 227: ['227', 'state', '2', 's11'], 228: ['228', 'state', '2', 't3'], 229: ['229', 'state', '2', 't4'], 230: ['230', 'state', '2', 't5'], 231: ['231', 'state', '2', 't6'], 301: ['301', 'init', '2', '201', '101', 'ra'], 302: ['302', 'init', '2', '202', '102', 'sp'], 303: ['303', 'init', '2', '203', '103', 'gp'], 304: ['304', 'init', '2', '204', '20', 'tp'], 305: ['305', 'init', '2', '205', '105', 't0'], 306: ['306', 'init', '2', '206', '20', 't1'], 307: ['307', 'init', '2', '207', '20', 't2'], 308: ['308', 'init', '2', '208', '108', 's0'], 309: ['309', 'init', '2', '209', '20', 's1'], 310: ['310', 'init', '2', '210', '20', 'a0'], 311: ['311', 'init', '2', '211', '111', 'a1'], 312: ['312', 'init', '2', '212', '112', 'a2'], 313: ['313', 'init', '2', '213', '20', 'a3'], 314: ['314', 'init', '2', '214', '20', 'a4'], 315: ['315', 'init', '2', '215', '20', 'a5'], 316: ['316', 'init', '2', '216', '116', 'a6'], 317: ['317', 'init', '2', '217', '117', 'a7'], 318: ['318', 'init', '2', '218', '20', 's2'], 319: ['319', 'init', '2', '219', '20', 's3'], 320: ['320', 'init', '2', '220', '20', 's4'], 321: ['321', 'init', '2', '221', '20', 's5'], 322: ['322', 'init', '2', '222', '20', 's6'], 323: ['323', 'init', '2', '223', '20', 's7'], 324: ['324', 'init', '2', '224', '20', 's8'], 325: ['325', 'init', '2', '225', '20', 's9'], 326: ['326', 'init', '2', '226', '20', 's10'], 327: ['327', 'init', '2', '227', '20', 's11'], 328: ['328', 'init', '2', '228', '20', 't3'], 329: ['329', 'init', '2', '229', '20', 't4'], 330: ['330', 'init', '2', '230', '20', 't5'], 331: ['331', 'init', '2', '231', '20', 't6'], 16561600: ['16561600', 'state', '1', 'pc=0x10050'], 16561601: ['16561601', 'init', '1', '16561600', '10'], 16562000: ['16562000', 'state', '1', 'pc=0x10054'], 16562001: ['16562001', 'init', '1', '16562000', '10'], 16562400: ['16562400', 'state', '1', 'pc=0x10058'], 16562401: ['16562401', 'init', '1', '16562400', '10'], 16562800: ['16562800', 'state', '1', 'pc=0x1005c'], 16562801: ['16562801', 'init', '1', '16562800', '10'], 16563200: ['16563200', 'state', '1', 'pc=0x10060'], 16563201: ['16563201', 'init', '1', '16563200', '10'], 16563600: ['16563600', 'state', '1', 'pc=0x10064'], 16563601: ['16563601', 'init', '1', '16563600', '10'], 16566800: ['16566800', 'state', '1', 'pc=0x10084'], 16566801: ['16566801', 'init', '1', '16566800', '11'], 16567200: ['16567200', 'state', '1', 'pc=0x10088'], 16567201: ['16567201', 'init', '1', '16567200', '10'], 16595200: ['16595200', 'state', '1', 'pc=0x101a0'], 16595201: ['16595201', 'init', '1', '16595200', '10'], 16595600: ['16595600', 'state', '1', 'pc=0x101a4'], 16595601: ['16595601', 'init', '1', '16595600', '10'], 16596000: ['16596000', 'state', '1', 'pc=0x101a8'], 16596001: ['16596001', 'init', '1', '16596000', '10'], 16596400: ['16596400', 'state', '1', 'pc=0x101ac'], 16596401: ['16596401', 'init', '1', '16596400', '10'], 16596800: ['16596800', 'state', '1', 'pc=0x101b0'], 16596801: ['16596801', 'init', '1', '16596800', '10'], 16597200: ['16597200', 'state', '1', 'pc=0x101b4'], 16597201: ['16597201', 'init', '1', '16597200', '10'], 16597600: ['16597600', 'state', '1', 'pc=0x101b8'], 16597601: ['16597601', 'init', '1', '16597600', '10'], 16598000: ['16598000', 'state', '1', 'pc=0x101bc'], 16598001: ['16598001', 'init', '1', '16598000', '10'], 16598400: ['16598400', 'state', '1', 'pc=0x101c0'], 16598401: ['16598401', 'init', '1', '16598400', '10'], 16598800: ['16598800', 'state', '1', 'pc=0x101c4'], 16598801: ['16598801', 'init', '1', '16598800', '10'], 16599200: ['16599200', 'state', '1', 'pc=0x101c8'], 16599201: ['16599201', 'init', '1', '16599200', '10'], 16599600: ['16599600', 'state', '1', 'pc=0x101cc'], 16599601: ['16599601', 'init', '1', '16599600', '10'], 16600000: ['16600000', 'state', '1', 'pc=0x101d0'], 16600001: ['16600001', 'init', '1', '16600000', '10'], 16600400: ['16600400', 'state', '1', 'pc=0x101d4'], 16600401: ['16600401', 'init', '1', '16600400', '10'], 16600800: ['16600800', 'state', '1', 'pc=0x101d8'], 16600801: ['16600801', 'init', '1', '16600800', '10'], 16601200: ['16601200', 'state', '1', 'pc=0x101dc'], 16601201: ['16601201', 'init', '1', '16601200', '10'], 16601600: ['16601600', 'state', '1', 'pc=0x101e0'], 16601601: ['16601601', 'init', '1', '16601600', '10'], 16963200: ['16963200', 'state', '5', 'memory-dump'], 16963201: ['16963201', 'constd', '4', '8704'], 16963202: ['16963202', 'constd', '2', '73728'], 16963203: ['16963203', 'write', '5', '16963200', '16963201', '16963202'], 16964000: ['16964000', 'constd', '4', '8705'], 16964001: ['16964001', 'constd', '2', '73736'], 16964002: ['16964002', 'write', '5', '16963203', '16964000', '16964001'], 17372800: ['17372800', 'constd', '4', '9216'], 17372801: ['17372801', 'write', '5', '16964002', '17372800', '20'], 17373600: ['17373600', 'constd', '4', '536870902'], 17373601: ['17373601', 'write', '5', '17372801', '17373600', '20'], 17374400: ['17374400', 'constd', '4', '536870903'], 17374401: ['17374401', 'write', '5', '17373601', '17374400', '20'], 17375200: ['17375200', 'constd', '4', '536870904'], 17375201: ['17375201', 'constd', '2', '65616'], 17375202: ['17375202', 'write', '5', '17374401', '17375200', '17375201'], 17376000: ['17376000', 'constd', '4', '536870905'], 17376001: ['17376001', 'constd', '2', '1'], 17376002: ['17376002', 'write', '5', '17375202', '17376000', '17376001'], 17376800: ['17376800', 'constd', '4', '536870906'], 17376801: ['17376801', 'constd', '2', '4294967256'], 17376802: ['17376802', 'write', '5', '17376002', '17376800', '17376801'], 17377600: ['17377600', 'constd', '4', '536870907'], 17377601: ['17377601', 'constd', '2', '4294967280'], 17377602: ['17377602', 'write', '5', '17376802', '17377600', '17377601'], 17378400: ['17378400', 'constd', '4', '536870908'], 17378401: ['17378401', 'write', '5', '17377602', '17378400', '20'], 17379200: ['17379200', 'constd', '4', '536870909'], 17379201: ['17379201', 'write', '5', '17378401', '17379200', '20'], 17380000: ['17380000', 'constd', '4', '536870910'], 17380001: ['17380001', 'constd', '2', '8101238474434109809'], 17380002: ['17380002', 'write', '5', '17379201', '17380000', '17380001'], 17380800: ['17380800', 'constd', '4', '536870911'], 17380801: ['17380801', 'constd', '2', '27917103536498028'], 17380802: ['17380802', 'write', '5', '17380002', '17380800', '17380801'], 20000000: ['20000000', 'state', '5', 'physical-memory'], 20000001: ['20000001', 'init', '5', '20000000', '17380802'], 36561200: ['36561200', 'constd', '2', '65616'], 36561600: ['36561600', 'constd', '2', '-8'], 36561601: ['36561601', 'add', '2', '202', '36561600'], 36561602: ['36561602', 'ite', '2', '16561600', '36561601', '202'], 36562000: ['36562000', 'slice', '4', '202', '31', '3'], 36562001: ['36562001', 'ite', '2', '16562000', '202', '30'], 36562002: ['36562002', 'write', '5', '20000000', '36562000', '210'], 36562003: ['36562003', 'ite', '5', '16562000', '36562002', '20000000'], 36562400: ['36562400', 'slice', '4', '202', '31', '3'], 36562401: ['36562401', 'ite', '2', '16562400', '202', '36562001'], 36562402: ['36562402', 'read', '2', '20000000', '36562400'], 36562403: ['36562403', 'ite', '2', '16562400', '36562402', '210'], 36562800: ['36562800', 'constd', '2', '8'], 36562801: ['36562801', 'add', '2', '202', '36562800'], 36562802: ['36562802', 'ite', '2', '16562800', '36562801', '36561602'], 36563200: ['36563200', 'constd', '2', '93'], 36563201: ['36563201', 'ite', '2', '16563200', '36563200', '217'], 36563600: ['36563600', 'ite', '1', '16563600', '11', '10'], 36566800: ['36566800', 'ite', '1', '16566800', '11', '36563600'], 36589200: ['36589200', 'constd', '2', '65896'], 36594800: ['36594800', 'constd', '2', '65952'], 36595200: ['36595200', 'ite', '2', '16595200', '200', '36562403'], 36595600: ['36595600', 'constd', '2', '-16'], 36595601: ['36595601', 'add', '2', '203', '36595600'], 36595602: ['36595602', 'slice', '4', '36595601', '31', '3'], 36595603: ['36595603', 'ite', '2', '16595600', '36595601', '36562401'], 36595604: ['36595604', 'read', '2', '20000000', '36595602'], 36595605: ['36595605', 'ite', '2', '16595600', '36595604', '205'], 36596000: ['36596000', 'slice', '4', '205', '31', '3'], 36596001: ['36596001', 'ite', '2', '16596000', '205', '36595603'], 36596002: ['36596002', 'read', '2', '20000000', '36596000'], 36596003: ['36596003', 'ite', '2', '16596000', '36596002', '36595605'], 36596400: ['36596400', 'constd', '2', '-8'], 36596401: ['36596401', 'add', '2', '208', '36596400'], 36596402: ['36596402', 'slice', '4', '36596401', '31', '3'], 36596403: ['36596403', 'ite', '2', '16596400', '36596401', '36596001'], 36596404: ['36596404', 'write', '5', '20000000', '36596402', '205'], 36596405: ['36596405', 'ite', '5', '16596400', '36596404', '36562003'], 36596800: ['36596800', 'constd', '2', '-16'], 36596801: ['36596801', 'add', '2', '203', '36596800'], 36596802: ['36596802', 'slice', '4', '36596801', '31', '3'], 36596803: ['36596803', 'ite', '2', '16596800', '36596801', '36596403'], 36596804: ['36596804', 'read', '2', '20000000', '36596802'], 36596805: ['36596805', 'ite', '2', '16596800', '36596804', '36596003'], 36597200: ['36597200', 'constd', '2', '-8'], 36597201: ['36597201', 'add', '2', '208', '36597200'], 36597202: ['36597202', 'slice', '4', '36597201', '31', '3'], 36597203: ['36597203', 'ite', '2', '16597200', '36597201', '36596803'], 36597204: ['36597204', 'read', '2', '20000000', '36597202'], 36597205: ['36597205', 'ite', '2', '16597200', '36597204', '206'], 36597600: ['36597600', 'constd', '2', '8'], 36597601: ['36597601', 'ite', '2', '16597600', '36597600', '207'], 36598000: ['36598000', 'mul', '2', '206', '207'], 36598001: ['36598001', 'ite', '2', '16598000', '36598000', '36597205'], 36598400: ['36598400', 'add', '2', '205', '206'], 36598401: ['36598401', 'ite', '2', '16598400', '36598400', '36596805'], 36598800: ['36598800', 'slice', '4', '205', '31', '3'], 36598801: ['36598801', 'ite', '2', '16598800', '205', '36597203'], 36598802: ['36598802', 'read', '2', '20000000', '36598800'], 36598803: ['36598803', 'ite', '2', '16598800', '36598802', '36598401'], 36599200: ['36599200', 'constd', '2', '-8'], 36599201: ['36599201', 'add', '2', '208', '36599200'], 36599202: ['36599202', 'slice', '4', '36599201', '31', '3'], 36599203: ['36599203', 'ite', '2', '16599200', '36599201', '36598801'], 36599204: ['36599204', 'write', '5', '20000000', '36599202', '205'], 36599205: ['36599205', 'ite', '5', '16599200', '36599204', '36596405'], 36599600: ['36599600', 'ite', '2', '16599600', '208', '36562802'], 36600000: ['36600000', 'slice', '4', '202', '31', '3'], 36600001: ['36600001', 'ite', '2', '16600000', '202', '36599203'], 36600002: ['36600002', 'read', '2', '20000000', '36600000'], 36600003: ['36600003', 'ite', '2', '16600000', '36600002', '208'], 36600400: ['36600400', 'constd', '2', '8'], 36600401: ['36600401', 'add', '2', '202', '36600400'], 36600402: ['36600402', 'ite', '2', '16600400', '36600401', '36599600'], 36600800: ['36600800', 'slice', '4', '202', '31', '3'], 36600801: ['36600801', 'ite', '2', '16600800', '202', '36600001'], 36600802: ['36600802', 'read', '2', '20000000', '36600800'], 36600803: ['36600803', 'ite', '2', '16600800', '36600802', '201'], 36601200: ['36601200', 'constd', '2', '8'], 36601201: ['36601201', 'add', '2', '202', '36601200'], 36601202: ['36601202', 'ite', '2', '16601200', '36601201', '36600402'], 40000000: ['40000000', 'constd', '2', '93'], 40000001: ['40000001', 'constd', '2', '63'], 40000002: ['40000002', 'constd', '2', '64'], 40000003: ['40000003', 'constd', '2', '56'], 40000004: ['40000004', 'constd', '2', '214'], 40000010: ['40000010', 'eq', '1', '217', '40000000'], 40000011: ['40000011', 'eq', '1', '217', '40000001'], 40000012: ['40000012', 'eq', '1', '217', '40000002'], 40000013: ['40000013', 'eq', '1', '217', '40000003'], 40000014: ['40000014', 'eq', '1', '217', '40000004'], 41000000: ['41000000', 'and', '1', '36566800', '40000010'], 41000001: ['41000001', 'ite', '1', '60', '40000010', '41000000'], 42000000: ['42000000', 'and', '1', '36566800', '40000011'], 42000001: ['42000001', 'ite', '2', '42000000', '211', '36600801'], 42000002: ['42000002', 'ite', '1', '42000000', '11', '41000001'], 42000003: ['42000003', 'ite', '2', '42000000', '20', '36595200'], 42000004: ['42000004', 'sub', '2', '212', '210'], 42000005: ['42000005', 'ugte', '1', '42000004', '28'], 42000006: ['42000006', 'ite', '2', '42000005', '28', '42000004'], 42000007: ['42000007', 'eq', '1', '42000006', '22'], 42000008: ['42000008', 'ite', '2', '42000007', '92', '91'], 42000009: ['42000009', 'eq', '1', '42000006', '23'], 42000010: ['42000010', 'ite', '2', '42000009', '93', '42000008'], 42000011: ['42000011', 'eq', '1', '42000006', '24'], 42000012: ['42000012', 'ite', '2', '42000011', '94', '42000010'], 42000013: ['42000013', 'eq', '1', '42000006', '25'], 42000014: ['42000014', 'ite', '2', '42000013', '95', '42000012'], 42000015: ['42000015', 'eq', '1', '42000006', '26'], 42000016: ['42000016', 'ite', '2', '42000015', '96', '42000014'], 42000017: ['42000017', 'eq', '1', '42000006', '27'], 42000018: ['42000018', 'ite', '2', '42000017', '97', '42000016'], 42000019: ['42000019', 'eq', '1', '42000006', '28'], 42000020: ['42000020', 'ite', '2', '42000019', '98', '42000018'], 42000021: ['42000021', 'add', '2', '211', '210'], 42000022: ['42000022', 'slice', '4', '42000021', '31', '3'], 42000023: ['42000023', 'write', '5', '20000000', '42000022', '42000020'], 42000024: ['42000024', 'ult', '1', '210', '212'], 42000025: ['42000025', 'and', '1', '40000011', '42000024'], 42000026: ['42000026', 'and', '1', '60', '42000025'], 42000027: ['42000027', 'ugt', '1', '42000006', '20'], 42000028: ['42000028', 'and', '1', '42000026', '42000027'], 42000029: ['42000029', 'ite', '5', '42000028', '42000023', '36599205'], 42000030: ['42000030', 'add', '2', '210', '42000006'], 42000031: ['42000031', 'ite', '2', '42000026', '42000030', '42000003'], 42000032: ['42000032', 'ite', '1', '42000026', '11', '42000002'], 43000000: ['43000000', 'and', '1', '36566800', '40000012'], 43000001: ['43000001', 'ite', '2', '43000000', '211', '42000001'], 43000002: ['43000002', 'ite', '2', '43000000', '212', '42000031'], 44000000: ['44000000', 'and', '1', '36566800', '40000013'], 44000001: ['44000001', 'ite', '2', '44000000', '211', '43000001'], 44000002: ['44000002', 'state', '2', 'fd-bump-pointer'], 44000003: ['44000003', 'init', '2', '44000002', '21'], 44000004: ['44000004', 'inc', '2', '44000002'], 44000005: ['44000005', 'ite', '2', '44000000', '44000004', '44000002'], 44000006: ['44000006', 'next', '2', '44000002', '44000005'], 44000007: ['44000007', 'ite', '2', '44000000', '44000004', '43000002'], 45000000: ['45000000', 'and', '1', '36566800', '40000014'], 45000001: ['45000001', 'state', '2', 'brk-bump-pointer'], 45000002: ['45000002', 'init', '2', '45000001', '33'], 45000003: ['45000003', 'ulte', '1', '45000001', '210'], 45000004: ['45000004', 'ult', '1', '210', '202'], 45000005: ['45000005', 'and', '1', '45000003', '45000004'], 45000006: ['45000006', 'and', '2', '210', '27'], 45000007: ['45000007', 'eq', '1', '45000006', '20'], 45000008: ['45000008', 'and', '1', '45000005', '45000007'], 45000009: ['45000009', 'and', '1', '45000000', '45000008'], 45000010: ['45000010', 'ite', '2', '45000009', '210', '45000001'], 45000011: ['45000011', 'next', '2', '45000001', '45000010'], 45000012: ['45000012', 'not', '1', '45000008'], 45000013: ['45000013', 'and', '1', '45000000', '45000012'], 45000014: ['45000014', 'ite', '2', '45000013', '45000001', '44000007'], 46000000: ['46000000', 'next', '1', '60', '42000032'], 56561600: ['56561600', 'not', '2', '21'], 56561601: ['56561601', 'and', '2', '201', '56561600'], 56561602: ['56561602', 'eq', '1', '56561601', '36561200'], 56561603: ['56561603', 'and', '1', '16601600', '56561602'], 56561604: ['56561604', 'next', '1', '16561600', '56561603'], 56562000: ['56562000', 'next', '1', '16562000', '16561600'], 56562400: ['56562400', 'next', '1', '16562400', '16562000'], 56562800: ['56562800', 'next', '1', '16562800', '16562400'], 56563200: ['56563200', 'next', '1', '16563200', '16562800'], 56563600: ['56563600', 'next', '1', '16563600', '16563200'], 56564000: ['56564000', 'state', '1', 'kernel-mode-pc-flag-65636[0x10064](~1)'], 56564001: ['56564001', 'init', '1', '56564000', '10'], 56564002: ['56564002', 'ite', '1', '56564000', '60', '16563600'], 56564003: ['56564003', 'next', '1', '56564000', '56564002'], 56564004: ['56564004', 'and', '1', '56564000', '62'], 56566800: ['56566800', 'next', '1', '16566800', '10'], 56567200: ['56567200', 'state', '1', 'kernel-mode-pc-flag-65668[0x10084](~1)'], 56567201: ['56567201', 'init', '1', '56567200', '10'], 56567202: ['56567202', 'ite', '1', '56567200', '60', '16566800'], 56567203: ['56567203', 'next', '1', '56567200', '56567202'], 56567204: ['56567204', 'and', '1', '56567200', '62'], 56567205: ['56567205', 'next', '1', '16567200', '56567204'], 56595200: ['56595200', 'not', '2', '21'], 56595201: ['56595201', 'and', '2', '201', '56595200'], 56595202: ['56595202', 'eq', '1', '56595201', '36594800'], 56595203: ['56595203', 'and', '1', '16567200', '56595202'], 56595204: ['56595204', 'next', '1', '16595200', '56595203'], 56595600: ['56595600', 'next', '1', '16595600', '16595200'], 56596000: ['56596000', 'next', '1', '16596000', '16595600'], 56596400: ['56596400', 'next', '1', '16596400', '16596000'], 56596800: ['56596800', 'next', '1', '16596800', '16596400'], 56597200: ['56597200', 'next', '1', '16597200', '16596800'], 56597600: ['56597600', 'next', '1', '16597600', '16597200'], 56598000: ['56598000', 'next', '1', '16598000', '16597600'], 56598400: ['56598400', 'next', '1', '16598400', '16598000'], 56598800: ['56598800', 'next', '1', '16598800', '16598400'], 56599200: ['56599200', 'next', '1', '16599200', '16598800'], 56599600: ['56599600', 'next', '1', '16599600', '16599200'], 56600000: ['56600000', 'next', '1', '16600000', '16599600'], 56600400: ['56600400', 'next', '1', '16600400', '16600000'], 56600800: ['56600800', 'next', '1', '16600800', '16600400'], 56601200: ['56601200', 'next', '1', '16601200', '16600800'], 56601600: ['56601600', 'next', '1', '16601600', '16601200'], 60000001: ['60000001', 'next', '2', '201', '36600803', 'ra'], 60000002: ['60000002', 'next', '2', '202', '36601202', 'sp'], 60000003: ['60000003', 'next', '2', '203', '203', 'gp'], 60000004: ['60000004', 'next', '2', '204', '204', 'tp'], 60000005: ['60000005', 'next', '2', '205', '36598803', 't0'], 60000006: ['60000006', 'next', '2', '206', '36598001', 't1'], 60000007: ['60000007', 'next', '2', '207', '36597601', 't2'], 60000008: ['60000008', 'next', '2', '208', '36600003', 's0'], 60000009: ['60000009', 'next', '2', '209', '209', 's1'], 60000010: ['60000010', 'next', '2', '210', '45000014', 'a0'], 60000011: ['60000011', 'next', '2', '211', '211', 'a1'], 60000012: ['60000012', 'next', '2', '212', '212', 'a2'], 60000013: ['60000013', 'next', '2', '213', '213', 'a3'], 60000014: ['60000014', 'next', '2', '214', '214', 'a4'], 60000015: ['60000015', 'next', '2', '215', '215', 'a5'], 60000016: ['60000016', 'next', '2', '216', '216', 'a6'], 60000017: ['60000017', 'next', '2', '217', '36563201', 'a7'], 60000018: ['60000018', 'next', '2', '218', '218', 's2'], 60000019: ['60000019', 'next', '2', '219', '219', 's3'], 60000020: ['60000020', 'next', '2', '220', '220', 's4'], 60000021: ['60000021', 'next', '2', '221', '221', 's5'], 60000022: ['60000022', 'next', '2', '222', '222', 's6'], 60000023: ['60000023', 'next', '2', '223', '223', 's7'], 60000024: ['60000024', 'next', '2', '224', '224', 's8'], 60000025: ['60000025', 'next', '2', '225', '225', 's9'], 60000026: ['60000026', 'next', '2', '226', '226', 's10'], 60000027: ['60000027', 'next', '2', '227', '227', 's11'], 60000028: ['60000028', 'next', '2', '228', '228', 't3'], 60000029: ['60000029', 'next', '2', '229', '229', 't4'], 60000030: ['60000030', 'next', '2', '230', '230', 't5'], 60000031: ['60000031', 'next', '2', '231', '231', 't6'], 70000000: ['70000000', 'next', '5', '20000000', '42000029', 'physical-memory'], 80000000: ['80000000', 'ult', '1', '44000001', '30'], 80000001: ['80000001', 'bad', '80000000', 'b0'], 80000002: ['80000002', 'ugte', '1', '44000001', '31'], 80000003: ['80000003', 'ult', '1', '44000001', '32'], 80000004: ['80000004', 'and', '1', '80000002', '80000003'], 80000005: ['80000005', 'bad', '80000004', 'b1'], 80000006: ['80000006', 'ugte', '1', '44000001', '45000001'], 80000007: ['80000007', 'ult', '1', '44000001', '202'], 80000008: ['80000008', 'and', '1', '80000006', '80000007'], 80000009: ['80000009', 'bad', '80000008', 'b2'], 80000010: ['80000010', 'ugt', '1', '44000001', '50'], 80000011: ['80000011', 'bad', '80000010', 'b3']}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["0.66 398\n"]}], "source": ["parser64 = BTor2BQM(15)\n", "bqm_64, total_time_64, num_variables_64 = parser64.parse_file(f\"../../../qa_examples/64_u.btor2\",\n", "                          f\"./64_u/\",\n", "                          with_init=True, modify_memory_sort=True)\n", "print(total_time_64, num_variables_64)"]}, {"cell_type": "markdown", "id": "numerical-joseph", "metadata": {}, "source": ["# Minor Embeddings"]}, {"cell_type": "code", "execution_count": 5, "id": "handy-assault", "metadata": {}, "outputs": [], "source": ["import dwave_networkx as dnx"]}, {"cell_type": "code", "execution_count": 6, "id": "plastic-democrat", "metadata": {}, "outputs": [{"data": {"text/plain": ["5640"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["pegasus_graph = dnx.pegasus_graph(16)\n", "len(pegasus_graph.nodes)"]}, {"cell_type": "code", "execution_count": 7, "id": "minus-mercury", "metadata": {}, "outputs": [{"data": {"text/plain": ["2048"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["chimera_graph = dnx.chimera_graph(16)\n", "len(chimera_graph.nodes)"]}, {"cell_type": "code", "execution_count": 8, "id": "magnetic-automation", "metadata": {}, "outputs": [], "source": ["import minorminer\n", "import dimod\n", "problem_graph_32 = dimod.to_networkx_graph(bqm_32)\n", "problem_graph_64 = dimod.to_networkx_graph(bqm_64)"]}, {"cell_type": "code", "execution_count": 9, "id": "direct-kansas", "metadata": {}, "outputs": [], "source": ["def get_count_embedding_physical_variables(embedding):\n", "    result = 0\n", "    for (key, ancillas) in embedding.items():\n", "        result += len(ancillas)\n", "    return result\n", "def get_chain_lengths(embedding):\n", "    result = []\n", "    for (key, ancillas) in embedding.items():\n", "        result.append(len(ancillas))\n", "    return result"]}, {"cell_type": "code", "execution_count": 10, "id": "available-demand", "metadata": {}, "outputs": [], "source": ["import time\n", "import random\n", "def get_statistics_embedding(target_graph, source_graph, num_runs, seed=1):\n", "    random.seed(seed)\n", "    times = []\n", "    distribution_chain_lengths = []\n", "    vars_in_qpu = []\n", "    for _ in range(num_runs):\n", "        embedding_seed = random.randint(0, 10000)\n", "        t0 = time.perf_counter()\n", "        embedding =  minorminer.find_embedding(source_graph, target_graph, random_seed=embedding_seed)\n", "        t1 = time.perf_counter()\n", "        times.append(round(t1-t0, 2))\n", "        vars_in_qpu.append(get_count_embedding_physical_variables(embedding))\n", "        distribution_chain_lengths.extend(get_chain_lengths(embedding))\n", "    return times, distribution_chain_lengths, vars_in_qpu"]}, {"cell_type": "code", "execution_count": 11, "id": "allied-brooklyn", "metadata": {}, "outputs": [], "source": ["import statistics\n", "import pandas as pd\n", "COL_FILE_NAME = 'file_name'\n", "COL_WORDSIZE = 'wordsize'\n", "COL_LOGIC_VARS = 'logic_vars'\n", "COL_TOPOLOGY = 'topology'\n", "\n", "COL_EMB_MAX_TIME = 'emb_max_time'\n", "COL_EMB_MIN_TIME = 'emb_min_time'\n", "COL_EMB_MEDIAN_TIME = 'emb_median_time'\n", "COL_EMB_AVG_TIME = 'emb_avg_time'\n", "\n", "COL_EMB_MAX_CHAIN = 'emb_max_chain'\n", "COL_EMB_MIN_CHAIN = 'emb_min_chain'\n", "COL_EMB_MEDIAN_CHAIN = 'emb_median_chain'\n", "COL_EMB_AVG_CHAIN = 'emb_avg_chain'\n", "\n", "COL_EMB_MAX_VARS = 'emb_max_vars'\n", "COL_EMB_MIN_VARS = 'emb_min_vars'\n", "COL_EMB_MEDIAN_VARS = 'emb_median_vars'\n", "COL_EMB_AVG_VARS = 'emb_avg_vars'\n", "\n", "columns = [COL_FILE_NAME, COL_WORDSIZE, COL_LOGIC_VARS, COL_EMB_MAX_TIME, COL_EMB_MIN_TIME, \n", "           COL_EMB_MEDIAN_TIME, COL_EMB_AVG_TIME, COL_EMB_MAX_CHAIN, \n", "           COL_<PERSON><PERSON>_MIN_CHAIN, COL_EMB_MEDIAN_CHAIN, COL_EMB_AVG_CHAIN,\n", "          COL_E<PERSON>_MAX_VARS, COL_EMB_MIN_VARS, COL_EMB_MEDIAN_VARS, COL_EMB_AVG_VARS]\n", "data_embedding = pd.DataFrame(columns=columns)\n", "def add_embedding_statistics_row(filename, wordsize, topology, logic_vars, times, chains, vars_qpu):\n", "    times.sort()\n", "    chains.sort()\n", "    vars_qpu.sort()\n", "    \n", "    row = {\n", "        COL_FILE_NAME: filename,\n", "        COL_WORDSIZE: wordsize,\n", "        COL_LOGIC_VARS: logic_vars,\n", "        COL_TOPOLOGY: topology,\n", "        \n", "        COL_EMB_MAX_TIME: max(times),\n", "        COL_EMB_MIN_TIME: min(times),\n", "        COL_EMB_MEDIAN_TIME: round(statistics.median(times),2),\n", "        COL_EMB_AVG_TIME: round(sum(times)/len(times),2),\n", "        \n", "        COL_EMB_MAX_CHAIN: max(chains),\n", "        COL_EMB_MIN_CHAIN: min(chains),\n", "        COL_EMB_MEDIAN_CHAIN: round(statistics.median(chains),2),\n", "        COL_EMB_AVG_CHAIN: round(sum(chains)/len(chains),2),\n", "        \n", "        COL_EMB_MAX_VARS: max(vars_qpu),\n", "        COL_EMB_MIN_VARS: min(vars_qpu),\n", "        COL_EMB_MEDIAN_VARS: round(statistics.median(vars_qpu),2),\n", "        COL_EMB_AVG_VARS: round(sum(vars_qpu)/len(vars_qpu),2),\n", "    }\n", "    return data_embedding.append(row, ignore_index=True)\n", "    "]}, {"cell_type": "markdown", "id": "victorian-chicken", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON><PERSON> Graph"]}, {"cell_type": "code", "execution_count": 12, "id": "cultural-queensland", "metadata": {}, "outputs": [], "source": ["chimera_times_32, chimera_chain_length_32, chimera_vars_qpu_32 = get_statistics_embedding(chimera_graph, problem_graph_32, 10)"]}, {"cell_type": "code", "execution_count": 13, "id": "synthetic-spirit", "metadata": {}, "outputs": [], "source": ["data_embedding = add_embedding_statistics_row('u.btor2', 32, 'chimera', num_variables_32, chimera_times_32, chimera_chain_length_32, chimera_vars_qpu_32)"]}, {"cell_type": "code", "execution_count": 14, "id": "higher-custody", "metadata": {}, "outputs": [], "source": ["chimera_times_64, chimera_chain_length_64, chimera_vars_qpu_64 = get_statistics_embedding(chimera_graph, problem_graph_64, 10)"]}, {"cell_type": "code", "execution_count": 15, "id": "medium-crown", "metadata": {}, "outputs": [], "source": ["data_embedding = add_embedding_statistics_row('u.btor2', 64, 'chimera', num_variables_64, chimera_times_64, chimera_chain_length_64, chimera_vars_qpu_64)"]}, {"cell_type": "code", "execution_count": 16, "id": "funded-lewis", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "      <th>wordsize</th>\n", "      <th>logic_vars</th>\n", "      <th>emb_max_time</th>\n", "      <th>emb_min_time</th>\n", "      <th>emb_median_time</th>\n", "      <th>emb_avg_time</th>\n", "      <th>emb_max_chain</th>\n", "      <th>emb_min_chain</th>\n", "      <th>emb_median_chain</th>\n", "      <th>emb_avg_chain</th>\n", "      <th>emb_max_vars</th>\n", "      <th>emb_min_vars</th>\n", "      <th>emb_median_vars</th>\n", "      <th>emb_avg_vars</th>\n", "      <th>topology</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>u.btor2</td>\n", "      <td>32</td>\n", "      <td>348</td>\n", "      <td>23.20</td>\n", "      <td>0.87</td>\n", "      <td>5.41</td>\n", "      <td>8.29</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>2.0</td>\n", "      <td>3.18</td>\n", "      <td>1201</td>\n", "      <td>998</td>\n", "      <td>1110.0</td>\n", "      <td>1106.0</td>\n", "      <td>chimera</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>u.btor2</td>\n", "      <td>64</td>\n", "      <td>398</td>\n", "      <td>26.77</td>\n", "      <td>2.43</td>\n", "      <td>9.45</td>\n", "      <td>12.16</td>\n", "      <td>29</td>\n", "      <td>1</td>\n", "      <td>2.0</td>\n", "      <td>2.83</td>\n", "      <td>1203</td>\n", "      <td>1052</td>\n", "      <td>1126.0</td>\n", "      <td>1124.9</td>\n", "      <td>chimera</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  file_name wordsize logic_vars  emb_max_time  emb_min_time  emb_median_time  \\\n", "0   u.btor2       32        348         23.20          0.87             5.41   \n", "1   u.b<PERSON>2       64        398         26.77          2.43             9.45   \n", "\n", "   emb_avg_time emb_max_chain emb_min_chain  emb_median_chain  emb_avg_chain  \\\n", "0          8.29            31             1               2.0           3.18   \n", "1         12.16            29             1               2.0           2.83   \n", "\n", "  emb_max_vars emb_min_vars  emb_median_vars  emb_avg_vars topology  \n", "0         1201          998           1110.0        1106.0  chimera  \n", "1         1203         1052           1126.0        1124.9  chimera  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["data_embedding"]}, {"cell_type": "markdown", "id": "criminal-investigator", "metadata": {}, "source": ["### Pegasus Graph"]}, {"cell_type": "code", "execution_count": 17, "id": "naval-elimination", "metadata": {}, "outputs": [], "source": ["pegasus_times_32, pegasus_chain_length_32, pegasus_physical_vars_32 = get_statistics_embedding(pegasus_graph, problem_graph_32, 10)\n", "\n"]}, {"cell_type": "code", "execution_count": 18, "id": "assumed-partnership", "metadata": {}, "outputs": [], "source": ["data_embedding = add_embedding_statistics_row('u.btor2', 32, 'pegasus', num_variables_32, pegasus_times_32, pegasus_chain_length_32, pegasus_physical_vars_32)"]}, {"cell_type": "code", "execution_count": 19, "id": "french-registration", "metadata": {}, "outputs": [], "source": ["pegasus_times_64, pegasus_chain_length_64, pegasus_physical_vars_64 = get_statistics_embedding(pegasus_graph, problem_graph_64, 10)\n", "\n"]}, {"cell_type": "code", "execution_count": 20, "id": "handled-robin", "metadata": {}, "outputs": [], "source": ["data_embedding = add_embedding_statistics_row('u.btor2', 64, 'pegasus', num_variables_64, pegasus_times_64, pegasus_chain_length_64, pegasus_physical_vars_64)"]}, {"cell_type": "code", "execution_count": 21, "id": "inner-theorem", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "      <th>wordsize</th>\n", "      <th>logic_vars</th>\n", "      <th>emb_max_time</th>\n", "      <th>emb_min_time</th>\n", "      <th>emb_median_time</th>\n", "      <th>emb_avg_time</th>\n", "      <th>emb_max_chain</th>\n", "      <th>emb_min_chain</th>\n", "      <th>emb_median_chain</th>\n", "      <th>emb_avg_chain</th>\n", "      <th>emb_max_vars</th>\n", "      <th>emb_min_vars</th>\n", "      <th>emb_median_vars</th>\n", "      <th>emb_avg_vars</th>\n", "      <th>topology</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>u.btor2</td>\n", "      <td>32</td>\n", "      <td>348</td>\n", "      <td>23.20</td>\n", "      <td>0.87</td>\n", "      <td>5.41</td>\n", "      <td>8.29</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "      <td>2.0</td>\n", "      <td>3.18</td>\n", "      <td>1201</td>\n", "      <td>998</td>\n", "      <td>1110.0</td>\n", "      <td>1106.0</td>\n", "      <td>chimera</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>u.btor2</td>\n", "      <td>64</td>\n", "      <td>398</td>\n", "      <td>26.77</td>\n", "      <td>2.43</td>\n", "      <td>9.45</td>\n", "      <td>12.16</td>\n", "      <td>29</td>\n", "      <td>1</td>\n", "      <td>2.0</td>\n", "      <td>2.83</td>\n", "      <td>1203</td>\n", "      <td>1052</td>\n", "      <td>1126.0</td>\n", "      <td>1124.9</td>\n", "      <td>chimera</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>u.btor2</td>\n", "      <td>32</td>\n", "      <td>348</td>\n", "      <td>4.44</td>\n", "      <td>1.87</td>\n", "      <td>3.08</td>\n", "      <td>3.12</td>\n", "      <td>11</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "      <td>1.69</td>\n", "      <td>628</td>\n", "      <td>555</td>\n", "      <td>584.5</td>\n", "      <td>588.6</td>\n", "      <td>pegasus</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>u.btor2</td>\n", "      <td>64</td>\n", "      <td>398</td>\n", "      <td>7.82</td>\n", "      <td>3.89</td>\n", "      <td>4.87</td>\n", "      <td>5.29</td>\n", "      <td>12</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "      <td>1.70</td>\n", "      <td>711</td>\n", "      <td>652</td>\n", "      <td>680.5</td>\n", "      <td>677.0</td>\n", "      <td>pegasus</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  file_name wordsize logic_vars  emb_max_time  emb_min_time  emb_median_time  \\\n", "0   u.btor2       32        348         23.20          0.87             5.41   \n", "1   u.b<PERSON>2       64        398         26.77          2.43             9.45   \n", "2   u.btor2       32        348          4.44          1.87             3.08   \n", "3   u.b<PERSON>2       64        398          7.82          3.89             4.87   \n", "\n", "   emb_avg_time emb_max_chain emb_min_chain  emb_median_chain  emb_avg_chain  \\\n", "0          8.29            31             1               2.0           3.18   \n", "1         12.16            29             1               2.0           2.83   \n", "2          3.12            11             1               1.0           1.69   \n", "3          5.29            12             1               1.0           1.70   \n", "\n", "  emb_max_vars emb_min_vars  emb_median_vars  emb_avg_vars topology  \n", "0         1201          998           1110.0        1106.0  chimera  \n", "1         1203         1052           1126.0        1124.9  chimera  \n", "2          628          555            584.5         588.6  pegasus  \n", "3          711          652            680.5         677.0  pegasus  "]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["data_embedding"]}, {"cell_type": "code", "execution_count": 22, "id": "organic-energy", "metadata": {}, "outputs": [], "source": ["data_embedding.to_csv('u_embedding.csv')"]}, {"cell_type": "markdown", "id": "japanese-casting", "metadata": {}, "source": ["# Simulated Annealing"]}, {"cell_type": "code", "execution_count": 5, "id": "informal-orientation", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["40 0.0\n"]}], "source": ["import neal\n", "sampler = neal.SimulatedAnnealingSampler()\n", "simulated_samples_32 = sampler.sample(bqm_32,num_reads=1000, seed=1).lowest()\n", "print(len(simulated_samples_32.lowest()), simulated_samples_32.first.energy)"]}, {"cell_type": "code", "execution_count": 6, "id": "historic-colon", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["29 0.0\n"]}], "source": ["import neal\n", "sampler = neal.SimulatedAnnealingSampler()\n", "simulated_samples_64 = sampler.sample(bqm_64,num_reads=1000, seed=1).lowest()\n", "print(len(simulated_samples_64.lowest()), simulated_samples_64.first.energy)"]}, {"cell_type": "code", "execution_count": 218, "id": "destroyed-colorado", "metadata": {}, "outputs": [], "source": ["for sample in simulated_samples_32.lowest():\n", "    input_dec = 0\n", "    input_variables = [9376, 9377, 9378, 9379, 9380, 9381, 9382, 9383]\n", "    for (index, input_variable) in enumerate(input_variables):\n", "        input_dec += sample[input_variable] * (2**index)\n", "    assert(input_dec > 0)\n", "    \n", "for sample in simulated_samples_64.lowest():\n", "    input_dec = 0\n", "    input_variables = [74187, 74188, 74189, 74190, 74191, 74192, 74193, 74194]\n", "    for (index, input_variable) in enumerate(input_variables):\n", "        input_dec += sample[input_variable] * (2**index)\n", "    assert(input_dec > 0)"]}, {"cell_type": "markdown", "id": "mechanical-behalf", "metadata": {}, "source": ["# Quantum Annealer"]}, {"cell_type": "code", "execution_count": 128, "id": "chief-examination", "metadata": {}, "outputs": [], "source": ["def get_max_chain_strength(bqm):\n", "    return max(bqm.quadratic.values())"]}, {"cell_type": "code", "execution_count": 129, "id": "recreational-sheep", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4.0\n", "4.0\n"]}], "source": ["max_chain_strength_32 = get_max_chain_strength(bqm_32)\n", "max_chain_strength_64 = get_max_chain_strength(bqm_64)\n", "print(max_chain_strength_32)\n", "print(max_chain_strength_64)"]}, {"cell_type": "code", "execution_count": null, "id": "buried-porter", "metadata": {}, "outputs": [], "source": ["chain_strengths_to_try = [1,2]\n", "samples_numbers_to_try = [1000, 5000, 7000, 10000]"]}, {"cell_type": "code", "execution_count": 181, "id": "supported-vancouver", "metadata": {}, "outputs": [], "source": ["from dwave.system import DWaveSampler, EmbeddingComposite\n", "from greedy import SteepestDescentComposite\n", "DWAVE_2000 = \"DW_2000Q_6\"\n", "ADVANTAGE = \"Advantage_system4.1\""]}, {"cell_type": "code", "execution_count": 182, "id": "adequate-equilibrium", "metadata": {}, "outputs": [], "source": ["def sample_qpu(bqm, qpu_name, num_runs, chain_strength):\n", "    random.seed(1)\n", "    embedding_seed = random.randint(0, 10000)\n", "    qpu = EmbeddingComposite(DWaveSampler(solver={\"name\": qpu_name}), embedding_parameters= {'random_seed':embedding_seed})\n", "    sampler = SteepestDescentComposite(qpu)\n", "    result = sampler.sample(bqm, num_reads=num_runs, chain_strength=chain_strength)\n", "    return result\n", "\n", "def get_energy_0_count(samples):\n", "    answer = 0\n", "    for sample in samples.lowest().record:\n", "        energy = sample[1]\n", "        occs = sample[2]\n", "        if energy > 0:\n", "            break\n", "        answer += occs\n", "    return answer\n", "\n", "def get_good_answers_overall(samples, unicorn_files_path, input_variables):\n", "    good_answers_overall = 0\n", "    for (index, sample) in enumerate(samples):\n", "        occs = samples.record[index][2]\n", "        input_dec = 0\n", "        for (index,input_variable) in enumerate(input_variables):\n", "            input_dec += sample[input_variable] * (2**index)\n", "\n", "        energy, bad_states = InputChecker.run_checker(f'./{unicorn_files_path}/', input_dec)\n", "        if energy == 0:\n", "            good_answers_overall += occs\n", "    return good_answers_overall\n", "\n", "def get_wrong_var_counts(bqm, samples, unicorn_files_path, input_variables):\n", "    wrong_var_counts = []\n", "    for (index, sample) in enumerate(samples):\n", "        occs = samples.record[index][2]\n", "        input_dec = 0\n", "        \n", "        for (index, input_variable) in enumerate(input_variables):\n", "            input_dec += sample[input_variable] * (2**index)\n", "        energy, bad_states = InputChecker.run_checker(f'./{unicorn_files_path}/', input_dec)\n", "\n", "        wrong_var_count = 0\n", "        for var in bqm.variables:\n", "            if InputChecker.qubits_to_fix[var] != sample[var]:\n", "                wrong_var_count += 1\n", "        for i in range(occs):\n", "            wrong_var_counts.append(wrong_var_count)\n", "    return wrong_var_counts\n", "\n", "def get_energies(samples):\n", "    energies = []\n", "    for sample in samples.record:\n", "        for i in range(sample[2]):\n", "            energies.append(sample[1])\n", "    return energies"]}, {"cell_type": "code", "execution_count": 183, "id": "flying-developer", "metadata": {}, "outputs": [], "source": ["COL_FILE_NAME = 'file_name'\n", "COL_WORDSIZE = 'wordsize'\n", "COL_LOGIC_VARS = 'logic_vars'\n", "COL_QPU_NAME = 'qpu_name'\n", "COL_CHAIN_STRENGTH = 'chain_strength'\n", "COL_NUM_SAMPLES = 'samples'\n", "COL_TIME = 'time'\n", "\n", "COL_AVG_ENERGY = 'avg_energy'\n", "COL_MEDIAN_ENERGY = 'median_energy'\n", "COL_MIN_ENERGY = 'min_energy'\n", "COL_MAX_ENERGY = 'max_energy'\n", "\n", "COL_COUNT_0_ENERGY = \"count_0_energy\"\n", "COL_GOOD_ANSWERS = \"good_answers\"\n", "\n", "COL_AVG_WRONG_VARS = 'avg_wrong_vars'\n", "COL_MEDIAN_WRONG_VARS = 'median_wrong_vars'\n", "COL_MIN_WRONG_VARS = 'min_wrong_vars'\n", "COL_MAX_WRONG_VARS = 'max_wrong_vars'\n", "\n", "columns_data_annealer = [COL_FILE_NAME, COL_WORDSIZE, COL_LOGIC_VARS, COL_QPU_NAME, \n", "                         COL_CHAIN_STRENGTH, COL_NUM_SAMPLES, COL_TIME, COL_AVG_ENERGY, \n", "                         COL_ME<PERSON>AN_ENERGY, COL_MIN_ENERGY, COL_MAX_ENERGY, COL_COUNT_0_ENERGY, \n", "                         COL_GOOD_ANSWERS, COL_AVG_WRONG_VARS, COL_MEDIAN_WRONG_VARS, \n", "                         COL_MI<PERSON>_WRONG_VARS, COL_MAX_WRONG_VARS]\n", "\n", "data_annealer = pd.DataFrame(columns=columns_data_annealer)\n", "\n", "def add_row_data_annealer(filename, wordsize, logic_vars, qpu_name, chain_strength, num_samples, \n", "                          time, energies, count_0_energy, good_answers, wrong_vars):\n", "    row = {\n", "        COL_FILE_NAME: filename,\n", "        COL_WORDSIZE: wordsize,\n", "        COL_LOGIC_VARS: logic_vars,\n", "        COL_QPU_NAME: qpu_name,\n", "        COL_CHAIN_STRENGTH: chain_strength,\n", "        COL_NUM_SAMPLES: num_samples,\n", "        COL_TIME: time,\n", "        COL_AVG_ENERGY: round(sum(energies)/len(energies),2),\n", "        COL_MEDIAN_ENERGY: statistics.median(energies),\n", "        COL_MIN_ENERGY: min(energies),\n", "        COL_MAX_ENERGY: max(energies),\n", "        COL_COUNT_0_ENERGY: count_0_energy,\n", "        COL_GOOD_ANSWERS: good_answers,\n", "        COL_AVG_WRONG_VARS: round(sum(wrong_vars)/len(wrong_vars),2),\n", "        COL_MEDIAN_WRONG_VARS: statistics.median(wrong_vars),\n", "        COL_MIN_WRONG_VARS: min(wrong_vars),\n", "        COL_MAX_WRONG_VARS: max(wrong_vars)\n", "    }\n", "    return data_annealer.append(row, ignore_index=True)"]}, {"cell_type": "code", "execution_count": 186, "id": "least-shuttle", "metadata": {}, "outputs": [], "source": ["def get_qpu_statistics(filename, wordsize, bqm, qpu_name, unicorn_files_path, input_variables):\n", "    global data_annealer\n", "    logic_vars = len(bqm.adj.keys())\n", "    for chain_strength in chain_strengths_to_try:\n", "        for num_samples in samples_numbers_to_try:\n", "            print(chain_strength, num_samples)\n", "            t0 = time.perf_counter()\n", "            samples = sample_qpu(bqm, qpu_name, num_samples, chain_strength)\n", "            t1 = time.perf_counter()\n", "            sampling_time = round(t1-t0, 2)\n", "            energies = get_energies(samples)\n", "            count_0_energy = get_energy_0_count(samples)\n", "            good_answers = get_good_answers_overall(samples, unicorn_files_path, input_variables)\n", "            wrong_vars = get_wrong_var_counts(bqm, samples, unicorn_files_path, input_variables)\n", "            data_annealer = add_row_data_annealer(filename, wordsize, logic_vars, qpu_name, chain_strength, \n", "                                                  num_samples, sampling_time, energies, count_0_energy, \n", "                                                  good_answers, wrong_vars)"]}, {"cell_type": "code", "execution_count": 229, "id": "becoming-drilling", "metadata": {}, "outputs": [], "source": ["get_qpu_statistics('u.c', 32, bqm_32, ADVANTAGE, '32_u', [9376, 9377, 9378, 9379, 9380, 9381, 9382, 9383])"]}, {"cell_type": "code", "execution_count": 176, "id": "working-conviction", "metadata": {}, "outputs": [], "source": ["get_qpu_statistics('u.c', 32, bqm_32, DWAVE_2000, '32_u', [9376, 9377, 9378, 9379, 9380, 9381, 9382, 9383])"]}, {"cell_type": "code", "execution_count": 188, "id": "egyptian-boards", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>file_name</th>\n", "      <th>wordsize</th>\n", "      <th>logic_vars</th>\n", "      <th>qpu_name</th>\n", "      <th>chain_strength</th>\n", "      <th>samples</th>\n", "      <th>time</th>\n", "      <th>avg_energy</th>\n", "      <th>median_energy</th>\n", "      <th>min_energy</th>\n", "      <th>max_energy</th>\n", "      <th>count_0_energy</th>\n", "      <th>good_answers</th>\n", "      <th>avg_wrong_vars</th>\n", "      <th>median_wrong_vars</th>\n", "      <th>min_wrong_vars</th>\n", "      <th>max_wrong_vars</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>u.c</td>\n", "      <td>32</td>\n", "      <td>348</td>\n", "      <td>Advantage_system4.1</td>\n", "      <td>1</td>\n", "      <td>1000</td>\n", "      <td>13.78</td>\n", "      <td>21.81</td>\n", "      <td>22.0</td>\n", "      <td>4.0</td>\n", "      <td>42.0</td>\n", "      <td>0</td>\n", "      <td>872</td>\n", "      <td>71.15</td>\n", "      <td>70.0</td>\n", "      <td>7</td>\n", "      <td>131</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>u.c</td>\n", "      <td>32</td>\n", "      <td>348</td>\n", "      <td>Advantage_system4.1</td>\n", "      <td>1</td>\n", "      <td>1000</td>\n", "      <td>14.09</td>\n", "      <td>21.42</td>\n", "      <td>22.0</td>\n", "      <td>4.0</td>\n", "      <td>40.0</td>\n", "      <td>0</td>\n", "      <td>907</td>\n", "      <td>68.02</td>\n", "      <td>67.0</td>\n", "      <td>8</td>\n", "      <td>149</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>u.c</td>\n", "      <td>32</td>\n", "      <td>348</td>\n", "      <td>Advantage_system4.1</td>\n", "      <td>1</td>\n", "      <td>5000</td>\n", "      <td>20.55</td>\n", "      <td>21.15</td>\n", "      <td>20.0</td>\n", "      <td>4.0</td>\n", "      <td>46.0</td>\n", "      <td>0</td>\n", "      <td>4276</td>\n", "      <td>70.64</td>\n", "      <td>71.0</td>\n", "      <td>7</td>\n", "      <td>146</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>u.c</td>\n", "      <td>32</td>\n", "      <td>348</td>\n", "      <td>Advantage_system4.1</td>\n", "      <td>1</td>\n", "      <td>7000</td>\n", "      <td>19.90</td>\n", "      <td>20.40</td>\n", "      <td>20.0</td>\n", "      <td>4.0</td>\n", "      <td>46.0</td>\n", "      <td>0</td>\n", "      <td>6479</td>\n", "      <td>65.04</td>\n", "      <td>64.0</td>\n", "      <td>7</td>\n", "      <td>162</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>u.c</td>\n", "      <td>32</td>\n", "      <td>348</td>\n", "      <td>Advantage_system4.1</td>\n", "      <td>1</td>\n", "      <td>10000</td>\n", "      <td>28.17</td>\n", "      <td>20.96</td>\n", "      <td>20.0</td>\n", "      <td>4.0</td>\n", "      <td>46.0</td>\n", "      <td>0</td>\n", "      <td>8389</td>\n", "      <td>70.72</td>\n", "      <td>71.0</td>\n", "      <td>7</td>\n", "      <td>150</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>u.c</td>\n", "      <td>32</td>\n", "      <td>348</td>\n", "      <td>Advantage_system4.1</td>\n", "      <td>2</td>\n", "      <td>1000</td>\n", "      <td>17.77</td>\n", "      <td>18.77</td>\n", "      <td>18.0</td>\n", "      <td>2.0</td>\n", "      <td>34.0</td>\n", "      <td>0</td>\n", "      <td>962</td>\n", "      <td>72.51</td>\n", "      <td>72.0</td>\n", "      <td>5</td>\n", "      <td>160</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>u.c</td>\n", "      <td>32</td>\n", "      <td>348</td>\n", "      <td>Advantage_system4.1</td>\n", "      <td>2</td>\n", "      <td>5000</td>\n", "      <td>24.40</td>\n", "      <td>18.03</td>\n", "      <td>18.0</td>\n", "      <td>2.0</td>\n", "      <td>44.0</td>\n", "      <td>0</td>\n", "      <td>4895</td>\n", "      <td>66.09</td>\n", "      <td>65.0</td>\n", "      <td>2</td>\n", "      <td>166</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>u.c</td>\n", "      <td>32</td>\n", "      <td>348</td>\n", "      <td>Advantage_system4.1</td>\n", "      <td>2</td>\n", "      <td>7000</td>\n", "      <td>18.22</td>\n", "      <td>19.98</td>\n", "      <td>20.0</td>\n", "      <td>2.0</td>\n", "      <td>50.0</td>\n", "      <td>0</td>\n", "      <td>6875</td>\n", "      <td>70.68</td>\n", "      <td>70.0</td>\n", "      <td>4</td>\n", "      <td>182</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>u.c</td>\n", "      <td>32</td>\n", "      <td>348</td>\n", "      <td>Advantage_system4.1</td>\n", "      <td>2</td>\n", "      <td>10000</td>\n", "      <td>23.83</td>\n", "      <td>20.76</td>\n", "      <td>20.0</td>\n", "      <td>2.0</td>\n", "      <td>46.0</td>\n", "      <td>0</td>\n", "      <td>9675</td>\n", "      <td>71.33</td>\n", "      <td>70.5</td>\n", "      <td>3</td>\n", "      <td>173</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  file_name wordsize logic_vars             qpu_name chain_strength samples  \\\n", "0       u.c       32        348  Advantage_system4.1              1    1000   \n", "1       u.c       32        348  Advantage_system4.1              1    1000   \n", "2       u.c       32        348  Advantage_system4.1              1    5000   \n", "3       u.c       32        348  Advantage_system4.1              1    7000   \n", "4       u.c       32        348  Advantage_system4.1              1   10000   \n", "5       u.c       32        348  Advantage_system4.1              2    1000   \n", "6       u.c       32        348  Advantage_system4.1              2    5000   \n", "7       u.c       32        348  Advantage_system4.1              2    7000   \n", "8       u.c       32        348  Advantage_system4.1              2   10000   \n", "\n", "    time  avg_energy  median_energy  min_energy  max_energy count_0_energy  \\\n", "0  13.78       21.81           22.0         4.0        42.0              0   \n", "1  14.09       21.42           22.0         4.0        40.0              0   \n", "2  20.55       21.15           20.0         4.0        46.0              0   \n", "3  19.90       20.40           20.0         4.0        46.0              0   \n", "4  28.17       20.96           20.0         4.0        46.0              0   \n", "5  17.77       18.77           18.0         2.0        34.0              0   \n", "6  24.40       18.03           18.0         2.0        44.0              0   \n", "7  18.22       19.98           20.0         2.0        50.0              0   \n", "8  23.83       20.76           20.0         2.0        46.0              0   \n", "\n", "  good_answers  avg_wrong_vars  median_wrong_vars min_wrong_vars  \\\n", "0          872           71.15               70.0              7   \n", "1          907           68.02               67.0              8   \n", "2         4276           70.64               71.0              7   \n", "3         6479           65.04               64.0              7   \n", "4         8389           70.72               71.0              7   \n", "5          962           72.51               72.0              5   \n", "6         4895           66.09               65.0              2   \n", "7         6875           70.68               70.0              4   \n", "8         9675           71.33               70.5              3   \n", "\n", "  max_wrong_vars  \n", "0            131  \n", "1            149  \n", "2            146  \n", "3            162  \n", "4            150  \n", "5            160  \n", "6            166  \n", "7            182  \n", "8            173  "]}, "execution_count": 188, "metadata": {}, "output_type": "execute_result"}], "source": ["data_annealer"]}, {"cell_type": "code", "execution_count": null, "id": "earned-description", "metadata": {}, "outputs": [], "source": ["get_qpu_statistics('u.c', 64, bqm_64, DWAVE_2000, '64_u', [74187, 74188, 74189, 74190, 74191, 74192, 74193, 74194])"]}, {"cell_type": "markdown", "id": "differential-institution", "metadata": {}, "source": ["# Advantage"]}, {"cell_type": "markdown", "id": "composed-shark", "metadata": {}, "source": ["## 32-bit, 7k reads, 1.5 chain"]}, {"cell_type": "code", "execution_count": 219, "id": "average-seafood", "metadata": {}, "outputs": [], "source": ["qpu = EmbeddingComposite(DWaveSampler(solver={\"name\": ADVANTAGE}))\n", "sampler = SteepestDescentComposite(qpu)\n", "result = sampler.sample(bqm_32, num_reads=7000, chain_strength=1.5)"]}, {"cell_type": "code", "execution_count": 221, "id": "chronic-miller", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.0"]}, "execution_count": 221, "metadata": {}, "output_type": "execute_result"}], "source": ["result.first.energy"]}, {"cell_type": "code", "execution_count": 223, "id": "coordinate-invalid", "metadata": {}, "outputs": [{"data": {"text/plain": ["12"]}, "execution_count": 223, "metadata": {}, "output_type": "execute_result"}], "source": ["len(result.lowest())"]}, {"cell_type": "markdown", "id": "potential-portfolio", "metadata": {}, "source": ["## 32-bit, 7k reads, 1 chain"]}, {"cell_type": "code", "execution_count": 224, "id": "mighty-buyer", "metadata": {}, "outputs": [], "source": ["qpu = EmbeddingComposite(DWaveSampler(solver={\"name\": ADVANTAGE}))\n", "sampler = SteepestDescentComposite(qpu)\n", "result = sampler.sample(bqm_32, num_reads=7000, chain_strength=1)"]}, {"cell_type": "code", "execution_count": 225, "id": "august-cruise", "metadata": {}, "outputs": [{"data": {"text/plain": ["6.0"]}, "execution_count": 225, "metadata": {}, "output_type": "execute_result"}], "source": ["result.first.energy"]}, {"cell_type": "markdown", "id": "south-trick", "metadata": {}, "source": ["## 32-bit, 10k reads, 1.5 chain"]}, {"cell_type": "code", "execution_count": 226, "id": "creative-culture", "metadata": {}, "outputs": [], "source": ["qpu = EmbeddingComposite(DWaveSampler(solver={\"name\": ADVANTAGE}))\n", "sampler = SteepestDescentComposite(qpu)\n", "result = sampler.sample(bqm_32, num_reads=10000, chain_strength=1.5)"]}, {"cell_type": "code", "execution_count": 227, "id": "public-jackson", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.0"]}, "execution_count": 227, "metadata": {}, "output_type": "execute_result"}], "source": ["result.first.energy"]}, {"cell_type": "code", "execution_count": 228, "id": "verified-district", "metadata": {}, "outputs": [{"data": {"text/plain": ["9"]}, "execution_count": 228, "metadata": {}, "output_type": "execute_result"}], "source": ["len(result.lowest())"]}, {"cell_type": "markdown", "id": "grand-candle", "metadata": {}, "source": ["## 64-bit, 7k reads, 1.5 chain"]}, {"cell_type": "code", "execution_count": 236, "id": "affiliated-fellow", "metadata": {}, "outputs": [], "source": ["t0 = time.perf_counter()\n", "qpu = EmbeddingComposite(DWaveSampler(solver={\"name\": ADVANTAGE}))\n", "sampler = SteepestDescentComposite(qpu)\n", "result = sampler.sample(bqm_64, num_reads=7000, chain_strength=1.5)\n", "t1 = time.perf_counter()"]}, {"cell_type": "code", "execution_count": 237, "id": "specialized-bargain", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["16.03 0.0 1\n"]}], "source": ["print(round(t1-t0,2), result.first.energy, len(result.lowest()))"]}, {"cell_type": "markdown", "id": "absent-andrew", "metadata": {}, "source": ["## 64-bit, 10k reads, 1.5 chain"]}, {"cell_type": "code", "execution_count": 238, "id": "informed-scheme", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["16.41 0.0 1\n"]}], "source": ["t0 = time.perf_counter()\n", "qpu = EmbeddingComposite(DWaveSampler(solver={\"name\": ADVANTAGE}))\n", "sampler = SteepestDescentComposite(qpu)\n", "result = sampler.sample(bqm_64, num_reads=10000, chain_strength=1.5)\n", "t1 = time.perf_counter()\n", "print(round(t1-t0,2), result.first.energy, len(result.lowest()))"]}, {"cell_type": "markdown", "id": "sustainable-foster", "metadata": {}, "source": ["# Chimera"]}, {"cell_type": "code", "execution_count": 239, "id": "eastern-tribe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["14.12 2.0 1\n"]}], "source": ["t0 = time.perf_counter()\n", "qpu = EmbeddingComposite(DWaveSampler(solver={\"name\": DWAVE_2000}))\n", "sampler = SteepestDescentComposite(qpu)\n", "result = sampler.sample(bqm_32, num_reads=7000, chain_strength=1.5)\n", "t1 = time.perf_counter()\n", "print(round(t1-t0,2), result.first.energy, len(result.lowest()))"]}, {"cell_type": "code", "execution_count": 240, "id": "duplicate-encyclopedia", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["22.82 2.0 5\n"]}], "source": ["t0 = time.perf_counter()\n", "qpu = EmbeddingComposite(DWaveSampler(solver={\"name\": DWAVE_2000}))\n", "sampler = SteepestDescentComposite(qpu)\n", "result = sampler.sample(bqm_32, num_reads=7000, chain_strength=2)\n", "t1 = time.perf_counter()\n", "print(round(t1-t0,2), result.first.energy, len(result.lowest()))"]}, {"cell_type": "code", "execution_count": 243, "id": "welcome-dietary", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["25.18 4.0 5\n"]}], "source": ["t0 = time.perf_counter()\n", "qpu = EmbeddingComposite(DWaveSampler(solver={\"name\": DWAVE_2000}))\n", "sampler = SteepestDescentComposite(qpu)\n", "result = sampler.sample(bqm_32, num_reads=7000, chain_strength=2.5)\n", "t1 = time.perf_counter()\n", "print(round(t1-t0,2), result.first.energy, len(result.lowest()))"]}, {"cell_type": "code", "execution_count": 244, "id": "going-bones", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["25.54 2.0 5\n"]}], "source": ["t0 = time.perf_counter()\n", "qpu = EmbeddingComposite(DWaveSampler(solver={\"name\": DWAVE_2000}))\n", "sampler = SteepestDescentComposite(qpu)\n", "result = sampler.sample(bqm_32, num_reads=10000, chain_strength=1.5)\n", "t1 = time.perf_counter()\n", "print(round(t1-t0,2), result.first.energy, len(result.lowest()))"]}, {"cell_type": "code", "execution_count": 245, "id": "advisory-specific", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["18.64 2.0 1\n"]}], "source": ["t0 = time.perf_counter()\n", "qpu = EmbeddingComposite(DWaveSampler(solver={\"name\": DWAVE_2000}))\n", "sampler = SteepestDescentComposite(qpu)\n", "result = sampler.sample(bqm_64, num_reads=10000, chain_strength=1.5)\n", "t1 = time.perf_counter()\n", "print(round(t1-t0,2), result.first.energy, len(result.lowest()))"]}, {"cell_type": "code", "execution_count": null, "id": "excellent-lodge", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.9"}}, "nbformat": 4, "nbformat_minor": 5}