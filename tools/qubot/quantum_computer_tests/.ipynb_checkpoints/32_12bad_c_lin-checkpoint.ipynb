{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "from dimod import BinaryQuadraticModel\n", "from dwave.system import LeapHybridSampler\n", "from bqm_input_checker import InputChecker\n", "import dimod"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["path = '../32_bit_results/32_12bad_c_lin/'"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["with open(f\"{path}context.json\") as context_file:\n", "    context = json.load(context_file)\n", "    offset = context[\"offset\"]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["InputChecker.process_coo('../32_bit_results/32_12bad_c_lin/adj.coo')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["bqm = BinaryQuadraticModel(InputChecker.linear, InputChecker.quadratic, dimod.BINARY)\n", "bqm.offset = offset"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["sampler = LeapHybridSampler(solver={\"name\": \"hybrid_binary_quadratic_model_version2\"}) "]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Documents/prague/matriarcher/myenv/lib/python3.9/site-packages/dwave/system/samplers/leap_hybrid_sampler.py:264: UserWarning: bqm.iter_variables() is deprecated since dimod 0.10.0, use iter(bqm.variables) instead.\n", "  mapping = dict(enumerate(bqm.iter_variables()))\n"]}], "source": ["sampleset = sampler.sample(bqm)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 1}